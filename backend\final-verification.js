const mongoose = require("mongoose");
require("dotenv").config();

async function finalVerification() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log("✅ Connected to MongoDB");

    const Exam = require("./models/examModel");
    const Question = require("./models/questionModel");
    const { AIQuestionGeneration } = require("./models/aiQuestionGenerationModel");

    console.log("🔍 FINAL VERIFICATION: AI-Generated Exam Visibility Fix");
    console.log("=" * 60);

    // 1. Check AI-generated exams with questions
    const aiExamsWithQuestions = await Exam.find({
      name: { $regex: /^[PSA]\d+[A-Z]{1,2}-[A-Z0-9]{2}$/ },
      questions: { $exists: true, $not: { $size: 0 } }
    }).populate('questions');

    console.log(`\n1️⃣ AI-Generated Exams with Questions: ${aiExamsWithQuestions.length}`);
    for (const exam of aiExamsWithQuestions) {
      const aiQuestions = await Question.find({ 
        _id: { $in: exam.questions },
        isAIGenerated: true 
      });
      console.log(`   ✅ ${exam.name} - ${exam.questions.length} questions (${aiQuestions.length} AI-generated)`);
    }

    // 2. Check remaining standalone generations
    const standaloneGenerations = await AIQuestionGeneration.find({
      $or: [
        { examId: null },
        { examId: { $exists: false } }
      ]
    });

    console.log(`\n2️⃣ Remaining Standalone Generations: ${standaloneGenerations.length}`);
    if (standaloneGenerations.length > 0) {
      console.log("   ⚠️ These need to be processed through the approval system:");
      standaloneGenerations.forEach(gen => {
        console.log(`   - ${gen._id}: ${gen.generatedQuestions.length} questions (${gen.generationStatus})`);
      });
    } else {
      console.log("   ✅ No standalone generations remaining!");
    }

    // 3. Check AI-generated exams without questions
    const aiExamsWithoutQuestions = await Exam.find({
      name: { $regex: /^[PSA]\d+[A-Z]{1,2}-[A-Z0-9]{2}$/ },
      $or: [
        { questions: { $size: 0 } },
        { questions: { $exists: false } }
      ]
    });

    console.log(`\n3️⃣ AI-Generated Exams without Questions: ${aiExamsWithoutQuestions.length}`);
    if (aiExamsWithoutQuestions.length > 0) {
      console.log("   ⚠️ These exams won't appear in quiz listings:");
      aiExamsWithoutQuestions.forEach(exam => {
        console.log(`   - ${exam.name} (${exam.level} Class ${exam.class})`);
      });
    } else {
      console.log("   ✅ All AI-generated exams have questions!");
    }

    // 4. Test quiz listing simulation for each level
    const levels = ["Primary", "Secondary", "Advance"];
    console.log(`\n4️⃣ Quiz Listing Simulation:`);
    
    for (const level of levels) {
      const exams = await Exam.find({ level }).populate('questions');
      const examsWithQuestions = exams.filter(exam => exam.questions && exam.questions.length > 0);
      const aiExamsVisible = examsWithQuestions.filter(exam => 
        exam.name.match(/^[PSA]\d+[A-Z]{1,2}-[A-Z0-9]{2}$/)
      );
      
      console.log(`   ${level}: ${examsWithQuestions.length} total exams, ${aiExamsVisible.length} AI-generated visible`);
    }

    // 5. Summary
    console.log(`\n5️⃣ SUMMARY:`);
    console.log(`   ✅ Auto-exam creation: IMPLEMENTED`);
    console.log(`   ✅ Legacy standalone handling: IMPLEMENTED`);
    console.log(`   ✅ AI exams in quiz listings: ${aiExamsWithQuestions.length > 0 ? 'WORKING' : 'NEEDS ATTENTION'}`);
    
    if (aiExamsWithQuestions.length > 0) {
      console.log(`\n🎉 SUCCESS: AI-generated exams are now appearing in quiz listings!`);
      console.log(`   Users can see and take ${aiExamsWithQuestions.length} AI-generated exam(s).`);
    } else {
      console.log(`\n⚠️ ATTENTION: No AI-generated exams with questions found.`);
      console.log(`   This might be because questions haven't been approved yet.`);
    }

    await mongoose.disconnect();
    console.log("\n✅ Verification completed");

  } catch (error) {
    console.error("❌ Verification failed:", error);
    await mongoose.disconnect();
  }
}

finalVerification();
