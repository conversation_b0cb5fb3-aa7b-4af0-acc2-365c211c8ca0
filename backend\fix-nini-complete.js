const mongoose = require('mongoose');
require('dotenv').config();

async function fixNiniComplete() {
  try {
    console.log('🔧 Complete fix for nini.nini subscription...\n');

    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    // Define schemas
    const subscriptionSchema = new mongoose.Schema({}, { strict: false, strictPopulate: false });
    const userSchema = new mongoose.Schema({}, { strict: false });
    const planSchema = new mongoose.Schema({}, { strict: false });

    const Subscription = mongoose.model('Subscription', subscriptionSchema);
    const User = mongoose.model('User', userSchema);
    const Plan = mongoose.model('Plan', planSchema);

    // Find nini.nini user
    const niniUser = await User.findOne({ username: 'nini.nini' });
    if (!niniUser) {
      console.log('❌ User nini.nini not found');
      return;
    }

    console.log('👤 Found user nini.nini');

    // Find a standard plan to assign
    const standardPlan = await Plan.findOne({ title: /standard/i }) || await Plan.findOne({});
    if (!standardPlan) {
      console.log('❌ No plans found in database');
      return;
    }

    console.log(`📦 Using plan: ${standardPlan.title} (${standardPlan.duration} months)`);

    // Find nini's latest subscription
    const latestSubscription = await Subscription.findOne({ user: niniUser._id })
      .sort({ createdAt: -1 });

    if (!latestSubscription) {
      console.log('❌ No subscription found for nini.nini');
      return;
    }

    console.log(`📋 Found subscription: ${latestSubscription._id}`);

    // Calculate subscription dates
    const startDate = new Date();
    const endDate = new Date();
    const planDuration = standardPlan.duration || 6;
    endDate.setMonth(endDate.getMonth() + planDuration);

    const formattedStartDate = startDate.toISOString().split('T')[0];
    const formattedEndDate = endDate.toISOString().split('T')[0];

    console.log(`📅 Subscription period: ${formattedStartDate} to ${formattedEndDate}`);

    // Update subscription with direct database operation
    const subscriptionUpdate = await Subscription.updateOne(
      { _id: latestSubscription._id },
      {
        $set: {
          activePlan: standardPlan._id,
          paymentStatus: 'paid',
          status: 'active',
          startDate: formattedStartDate,
          endDate: formattedEndDate,
          updatedAt: new Date()
        }
      }
    );

    console.log(`✅ Updated subscription (${subscriptionUpdate.modifiedCount} document)`);

    // Update user status with direct database operation
    const userUpdate = await User.updateOne(
      { _id: niniUser._id },
      {
        $set: {
          subscriptionStatus: 'active',
          paymentRequired: false,
          subscriptionStartDate: startDate,
          subscriptionEndDate: endDate,
          updatedAt: new Date()
        }
      }
    );

    console.log(`✅ Updated user (${userUpdate.modifiedCount} document)`);

    // Verify the changes
    console.log('\n🎯 VERIFICATION:');
    console.log('=' .repeat(40));

    // Check subscription
    const verifySubscription = await Subscription.findById(latestSubscription._id).populate('activePlan');
    console.log('📋 Subscription Status:');
    console.log(`   📊 Status: ${verifySubscription.status}`);
    console.log(`   💳 Payment Status: ${verifySubscription.paymentStatus}`);
    console.log(`   📅 Start Date: ${verifySubscription.startDate}`);
    console.log(`   📅 End Date: ${verifySubscription.endDate}`);
    console.log(`   📦 Plan: ${verifySubscription.activePlan?.title}`);

    // Check user
    const verifyUser = await User.findById(niniUser._id);
    console.log('\n👤 User Status:');
    console.log(`   📊 Subscription Status: ${verifyUser.subscriptionStatus}`);
    console.log(`   💳 Payment Required: ${verifyUser.paymentRequired}`);
    console.log(`   📅 Subscription End Date: ${verifyUser.subscriptionEndDate}`);

    // Test the API endpoint
    console.log('\n🧪 Testing API endpoint...');
    try {
      const axios = require('axios');
      
      // Create a temporary token for testing
      const jwt = require('jsonwebtoken');
      const token = jwt.sign(
        { userId: niniUser._id },
        process.env.JWT_SECRET || 'fallback-secret',
        { expiresIn: '1h' }
      );

      const response = await axios.get('http://localhost:5000/api/payment/check-payment-status', {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });

      if (response.data.success) {
        console.log('✅ API test successful!');
        console.log(`   📊 Status: ${response.data.status}`);
        console.log(`   💳 Payment Status: ${response.data.paymentStatus}`);
        console.log(`   📦 Plan: ${response.data.planTitle}`);
      } else {
        console.log('⚠️ API test failed:', response.data);
      }

    } catch (apiError) {
      console.log('⚠️ API test error:', apiError.message);
    }

    console.log('\n🎉 nini.nini subscription completely fixed!');
    console.log('💡 User should now see active subscription status in real-time');
    console.log('🔄 User should refresh the subscription page to see changes');

  } catch (error) {
    console.error('❌ Error fixing nini.nini subscription:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from database');
  }
}

fixNiniComplete();
