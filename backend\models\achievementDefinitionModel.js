const mongoose = require("mongoose");

const achievementDefinitionSchema = new mongoose.Schema(
  {
    // Achievement Identity
    id: {
      type: String,
      required: true,
      unique: true,
    },
    name: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    
    // Visual Elements
    icon: {
      type: String,
      required: true,
    },
    color: {
      primary: String,
      secondary: String,
      gradient: [String],
    },
    animation: {
      type: String,
      enum: ["none", "pulse", "glow", "bounce", "shake", "rotate"],
      default: "none",
    },
    
    // Achievement Properties
    category: {
      type: String,
      enum: ["learning", "streak", "subject", "social", "special", "seasonal"],
      required: true,
    },
    rarity: {
      type: String,
      enum: ["common", "uncommon", "rare", "epic", "legendary", "mythic"],
      default: "common",
    },
    
    // Rewards
    xpReward: {
      type: Number,
      default: 0,
    },
    additionalRewards: [{
      type: {
        type: String,
        enum: ["badge", "title", "theme", "multiplier", "unlock"],
      },
      value: String,
      duration: Number, // in days, 0 for permanent
    }],
    
    // Requirements
    requirements: {
      // Quiz-based requirements
      quizzes: {
        total: Number,
        subject: String,
        difficulty: String,
        minScore: Number,
        consecutive: Boolean,
        timeframe: Number, // in days
      },
      
      // Streak requirements
      streak: {
        type: {
          type: String,
          enum: ["daily", "weekly", "quiz", "subject"],
        },
        count: Number,
        subject: String,
      },
      
      // Performance requirements
      performance: {
        perfectScores: Number,
        averageScore: Number,
        improvementRate: Number,
        speedBonus: Number,
      },
      
      // Social requirements
      social: {
        helpOthers: Number,
        mentorStudents: Number,
        groupActivities: Number,
      },
      
      // Level requirements
      level: {
        minimum: Number,
        maximum: Number,
      },
      
      // Time-based requirements
      time: {
        studyHours: Number,
        activeDays: Number,
        registrationAge: Number, // days since registration
      },
      
      // Special requirements
      special: {
        firstInClass: Boolean,
        topPercentile: Number,
        seasonalEvent: String,
        customCondition: String,
      },
    },
    
    // Tracking Information
    tracking: {
      isRepeatable: {
        type: Boolean,
        default: false,
      },
      cooldownPeriod: Number, // in days
      maxEarns: Number, // maximum times this can be earned
      trackingFields: [String], // fields to track for progress
    },
    
    // Visibility and Availability
    isActive: {
      type: Boolean,
      default: true,
    },
    isHidden: {
      type: Boolean,
      default: false,
    },
    isSecret: {
      type: Boolean,
      default: false,
    },
    
    // Seasonal/Event Information
    seasonal: {
      isSeasonalAchievement: {
        type: Boolean,
        default: false,
      },
      availableSeasons: [String],
      eventId: String,
      startDate: Date,
      endDate: Date,
    },
    
    // Prerequisites
    prerequisites: {
      achievements: [String], // Required achievement IDs
      level: Number,
      subjects: [String],
    },
    
    // Statistics
    stats: {
      totalEarned: {
        type: Number,
        default: 0,
      },
      uniqueEarners: {
        type: Number,
        default: 0,
      },
      averageTimeToEarn: Number, // in days
      completionRate: Number, // percentage of eligible users who earn this
      lastEarned: Date,
    },
    
    // Localization
    localization: {
      en: {
        name: String,
        description: String,
      },
      sw: {
        name: String,
        description: String,
      },
    },
    
    // Admin Information
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
    },
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
    },
    
    // Metadata
    metadata: {
      type: Object,
      default: {},
    },
  },
  {
    timestamps: true,
  }
);

// Indexes
achievementDefinitionSchema.index({ id: 1 });
achievementDefinitionSchema.index({ category: 1, rarity: 1 });
achievementDefinitionSchema.index({ isActive: 1, isHidden: 1 });
achievementDefinitionSchema.index({ "seasonal.isSeasonalAchievement": 1, "seasonal.availableSeasons": 1 });

// Virtual for display name based on locale
achievementDefinitionSchema.virtual('displayName').get(function() {
  // Default to English, could be enhanced with user locale
  return this.localization?.en?.name || this.name;
});

// Method to check if user qualifies for this achievement
achievementDefinitionSchema.methods.checkUserQualifies = async function(user, userStats = {}) {
  const req = this.requirements;
  
  // Check level requirements
  if (req.level?.minimum && user.currentLevel < req.level.minimum) {
    return { qualifies: false, reason: 'Level too low' };
  }
  
  // Check prerequisites
  if (req.prerequisites?.achievements?.length > 0) {
    const userAchievements = user.achievements.map(a => a.id);
    const hasAllPrereqs = req.prerequisites.achievements.every(prereq => 
      userAchievements.includes(prereq)
    );
    if (!hasAllPrereqs) {
      return { qualifies: false, reason: 'Missing prerequisites' };
    }
  }
  
  // Check if already earned (and not repeatable)
  if (!this.tracking.isRepeatable) {
    const alreadyEarned = user.achievements.some(a => a.id === this.id);
    if (alreadyEarned) {
      return { qualifies: false, reason: 'Already earned' };
    }
  }
  
  // Check seasonal availability
  if (this.seasonal.isSeasonalAchievement) {
    const currentSeason = user.currentSeason;
    if (!this.seasonal.availableSeasons.includes(currentSeason)) {
      return { qualifies: false, reason: 'Not available this season' };
    }
  }
  
  return { qualifies: true };
};

// Static method to get available achievements for user
achievementDefinitionSchema.statics.getAvailableForUser = function(user) {
  return this.find({
    isActive: true,
    isHidden: false,
    $or: [
      { "requirements.level.minimum": { $lte: user.currentLevel } },
      { "requirements.level.minimum": { $exists: false } }
    ]
  });
};

// Static method to get achievements by category
achievementDefinitionSchema.statics.getByCategory = function(category) {
  return this.find({
    category: category,
    isActive: true,
  }).sort({ rarity: 1, xpReward: 1 });
};

const AchievementDefinition = mongoose.model("achievement_definitions", achievementDefinitionSchema);

module.exports = AchievementDefinition;
