const axios = require('axios');

const testSubscriptionAPI = async () => {
  try {
    console.log('🔍 Testing XP Leaderboard API for subscription data...\n');
    
    const response = await axios.get('http://localhost:5000/api/quiz/xp-leaderboard?limit=5&levelFilter=all');
    
    console.log('✅ API Response Status:', response.status);
    console.log('📊 Total Users:', response.data.length);
    
    if (response.data.length > 0) {
      console.log('\n🔍 First 3 users subscription data:');
      
      response.data.slice(0, 3).forEach((user, index) => {
        console.log(`\n👤 User ${index + 1}: ${user.name}`);
        console.log(`   📧 Email: ${user.email}`);
        console.log(`   📊 Subscription Status: ${user.subscriptionStatus || 'undefined'}`);
        console.log(`   📅 Subscription End Date: ${user.subscriptionEndDate || 'undefined'}`);
        console.log(`   📋 Subscription Plan: ${user.subscriptionPlan || 'undefined'}`);
        console.log(`   🎯 Active Plan Title: ${user.activePlanTitle || 'undefined'}`);
        console.log(`   ⏱️ Active Plan Duration: ${user.activePlanDuration || 'undefined'}`);
        console.log(`   🏆 Total XP: ${user.totalXP || 0}`);
        console.log(`   📈 Rank: ${user.rank || 'N/A'}`);
      });
      
      // Check if any users have subscription data
      const usersWithSubscription = response.data.filter(user => 
        user.subscriptionStatus && user.subscriptionStatus !== 'free'
      );
      
      const usersWithActivePlan = response.data.filter(user => 
        user.activePlanTitle
      );
      
      console.log(`\n📊 Summary:`);
      console.log(`   👥 Total users: ${response.data.length}`);
      console.log(`   💳 Users with subscription status: ${usersWithSubscription.length}`);
      console.log(`   🎯 Users with active plan title: ${usersWithActivePlan.length}`);
      
      if (usersWithActivePlan.length > 0) {
        console.log(`\n🎯 Users with active plans:`);
        usersWithActivePlan.forEach(user => {
          console.log(`   - ${user.name}: ${user.activePlanTitle} (${user.subscriptionStatus})`);
        });
      }
      
    } else {
      console.log('❌ No users found in response');
    }
    
  } catch (error) {
    console.error('❌ API Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
};

testSubscriptionAPI();
