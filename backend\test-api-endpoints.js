const axios = require('axios');

console.log('🧪 Testing API Endpoints for Client-Server Connection...\n');

// Test 1: Server Health
async function testServerHealth() {
  try {
    console.log('1️⃣ Testing Server Health...');
    const response = await axios.get('http://localhost:5000');
    console.log('✅ Server Health: PASSED');
    console.log(`   Status: ${response.status}`);
    console.log(`   Response: ${response.data.substring(0, 50)}...`);
    return true;
  } catch (error) {
    console.log('❌ Server Health: FAILED');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Test 2: API Health Check
async function testAPIHealth() {
  try {
    console.log('\n2️⃣ Testing API Health...');
    const response = await axios.get('http://localhost:5000/api/health');
    console.log('✅ API Health: PASSED');
    console.log(`   Status: ${response.status}`);
    return true;
  } catch (error) {
    console.log('❌ API Health: FAILED');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Test 3: Database Test Endpoint
async function testDatabaseEndpoint() {
  try {
    console.log('\n3️⃣ Testing Database Endpoint...');
    const response = await axios.get('http://localhost:5000/api/test/db');
    console.log('✅ Database Endpoint: PASSED');
    console.log(`   Status: ${response.status}`);
    return true;
  } catch (error) {
    console.log('❌ Database Endpoint: FAILED');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Test 4: Plans Endpoint (No Auth Required)
async function testPlansEndpoint() {
  try {
    console.log('\n4️⃣ Testing Plans Endpoint...');
    const response = await axios.get('http://localhost:5000/api/plans');
    console.log('✅ Plans Endpoint: PASSED');
    console.log(`   Status: ${response.status}`);
    console.log(`   Plans Found: ${response.data?.length || 0}`);
    return true;
  } catch (error) {
    console.log('❌ Plans Endpoint: FAILED');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Test 5: CORS Headers
async function testCORSHeaders() {
  try {
    console.log('\n5️⃣ Testing CORS Headers...');
    const response = await axios.options('http://localhost:5000/api/health');
    console.log('✅ CORS Headers: PASSED');
    console.log(`   Status: ${response.status}`);
    return true;
  } catch (error) {
    console.log('❌ CORS Headers: FAILED');
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

// Run all tests
async function runAPITests() {
  console.log('🚀 Starting API Connection Tests...\n');
  
  const results = [];
  
  results.push(await testServerHealth());
  results.push(await testAPIHealth());
  results.push(await testDatabaseEndpoint());
  results.push(await testPlansEndpoint());
  results.push(await testCORSHeaders());
  
  const passedTests = results.filter(result => result).length;
  const totalTests = results.length;
  
  console.log('\n📊 Test Results:');
  console.log(`   Passed: ${passedTests}/${totalTests}`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All API tests PASSED!');
    console.log('✅ Server is ready for client connections');
    console.log('✅ Database is accessible through API');
    console.log('✅ CORS is properly configured');
    console.log('\n🔗 Client can now connect to:');
    console.log('   - Server: http://localhost:5000');
    console.log('   - API: http://localhost:5000/api/*');
  } else {
    console.log('\n❌ Some API tests FAILED!');
    console.log('Please check server configuration and try again');
  }
}

runAPITests();
