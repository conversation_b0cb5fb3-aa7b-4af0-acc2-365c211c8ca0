const mongoose = require("mongoose");

const connectionOptions = {
  maxPoolSize: 10,
  family: 4,
  bufferCommands: false
};

const connectDB = async () => {
  const urls = [
    process.env.MONGO_URL_ATLAS, // Primary Non-SRV
    process.env.MONGO_URL_FALLBACK // Fallback Non-SRV
  ];

  for (let i = 0; i < urls.length; i++) {
    try {
      console.log(`🔗 Trying MongoDB connection [${i + 1}/${urls.length}]...`);
      await mongoose.connect(urls[i], connectionOptions);
      console.log(`✅ Connected to MongoDB using ${i === 0 ? "Primary" : "Fallback"} URI`);
      return; // Stop after successful connection
    } catch (error) {
      console.error(`❌ Connection attempt ${i + 1} failed: ${error.message}`);
    }
  }

  console.error("❌ All MongoDB connection attempts failed");
  process.exit(1);
};

// ✅ Connection events
mongoose.connection.on("error", (err) => console.error("❌ MongoDB Error:", err.message));
mongoose.connection.on("disconnected", () => console.log("⚠️ MongoDB Disconnected"));

// ✅ Graceful shutdown
process.on("SIGINT", async () => {
  await mongoose.connection.close();
  console.log("MongoDB closed due to app termination");
  process.exit(0);
});

module.exports = { connectDB };
