const mongoose = require('mongoose');
require('dotenv').config();
const User = require('./models/userModel');

async function checkCurrentUserStatus() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('Connected to MongoDB');
    
    // Check the Brainwave user (likely the current user)
    const user = await User.findOne({name: 'Brainwave✓'});
    if (user) {
      console.log('Current user status:');
      console.log('Name:', user.name);
      console.log('Is Admin:', user.isAdmin);
      console.log('Role:', user.role);
      console.log('Total XP:', user.totalXP);
      console.log('Lifetime XP:', user.lifetimeXP);
      console.log('Season XP:', user.seasonXP);
      console.log('Current Level:', user.currentLevel);
      
      // Check if this user would appear in rankings if not admin
      const nonAdminUsers = await User.find({
        isAdmin: { $ne: true },
        role: { $ne: 'admin' }
      }).sort({ totalXP: -1 }).limit(10);
      
      console.log('\nTop 10 non-admin users by XP:');
      nonAdminUsers.forEach((u, i) => {
        console.log((i + 1) + '. ' + u.name + ': ' + u.totalXP + ' XP');
      });
      
      // Calculate where this user would rank
      const userXP = user.totalXP || 0;
      const higherXPUsers = await User.countDocuments({
        isAdmin: { $ne: true },
        role: { $ne: 'admin' },
        totalXP: { $gt: userXP }
      });
      
      console.log('\nIf not admin, this user would rank:', higherXPUsers + 1);
      
    } else {
      console.log('User not found');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkCurrentUserStatus();
