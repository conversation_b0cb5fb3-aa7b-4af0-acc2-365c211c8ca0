const mongoose = require("mongoose");
const User = require("./models/userModel");
require("dotenv").config();

const makeUserAdmin = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log("✅ Connected to MongoDB");

    // Find user by email (change this to your email)
    const userEmail = "<EMAIL>"; // Change this to your email
    
    console.log(`🔍 Looking for user with email: ${userEmail}`);
    
    const user = await User.findOne({ email: userEmail });
    
    if (!user) {
      console.log("❌ User not found with that email");
      console.log("Available users:");
      const allUsers = await User.find({}).select('name email isAdmin');
      allUsers.forEach(u => {
        console.log(`- ${u.name} (${u.email}) - Admin: ${u.isAdmin}`);
      });
      return;
    }

    console.log(`👤 Found user: ${user.name} (${user.email})`);
    console.log(`Current admin status: ${user.isAdmin}`);

    if (user.isAdmin) {
      console.log("✅ User is already an admin!");
    } else {
      // Make user admin
      await User.findByIdAndUpdate(user._id, { 
        isAdmin: true,
        isBlocked: false // Also ensure not blocked
      });
      
      console.log("🎉 User has been made admin!");
      
      // Verify the change
      const updatedUser = await User.findById(user._id);
      console.log(`✅ Verified - Admin status: ${updatedUser.isAdmin}`);
    }

  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    await mongoose.disconnect();
    console.log("🔌 Disconnected from MongoDB");
    process.exit(0);
  }
};

// Run the script
makeUserAdmin();
