.edit-study-material-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px;
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.form-icon {
  font-size: 32px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
}

.form-header h2 {
  color: white;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.form-header p {
  color: rgba(255, 255, 255, 0.9);
  margin: 4px 0 0 0;
  font-size: 14px;
}

/* Form Styles */
.material-form {
  padding: 30px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-item-half {
  margin-bottom: 20px;
}

.ant-form-item-label > label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Upload Method Section */
.upload-method-section {
  margin-bottom: 25px;
}

.section-label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
  margin-bottom: 12px;
}

.upload-method-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.method-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.method-option:hover {
  border-color: #3498db;
  background: #f0f8ff;
}

.method-option.active {
  border-color: #3498db;
  background: #e3f2fd;
  color: #1976d2;
}

.method-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: #7f8c8d;
}

.method-option.active .method-icon {
  color: #1976d2;
}

.method-option span {
  font-weight: 500;
  font-size: 14px;
}

/* Additional Classes Section */
.additional-classes-section .ant-form-item-label {
  margin-bottom: 8px;
}

.additional-classes-note {
  margin-top: 8px;
}

.additional-classes-note small {
  color: #7f8c8d;
  font-style: italic;
}

/* Upload Areas */
.upload-section {
  margin-bottom: 25px;
}

.upload-area {
  border: 2px dashed #d1ecf1;
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  background: #f8fdff;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #3498db;
  background: #f0f8ff;
}

.upload-area.small {
  padding: 30px 20px;
}

.upload-icon {
  font-size: 32px;
  color: #3498db;
  margin-bottom: 12px;
}

.upload-area p {
  margin: 8px 0 4px 0;
  color: #2c3e50;
  font-weight: 500;
}

.upload-hint {
  color: #7f8c8d !important;
  font-size: 12px !important;
  font-weight: normal !important;
  margin: 4px 0 0 0 !important;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.cancel-button {
  padding: 12px 24px;
  height: auto;
  border-radius: 8px;
  font-weight: 500;
  border: 2px solid #e9ecef;
  color: #7f8c8d;
  display: flex;
  align-items: center;
  gap: 8px;
}

.cancel-button:hover {
  border-color: #bdc3c7;
  color: #2c3e50;
}

.submit-button {
  padding: 12px 24px;
  height: auto;
  border-radius: 8px;
  font-weight: 500;
  background: #3498db;
  border-color: #3498db;
  display: flex;
  align-items: center;
  gap: 8px;
}

.submit-button:hover {
  background: #2980b9;
  border-color: #2980b9;
}

/* Ant Design Overrides */
.ant-select {
  border-radius: 8px;
}

.ant-select-selector {
  border-radius: 8px !important;
  border: 2px solid #e9ecef !important;
  background: #f8f9fa !important;
  padding: 8px 12px !important;
  min-height: 48px !important;
}

.ant-select-focused .ant-select-selector {
  border-color: #3498db !important;
  background: white !important;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1) !important;
}

.ant-upload {
  width: 100%;
}

.ant-upload-list {
  margin-top: 15px;
}

.ant-upload-list-item {
  border-radius: 8px;
  border: 1px solid #e9ecef;
  background: #f8f9fa;
}

/* Responsive Design */
@media (max-width: 768px) {
  .edit-study-material-form {
    margin: 10px;
    border-radius: 8px;
  }

  .form-header {
    padding: 20px;
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .material-form {
    padding: 20px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .upload-method-options {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 10px;
  }

  .cancel-button,
  .submit-button {
    width: 100%;
    justify-content: center;
  }

  .upload-area {
    padding: 30px 15px;
  }

  .upload-area.small {
    padding: 25px 15px;
  }
}

/* Loading States */
.ant-btn-loading {
  pointer-events: none;
}

/* Form Validation */
.ant-form-item-has-error .ant-select-selector {
  border-color: #e74c3c !important;
}

.ant-form-item-has-error .form-input {
  border-color: #e74c3c;
}

.ant-form-item-explain-error {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
}

/* Success States */
.ant-form-item-has-success .ant-select-selector {
  border-color: #27ae60 !important;
}

.ant-form-item-has-success .form-input {
  border-color: #27ae60;
}

/* Disabled States */
.ant-select-disabled .ant-select-selector {
  background: #f5f5f5 !important;
  color: #bfbfbf !important;
  cursor: not-allowed !important;
}

.form-input:disabled {
  background: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

/* Upload Drag States */
.ant-upload-drag.ant-upload-drag-hover {
  border-color: #3498db;
}

.ant-upload-drag {
  border-radius: 12px;
  background: #f8fdff;
}

/* Select Dropdown */
.ant-select-dropdown {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ant-select-item {
  border-radius: 6px;
  margin: 2px 8px;
}

.ant-select-item-option-selected {
  background: #e3f2fd;
  color: #1976d2;
  font-weight: 500;
}
