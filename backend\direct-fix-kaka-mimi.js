const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const Plan = require('./models/planModel');

async function directFixKakaMimi() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    console.log('🔧 Direct fix for kaka.mimi subscription...\n');
    
    // Find the user
    const user = await User.findOne({ username: 'kaka.mimi' });
    if (!user) {
      console.log('❌ User kaka.mimi not found');
      return;
    }
    
    console.log('👤 User found:', user.firstName, user.lastName);
    console.log('   Current status:', user.subscriptionStatus);
    console.log('   Payment required:', user.paymentRequired);
    
    // Find their subscriptions
    const subscriptions = await Subscription.find({ user: user._id })
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    console.log(`\n💳 Found ${subscriptions.length} subscriptions`);
    
    if (subscriptions.length === 0) {
      console.log('❌ No subscriptions found for this user');
      return;
    }
    
    // Get the most recent subscription (Standard Membership - 500 TZS)
    const latestSubscription = subscriptions[0];
    console.log('\n📋 Latest subscription:');
    console.log('   Plan:', latestSubscription.activePlan?.title);
    console.log('   Amount:', latestSubscription.activePlan?.discountedPrice);
    console.log('   Current Status:', latestSubscription.status);
    console.log('   Payment Status:', latestSubscription.paymentStatus);
    
    // Check if this is the Standard Membership (500 TZS)
    if (latestSubscription.activePlan?.discountedPrice === 500) {
      console.log('\n✅ This is the Standard Membership plan (500 TZS)');
      console.log('🔄 Activating subscription...');
      
      // Calculate subscription dates
      const startDate = new Date();
      const endDate = new Date();
      const planDuration = latestSubscription.activePlan?.duration || 1;
      endDate.setMonth(endDate.getMonth() + planDuration);
      
      const formattedStartDate = startDate.toISOString().split('T')[0];
      const formattedEndDate = endDate.toISOString().split('T')[0];
      
      // Update subscription
      latestSubscription.paymentStatus = 'paid';
      latestSubscription.status = 'active';
      latestSubscription.startDate = formattedStartDate;
      latestSubscription.endDate = formattedEndDate;
      
      // Update payment history
      if (latestSubscription.paymentHistory.length > 0) {
        const latestPayment = latestSubscription.paymentHistory[latestSubscription.paymentHistory.length - 1];
        latestPayment.paymentStatus = 'paid';
        latestPayment.referenceId = `MANUAL_FIX_${Date.now()}`;
      }
      
      await latestSubscription.save();
      console.log('✅ Subscription updated successfully');
      
      // Update user status
      user.subscriptionStatus = 'active';
      user.paymentRequired = false;
      user.subscriptionEndDate = new Date(formattedEndDate);
      
      await user.save();
      console.log('✅ User status updated successfully');
      
      console.log('\n🎉 SUCCESS! kaka.mimi subscription is now active!');
      console.log(`📅 Subscription period: ${formattedStartDate} to ${formattedEndDate}`);
      console.log(`⏰ Duration: ${planDuration} month(s)`);
      
    } else {
      console.log('\n⚠️ Latest subscription is not the Standard Membership');
      console.log('   Please verify which plan should be activated');
    }
    
    // Show final status
    console.log('\n📊 Final Status:');
    const updatedUser = await User.findById(user._id);
    const updatedSubscription = await Subscription.findById(latestSubscription._id).populate('activePlan');
    
    console.log('👤 User:');
    console.log('   Subscription Status:', updatedUser.subscriptionStatus);
    console.log('   Payment Required:', updatedUser.paymentRequired);
    console.log('   Subscription End Date:', updatedUser.subscriptionEndDate);
    
    console.log('💳 Subscription:');
    console.log('   Status:', updatedSubscription.status);
    console.log('   Payment Status:', updatedSubscription.paymentStatus);
    console.log('   Start Date:', updatedSubscription.startDate);
    console.log('   End Date:', updatedSubscription.endDate);
    console.log('   Plan:', updatedSubscription.activePlan?.title);
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the fix
directFixKakaMimi();
