const mongoose = require('mongoose');
require('dotenv').config();
const User = require('./models/userModel');

async function findNonAdminUser() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('Connected to MongoDB');
    
    // Find a non-admin user with low XP for testing
    const user = await User.findOne({
      isAdmin: { $ne: true },
      totalXP: { $lt: 1000 }
    }).sort({ totalXP: 1 });
    
    if (user) {
      console.log('Found test user:', user.name);
      console.log('Current XP:', user.totalXP);
      console.log('Is Admin:', user.isAdmin);
      console.log('User ID:', user._id.toString());
    } else {
      console.log('No suitable test user found');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

findNonAdminUser();
