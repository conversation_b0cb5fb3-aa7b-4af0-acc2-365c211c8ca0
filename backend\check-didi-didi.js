const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const Plan = require('./models/planModel');

async function checkDidiDidi() {
  try {
    await mongoose.connect('mongodb://hvmgeeks:<EMAIL>:27017,ac-4ojvv6y-shard-00-01.cdg8fdn.mongodb.net:27017,ac-4ojvv6y-shard-00-02.cdg8fdn.mongodb.net:27017/stjoseph?ssl=true&replicaSet=atlas-fsgg6f-shard-0&authSource=admin&retryWrites=true&w=majority&appName=Cluster0');
    
    console.log('🔍 Searching for user didi.didi...');
    
    // Find user by username
    const user = await User.findOne({ username: 'didi.didi' });
    
    if (!user) {
      console.log('❌ User didi.didi not found');
      return;
    }
    
    console.log('👤 User found:');
    console.log('   Name:', user.firstName, user.lastName);
    console.log('   Email:', user.email);
    console.log('   Username:', user.username);
    console.log('   Phone:', user.phoneNumber);
    console.log('   Subscription Status:', user.subscriptionStatus);
    console.log('   Payment Required:', user.paymentRequired);
    console.log('   Subscription End Date:', user.subscriptionEndDate);
    console.log('   Created:', user.createdAt);
    
    // Find all subscriptions for this user
    const subscriptions = await Subscription.find({ user: user._id })
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    console.log('\n💳 Subscriptions found:', subscriptions.length);
    
    subscriptions.forEach((sub, index) => {
      console.log(`\n📋 Subscription ${index + 1}:`);
      console.log('   ID:', sub._id);
      console.log('   Plan:', sub.activePlan?.title || 'Unknown');
      console.log('   Payment Status:', sub.paymentStatus);
      console.log('   Status:', sub.status);
      console.log('   Start Date:', sub.startDate);
      console.log('   End Date:', sub.endDate);
      console.log('   Created:', sub.createdAt);
      console.log('   Payment History:', sub.paymentHistory.length, 'entries');
      
      if (sub.paymentHistory.length > 0) {
        sub.paymentHistory.forEach((payment, pIndex) => {
          console.log(`   Payment ${pIndex + 1}:`);
          console.log('     Order ID:', payment.orderId);
          console.log('     Amount:', payment.amount);
          console.log('     Status:', payment.paymentStatus);
          console.log('     Date:', payment.paymentDate);
          console.log('     Reference:', payment.referenceId);
        });
      }
    });
    
    // Check if there are any paid subscriptions that should be active
    const paidSubscriptions = subscriptions.filter(sub => sub.paymentStatus === 'paid');
    const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
    const pendingSubscriptions = subscriptions.filter(sub => sub.paymentStatus === 'pending');
    
    console.log('\n📊 Summary:');
    console.log('   Total subscriptions:', subscriptions.length);
    console.log('   Paid subscriptions:', paidSubscriptions.length);
    console.log('   Active subscriptions:', activeSubscriptions.length);
    console.log('   Pending subscriptions:', pendingSubscriptions.length);
    
    // Analyze the issue
    console.log('\n🔍 Issue Analysis:');
    
    if (paidSubscriptions.length > 0 && user.subscriptionStatus === 'free') {
      console.log('⚠️  ISSUE: User has paid subscriptions but status is FREE!');
      console.log('   This explains why subscription page shows "free account"');
      console.log('   But other pages might be checking subscription records directly');
    }
    
    if (activeSubscriptions.length > 0 && user.subscriptionStatus === 'free') {
      console.log('⚠️  ISSUE: User has active subscriptions but user status is FREE!');
    }
    
    if (paidSubscriptions.length > 0 || activeSubscriptions.length > 0) {
      console.log('\n💡 SOLUTION: User should have active status');
      
      // Check if subscription is still valid
      const latestActiveSub = activeSubscriptions[0] || paidSubscriptions[0];
      if (latestActiveSub && latestActiveSub.endDate) {
        const endDate = new Date(latestActiveSub.endDate);
        const today = new Date();
        
        if (endDate > today) {
          console.log('✅ Subscription is still valid until:', endDate.toDateString());
          console.log('🔧 User status should be updated to "active"');
        } else {
          console.log('❌ Subscription has expired on:', endDate.toDateString());
          console.log('🔧 User status should be "expired"');
        }
      }
    }
    
    // Check recent activity
    const recentSub = subscriptions[0];
    if (recentSub) {
      const timeSinceCreated = Date.now() - new Date(recentSub.createdAt).getTime();
      const hoursAgo = Math.floor(timeSinceCreated / (1000 * 60 * 60));
      console.log(`\n⏰ Most recent subscription was created ${hoursAgo} hours ago`);
      
      if (hoursAgo < 24 && recentSub.paymentStatus === 'pending') {
        console.log('💡 This might be a recent payment that webhook missed');
        console.log('🔧 Payment verification service should catch this automatically');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    mongoose.disconnect();
  }
}

checkDidiDidi();
