const subtitleService = require('./services/subtitleService');
const axios = require('axios');

async function testSubtitleServiceDirect() {
  console.log('🧪 Testing subtitle service directly...');
  console.log('📦 Subtitle service loaded:', typeof subtitleService);

  try {
    
    // Test with a small, publicly accessible video
    const testVideoUrl = 'https://brainwavebucket.s3.us-east-1.amazonaws.com/Videos/CPR_in_Action___A_3D_look_inside_the_body(720p).mp4';
    const testVideoId = 'test-video-123';
    
    console.log('📹 Testing with video URL:', testVideoUrl);
    
    // First, let's test if we can download the video
    console.log('1️⃣ Testing video download...');
    try {
      const response = await axios.head(testVideoUrl, { timeout: 10000 });
      console.log('✅ Video is accessible');
      console.log('   Content-Type:', response.headers['content-type']);
      console.log('   Content-Length:', response.headers['content-length'], 'bytes');
    } catch (downloadError) {
      console.error('❌ Cannot access video:', downloadError.message);
      return;
    }
    
    // Test subtitle generation
    console.log('2️⃣ Testing subtitle generation...');
    try {
      const result = await subtitleService.generateSubtitlesFromS3Video(
        testVideoUrl,
        testVideoId,
        'en'
      );
      
      console.log('✅ Subtitle generation successful!');
      console.log('📝 Result:', {
        language: result.language,
        url: result.url,
        isAutoGenerated: result.isAutoGenerated,
        contentLength: result.content ? result.content.length : 0
      });
      
      // Show first few lines of subtitle content
      if (result.content) {
        const lines = result.content.split('\n').slice(0, 10);
        console.log('📄 First few lines of subtitles:');
        lines.forEach(line => console.log('   ', line));
      }
      
    } catch (subtitleError) {
      console.error('❌ Subtitle generation failed:', subtitleError.message);
      console.error('   Stack:', subtitleError.stack);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('   Stack:', error.stack);
  }
}

testSubtitleServiceDirect();
