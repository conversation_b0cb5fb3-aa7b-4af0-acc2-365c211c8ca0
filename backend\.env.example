# ============================================================================
# ST. JOSEPH KIBADA QUIZ APP - ENVIRONMENT CONFIGURATION TEMPLATE
# ============================================================================
# 
# INSTRUCTIONS:
# 1. Copy this file to .env
# 2. Replace all placeholder values with actual credentials
# 3. Never commit .env file to version control
# 4. Use strong, unique passwords and secrets
# 5. Rotate credentials regularly
#
# ============================================================================

# ============================================================================
# APPLICATION CONFIGURATION
# ============================================================================
NODE_ENV=development
APP_NAME="St. Joseph Kibada Quiz App"
APP_VERSION=1.0.0
PORT=5000
HOST=localhost

# Frontend URL (for CORS and redirects)
CLIENT_URL=http://localhost:3000
FRONTEND_DOMAIN=localhost:3000

# API Configuration
API_VERSION=v1
API_PREFIX=/api

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================
# Primary MongoDB Atlas Connection
MONGO_URL=************************************************?options

# Fallback MongoDB Connection
MONGO_URL_FALLBACK=*********************************************************?options

# Database Configuration
DB_NAME=stjoseph
DB_CONNECTION_TIMEOUT=30000
DB_SOCKET_TIMEOUT=45000
DB_MAX_POOL_SIZE=10
DB_MIN_POOL_SIZE=5

# ============================================================================
# AUTHENTICATION & SECURITY
# ============================================================================
# JWT Configuration (Use a strong, random secret in production)
JWT_SECRET=your_super_secure_jwt_secret_here_minimum_32_characters
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# Session Configuration
SESSION_SECRET=your_super_secure_session_secret_here
SESSION_MAX_AGE=86400000

# Password Security
BCRYPT_SALT_ROUNDS=12
PASSWORD_MIN_LENGTH=8

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ============================================================================
# EMAIL CONFIGURATION
# ============================================================================
# Email Service Provider
EMAIL_SERVICE=gmail
SENDER_EMAIL=<EMAIL>
SENDER_EMAIL_PASSWORD=your_app_specific_password
SENDER_NAME="St. Joseph Kibada Quiz App"

# Email Templates
EMAIL_VERIFICATION_TEMPLATE=verification
PASSWORD_RESET_TEMPLATE=password-reset
WELCOME_EMAIL_TEMPLATE=welcome

# Admin Email
OWNER_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# ============================================================================
# AWS CONFIGURATION
# ============================================================================
# AWS Credentials
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1

# S3 Configuration
AWS_S3_BUCKET_NAME=your_s3_bucket_name
AWS_S3_BUCKET_REGION=us-east-1
S3_UPLOAD_MAX_SIZE=10485760
S3_ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,application/pdf,video/mp4,video/avi

# CloudFront (if using CDN)
AWS_CLOUDFRONT_DOMAIN=your_cloudfront_domain

# ============================================================================
# AI/ML SERVICES
# ============================================================================
# OpenAI Configuration
OPENAI_API_KEY=sk-your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7

# AI Features
AI_QUESTION_GENERATION=true
AI_ANSWER_EXPLANATION=true
AI_STUDY_RECOMMENDATIONS=true

# ============================================================================
# PAYMENT GATEWAY CONFIGURATION
# ============================================================================
# ZenoPay Configuration
ZENOPAY_ACCOUNT_ID=your_zenopay_account_id
ZENOPAY_SECRET_KEY=your_zenopay_secret_key
ZENOPAY_API_KEY=your_zenopay_api_key
ZENOPAY_WEBHOOK_URL=https://yourdomain.com/api/payment/webhook
ZENOPAY_ENVIRONMENT=sandbox

# Payment Configuration
PAYMENT_CURRENCY=TZS
PAYMENT_SUCCESS_URL=http://localhost:3000/payment/success
PAYMENT_CANCEL_URL=http://localhost:3000/payment/cancel
PAYMENT_WEBHOOK_SECRET=your_webhook_secret_key

# Subscription Plans
BASIC_PLAN_PRICE=5000
PREMIUM_PLAN_PRICE=10000
ANNUAL_PLAN_DISCOUNT=20

# ============================================================================
# LOGGING & MONITORING
# ============================================================================
# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
LOG_DATE_PATTERN=YYYY-MM-DD

# Error Tracking (Sentry, etc.)
SENTRY_DSN=your_sentry_dsn_here
ERROR_REPORTING=true

# Performance Monitoring
PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=1000

# ============================================================================
# CACHE CONFIGURATION
# ============================================================================
# Redis Configuration (if using Redis for caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0
CACHE_TTL=3600

# Memory Cache
MEMORY_CACHE_MAX_SIZE=100
MEMORY_CACHE_TTL=300

# ============================================================================
# FILE UPLOAD CONFIGURATION
# ============================================================================
# Upload Limits
MAX_FILE_SIZE=10485760
MAX_FILES_PER_REQUEST=5
UPLOAD_TIMEOUT=30000

# Allowed File Types
ALLOWED_IMAGE_TYPES=image/jpeg,image/png,image/gif,image/webp
ALLOWED_VIDEO_TYPES=video/mp4,video/avi,video/mov,video/wmv
ALLOWED_DOCUMENT_TYPES=application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# Storage Configuration
STORAGE_TYPE=s3
LOCAL_STORAGE_PATH=./uploads
CDN_BASE_URL=your_cdn_base_url

# ============================================================================
# NOTIFICATION CONFIGURATION
# ============================================================================
# Push Notifications (Firebase, etc.)
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# SMS Configuration (Twilio, etc.)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Email Notifications
EMAIL_NOTIFICATIONS=true
SMS_NOTIFICATIONS=false
PUSH_NOTIFICATIONS=false

# ============================================================================
# SOCIAL MEDIA INTEGRATION
# ============================================================================
# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_CALLBACK_URL=http://localhost:5000/auth/google/callback

# Facebook OAuth
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_CALLBACK_URL=http://localhost:5000/auth/facebook/callback

# ============================================================================
# ANALYTICS & TRACKING
# ============================================================================
# Google Analytics
GOOGLE_ANALYTICS_ID=your_google_analytics_id
GOOGLE_TAG_MANAGER_ID=your_google_tag_manager_id

# Custom Analytics
ANALYTICS_ENABLED=true
TRACK_USER_BEHAVIOR=true
TRACK_PERFORMANCE=true

# ============================================================================
# BACKUP & MAINTENANCE
# ============================================================================
# Database Backup
DB_BACKUP_ENABLED=true
DB_BACKUP_SCHEDULE=0 2 * * *
DB_BACKUP_RETENTION_DAYS=30
DB_BACKUP_S3_BUCKET=your_backup_bucket_name

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="System is under maintenance. Please try again later."

# ============================================================================
# DEVELOPMENT & DEBUGGING
# ============================================================================
# Debug Configuration
DEBUG=true
VERBOSE_LOGGING=false
API_DOCUMENTATION=true

# Development Tools
HOT_RELOAD=true
AUTO_RESTART=true
MOCK_EXTERNAL_APIS=false

# Testing
TEST_DATABASE_URL=mongodb://localhost:27017/stjoseph_test
TEST_EMAIL_PROVIDER=ethereal

# ============================================================================
# SECURITY HEADERS & CORS
# ============================================================================
# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
CORS_CREDENTIALS=true
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_HEADERS=Content-Type,Authorization,X-Requested-With

# Security Headers
HELMET_ENABLED=true
CSP_ENABLED=true
HSTS_ENABLED=true

# ============================================================================
# FEATURE FLAGS
# ============================================================================
# Application Features
FEATURE_USER_REGISTRATION=true
FEATURE_EMAIL_VERIFICATION=true
FEATURE_PASSWORD_RESET=true
FEATURE_SOCIAL_LOGIN=false
FEATURE_TWO_FACTOR_AUTH=false
FEATURE_ADMIN_PANEL=true
FEATURE_ANALYTICS_DASHBOARD=true
FEATURE_PAYMENT_GATEWAY=true
FEATURE_AI_ASSISTANCE=true
FEATURE_OFFLINE_MODE=false
FEATURE_DARK_MODE=true
FEATURE_MULTI_LANGUAGE=false

# Educational Features
FEATURE_VIDEO_STREAMING=true
FEATURE_LIVE_CLASSES=false
FEATURE_DISCUSSION_FORUM=true
FEATURE_STUDY_GROUPS=false
FEATURE_PROGRESS_TRACKING=true
FEATURE_CERTIFICATES=true
FEATURE_GAMIFICATION=true

# ============================================================================
# END OF CONFIGURATION
# ============================================================================
