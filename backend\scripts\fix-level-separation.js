const mongoose = require('mongoose');
require('dotenv').config();

async function fixLevelSeparation() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Direct MongoDB operations
        const db = mongoose.connection.db;

        // Fix Users collection
        console.log('📊 Fixing Users collection...');
        
        // Update Primary -> primary
        const primaryUsers = await db.collection('users').updateMany(
            { level: 'Primary' },
            { $set: { level: 'primary' } }
        );
        console.log(`✅ Updated ${primaryUsers.modifiedCount} Primary users to primary`);

        // Update Secondary -> secondary
        const secondaryUsers = await db.collection('users').updateMany(
            { level: 'Secondary' },
            { $set: { level: 'secondary' } }
        );
        console.log(`✅ Updated ${secondaryUsers.modifiedCount} Secondary users to secondary`);

        // Update Advance -> advance
        const advanceUsers = await db.collection('users').updateMany(
            { level: 'Advance' },
            { $set: { level: 'advance' } }
        );
        console.log(`✅ Updated ${advanceUsers.modifiedCount} Advance users to advance`);

        // Update null levels to primary
        const nullUsers = await db.collection('users').updateMany(
            { $or: [{ level: null }, { level: { $exists: false } }] },
            { $set: { level: 'primary' } }
        );
        console.log(`✅ Updated ${nullUsers.modifiedCount} null level users to primary`);

        // Fix Forum Questions collection
        console.log('📊 Fixing Forum Questions collection...');
        
        const primaryForum = await db.collection('forum-questions').updateMany(
            { level: 'Primary' },
            { $set: { level: 'primary' } }
        );
        console.log(`✅ Updated ${primaryForum.modifiedCount} Primary forum questions to primary`);

        const secondaryForum = await db.collection('forum-questions').updateMany(
            { level: 'Secondary' },
            { $set: { level: 'secondary' } }
        );
        console.log(`✅ Updated ${secondaryForum.modifiedCount} Secondary forum questions to secondary`);

        const advanceForum = await db.collection('forum-questions').updateMany(
            { level: 'Advance' },
            { $set: { level: 'advance' } }
        );
        console.log(`✅ Updated ${advanceForum.modifiedCount} Advance forum questions to advance`);

        const nullForum = await db.collection('forum-questions').updateMany(
            { $or: [{ level: null }, { level: { $exists: false } }] },
            { $set: { level: 'primary' } }
        );
        console.log(`✅ Updated ${nullForum.modifiedCount} null level forum questions to primary`);

        // Fix Exams collection
        console.log('📊 Fixing Exams collection...');
        
        const primaryExams = await db.collection('exams').updateMany(
            { level: 'Primary' },
            { $set: { level: 'primary' } }
        );
        console.log(`✅ Updated ${primaryExams.modifiedCount} Primary exams to primary`);

        const secondaryExams = await db.collection('exams').updateMany(
            { level: 'Secondary' },
            { $set: { level: 'secondary' } }
        );
        console.log(`✅ Updated ${secondaryExams.modifiedCount} Secondary exams to secondary`);

        const advanceExams = await db.collection('exams').updateMany(
            { level: 'Advance' },
            { $set: { level: 'advance' } }
        );
        console.log(`✅ Updated ${advanceExams.modifiedCount} Advance exams to advance`);

        // Verification
        console.log('\n📊 Verification - Current level distribution:');
        
        const userLevels = await db.collection('users').aggregate([
            { $group: { _id: "$level", count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
        ]).toArray();
        console.log('Users by level:', userLevels);

        const forumLevels = await db.collection('forum-questions').aggregate([
            { $group: { _id: "$level", count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
        ]).toArray();
        console.log('Forum questions by level:', forumLevels);

        const examLevels = await db.collection('exams').aggregate([
            { $group: { _id: "$level", count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
        ]).toArray();
        console.log('Exams by level:', examLevels);

        console.log('\n✅ Level separation fix completed successfully!');
        
    } catch (error) {
        console.error('❌ Error fixing level separation:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    fixLevelSeparation();
}

module.exports = fixLevelSeparation;
