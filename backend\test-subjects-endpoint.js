const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Test the subjects endpoint directly
async function testSubjectsEndpoint() {
  console.log('🧪 Testing Subjects Endpoint...\n');

  try {
    // Connect to database first
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    const Syllabus = require('./models/syllabusModel');

    // Check database state
    console.log('\n1️⃣ Checking database state...');
    const allSyllabuses = await Syllabus.find({});
    const activeSyllabuses = await Syllabus.find({ isActive: true });
    const completedSyllabuses = await Syllabus.find({ processingStatus: 'completed' });
    
    console.log(`Total syllabuses: ${allSyllabuses.length}`);
    console.log(`Active syllabuses: ${activeSyllabuses.length}`);
    console.log(`Completed syllabuses: ${completedSyllabuses.length}`);

    if (allSyllabuses.length > 0) {
      console.log('\nSyllabus details:');
      allSyllabuses.forEach((s, i) => {
        console.log(`${i + 1}. "${s.title}" - Level: ${s.level}, Subject: ${s.subject}, Active: ${s.isActive}, Status: ${s.processingStatus}`);
      });
    }

    // Test the static method directly
    console.log('\n2️⃣ Testing getAvailableSubjects method...');
    const primarySubjects = await Syllabus.getAvailableSubjects('primary');
    console.log(`Primary subjects: [${primarySubjects.join(', ')}]`);

    const secondarySubjects = await Syllabus.getAvailableSubjects('secondary');
    console.log(`Secondary subjects: [${secondarySubjects.join(', ')}]`);

    const advanceSubjects = await Syllabus.getAvailableSubjects('advance');
    console.log(`Advance subjects: [${advanceSubjects.join(', ')}]`);

    // If no active syllabuses, fix them
    if (activeSyllabuses.length === 0 && allSyllabuses.length > 0) {
      console.log('\n🔧 Fixing inactive syllabuses...');
      const result = await Syllabus.updateMany(
        { processingStatus: 'completed' },
        { isActive: true }
      );
      console.log(`✅ Updated ${result.modifiedCount} syllabuses to active`);
      
      // Re-test
      const newPrimarySubjects = await Syllabus.getAvailableSubjects('primary');
      console.log(`Primary subjects after fix: [${newPrimarySubjects.join(', ')}]`);
    }

    console.log('\n🎯 Summary:');
    console.log(`✅ Database connection: Working`);
    console.log(`✅ Syllabus model: Working`);
    console.log(`✅ Data exists: ${allSyllabuses.length > 0 ? 'Yes' : 'No'}`);
    console.log(`✅ Active data: ${activeSyllabuses.length > 0 ? 'Yes' : 'No'}`);
    console.log(`✅ Primary subjects available: ${primarySubjects.length > 0 ? 'Yes' : 'No'}`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from database');
  }
}

// Test API endpoint with authentication
async function testAPIEndpoint() {
  console.log('\n🌐 Testing API Endpoint...\n');
  
  const baseURL = 'http://localhost:5000';
  
  // Test without authentication (should fail)
  try {
    console.log('Testing without authentication...');
    const response = await axios.get(`${baseURL}/api/syllabus/subjects/primary`);
    console.log('❌ Should require authentication');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly requires authentication');
    } else {
      console.log('❌ Unexpected error:', error.response?.status, error.message);
    }
  }

  // Test with invalid token (should fail)
  try {
    console.log('Testing with invalid token...');
    const response = await axios.get(`${baseURL}/api/syllabus/subjects/primary`, {
      headers: { 'Authorization': 'Bearer invalid-token' }
    });
    console.log('❌ Should reject invalid token');
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('✅ Correctly rejects invalid token');
    } else {
      console.log('❌ Unexpected error:', error.response?.status, error.message);
    }
  }

  console.log('\n📋 API Test Summary:');
  console.log('✅ Endpoint exists and requires authentication');
  console.log('🔧 Need valid token to test actual functionality');
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Subjects Endpoint Tests\n');
  console.log('=' .repeat(60));
  
  await testSubjectsEndpoint();
  await testAPIEndpoint();
  
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 All tests completed!');
  
  console.log('\n🔧 Next Steps:');
  console.log('1. If subjects are available in database, check frontend authentication');
  console.log('2. If no subjects, upload and process more syllabuses');
  console.log('3. Test AI question generation interface with browser console open');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testSubjectsEndpoint,
  testAPIEndpoint,
  runAllTests
};
