const mongoose = require('mongoose');
const Plan = require('./models/planModel');

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || 'mongodb+srv://henrymushi:<EMAIL>/brainwave');
    console.log('✅ MongoDB connected successfully');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
};

const addGlimpPlan = async () => {
  try {
    await connectDB();
    
    console.log('🔄 Adding Glimp Plan...\n');
    
    // Check if Glimp plan already exists
    const existingGlimpPlan = await Plan.findOne({ title: 'Glimp Plan' });
    if (existingGlimpPlan) {
      console.log('⚠️ Glimp Plan already exists');
      return;
    }
    
    // Create the new Glimp plan
    const glimpPlan = new Plan({
      title: 'Glimp Plan',
      features: [
        '1-month full access',
        'Unlimited quizzes',
        'Personalized profile',
        'AI chat for instant help',
        'Forum for student discussions',
        'Study notes',
        'Past papers',
        'Books',
        'Learning videos',
        'Track progress with rankings'
      ],
      actualPrice: 15000,
      discountedPrice: 13000,
      discountPercentage: 13,
      duration: 1, // 1 month
      status: true,
    });

    const savedPlan = await glimpPlan.save();
    console.log('✅ Glimp Plan created successfully:', savedPlan);
    
    // Also update existing Basic plan to ensure proper ordering
    const basicPlan = await Plan.findOne({ title: 'Basic Membership' });
    if (basicPlan) {
      console.log('✅ Basic Plan found:', basicPlan.title);
    }
    
    console.log('\n🎉 Plan setup completed!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error adding Glimp Plan:', error);
    process.exit(1);
  }
};

addGlimpPlan();
