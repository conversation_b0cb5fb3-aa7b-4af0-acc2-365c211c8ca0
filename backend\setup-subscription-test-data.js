const mongoose = require('mongoose');
const User = require('./models/userModel');
const Plan = require('./models/planModel');
const Subscription = require('./models/subscriptionModel');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/brainwave', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const setupSubscriptionTestData = async () => {
  try {
    await connectDB();
    
    console.log('🔄 Setting up subscription test data...\n');
    
    // First, create test plans if they don't exist
    console.log('📋 Creating test plans...');
    
    const basicPlan = await Plan.findOneAndUpdate(
      { title: 'Basic Membership' },
      {
        title: 'Basic Membership',
        features: ['2-month access', 'Basic quizzes', 'Study materials'],
        actualPrice: 15000,
        discountedPrice: 12000,
        discountPercentage: 20,
        duration: 2,
        status: true
      },
      { upsert: true, new: true }
    );
    
    const standardPlan = await Plan.findOneAndUpdate(
      { title: 'Standard Membership' },
      {
        title: 'Standard Membership',
        features: ['3-month access', 'All quizzes', 'Study materials', 'AI chat'],
        actualPrice: 25000,
        discountedPrice: 20000,
        discountPercentage: 20,
        duration: 3,
        status: true
      },
      { upsert: true, new: true }
    );
    
    const premiumPlan = await Plan.findOneAndUpdate(
      { title: 'Premium Membership' },
      {
        title: 'Premium Membership',
        features: ['6-month access', 'All features', 'Priority support', 'Advanced analytics'],
        actualPrice: 45000,
        discountedPrice: 35000,
        discountPercentage: 22,
        duration: 6,
        status: true
      },
      { upsert: true, new: true }
    );
    
    console.log('✅ Plans created:');
    console.log(`   📦 Basic Plan: ${basicPlan._id}`);
    console.log(`   📦 Standard Plan: ${standardPlan._id}`);
    console.log(`   📦 Premium Plan: ${premiumPlan._id}`);
    
    // Get all non-admin users
    const users = await User.find({ isAdmin: { $ne: true } }).limit(15);
    
    if (users.length === 0) {
      console.log('❌ No users found');
      return;
    }
    
    console.log(`\n👥 Found ${users.length} users to update`);
    
    // Clear existing subscriptions for test users
    await Subscription.deleteMany({ user: { $in: users.map(u => u._id) } });
    console.log('🗑️ Cleared existing test subscriptions');
    
    // Update users with different subscription statuses and create actual subscriptions
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      let subscriptionStatus, subscriptionEndDate, subscriptionPlan, planToUse;
      
      if (i % 5 === 0) {
        // Premium Activated users
        subscriptionStatus = 'active';
        subscriptionPlan = 'premium';
        subscriptionEndDate = new Date(Date.now() + 180 * 24 * 60 * 60 * 1000); // 6 months from now
        planToUse = premiumPlan;
      } else if (i % 5 === 1) {
        // Standard Activated users
        subscriptionStatus = 'active';
        subscriptionPlan = 'standard';
        subscriptionEndDate = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000); // 3 months from now
        planToUse = standardPlan;
      } else if (i % 5 === 2) {
        // Basic Activated users
        subscriptionStatus = 'active';
        subscriptionPlan = 'basic';
        subscriptionEndDate = new Date(Date.now() + 60 * 24 * 60 * 60 * 1000); // 2 months from now
        planToUse = basicPlan;
      } else if (i % 5 === 3) {
        // Expired users (had subscription but expired)
        subscriptionStatus = 'expired';
        subscriptionPlan = 'basic';
        subscriptionEndDate = new Date(Date.now() - 10 * 24 * 60 * 60 * 1000); // 10 days ago
        planToUse = null; // No active subscription
      } else {
        // Free users (never had subscription)
        subscriptionStatus = 'free';
        subscriptionPlan = null;
        subscriptionEndDate = null;
        planToUse = null;
      }
      
      // Update user subscription fields
      await User.findByIdAndUpdate(user._id, {
        subscriptionStatus: subscriptionStatus,
        subscriptionEndDate: subscriptionEndDate,
        subscriptionPlan: subscriptionPlan
      });
      
      // Create actual subscription record for active users
      if (planToUse && subscriptionStatus === 'active') {
        const subscription = new Subscription({
          user: user._id,
          activePlan: planToUse._id,
          paymentStatus: 'paid',
          status: 'active',
          startDate: new Date().toISOString().split('T')[0],
          endDate: subscriptionEndDate.toISOString().split('T')[0],
          paymentHistory: [{
            orderId: `TEST_${Date.now()}_${i}`,
            plan: planToUse._id,
            amount: planToUse.discountedPrice,
            paymentStatus: 'paid',
            paymentDate: new Date().toISOString().split('T')[0]
          }]
        });
        
        await subscription.save();
        console.log(`✅ Created subscription for ${user.name}: ${planToUse.title}`);
      } else {
        console.log(`✅ Updated ${user.name}: ${subscriptionStatus} (${subscriptionPlan || 'no plan'})`);
      }
    }
    
    console.log('\n🎉 Subscription test data setup completed!');
    
    // Show summary
    const premiumCount = await User.countDocuments({ 
      subscriptionStatus: 'active',
      subscriptionPlan: 'premium',
      isAdmin: { $ne: true }
    });
    
    const standardCount = await User.countDocuments({ 
      subscriptionStatus: 'active',
      subscriptionPlan: 'standard',
      isAdmin: { $ne: true }
    });
    
    const basicCount = await User.countDocuments({ 
      subscriptionStatus: 'active',
      subscriptionPlan: 'basic',
      isAdmin: { $ne: true }
    });
    
    const expiredCount = await User.countDocuments({ 
      subscriptionStatus: 'expired',
      isAdmin: { $ne: true }
    });
    
    const freeCount = await User.countDocuments({ 
      subscriptionStatus: 'free',
      isAdmin: { $ne: true }
    });
    
    const activeSubscriptions = await Subscription.countDocuments({ status: 'active' });
    
    console.log('\n📊 Summary:');
    console.log(`🔵 Premium Activated: ${premiumCount}`);
    console.log(`🟡 Standard Activated: ${standardCount}`);
    console.log(`🟢 Basic Activated: ${basicCount}`);
    console.log(`🔴 Expired: ${expiredCount}`);
    console.log(`⚪ Free: ${freeCount}`);
    console.log(`📋 Active Subscriptions in DB: ${activeSubscriptions}`);
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
};

setupSubscriptionTestData();
