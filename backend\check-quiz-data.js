const mongoose = require("mongoose");
const path = require("path");
require("dotenv").config({ path: path.join(__dirname, '.env') });

async function checkQuizData() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log("✅ Connected to MongoDB");

    const Exam = require("./models/examModel");
    const Question = require("./models/questionModel");

    // Check total exams
    const totalExams = await Exam.countDocuments();
    console.log(`📊 Total exams in database: ${totalExams}`);

    // Check total questions
    const totalQuestions = await Question.countDocuments();
    console.log(`📊 Total questions in database: ${totalQuestions}`);

    // Find exams with populated questions
    const examsWithQuestions = await Exam.find().populate('questions');
    
    console.log("\n📋 Exam Details:");
    for (const exam of examsWithQuestions) {
      console.log(`\n🎯 Exam: ${exam.name}`);
      console.log(`   ID: ${exam._id}`);
      console.log(`   Level: ${exam.level}`);
      console.log(`   Class: ${exam.class}`);
      console.log(`   Subject: ${exam.subject}`);
      console.log(`   Duration: ${exam.duration} minutes`);
      console.log(`   Questions: ${exam.questions.length}`);
      
      if (exam.questions.length > 0) {
        console.log(`   First question: ${exam.questions[0].name || 'No name'}`);
        console.log(`   Question type: ${exam.questions[0].type || exam.questions[0].answerType || 'Unknown'}`);
        console.log(`   Has options: ${!!exam.questions[0].options}`);
        if (exam.questions[0].options) {
          console.log(`   Options count: ${Object.keys(exam.questions[0].options).length}`);
        }
      }
    }

    // Check for orphaned questions (questions not linked to any exam)
    const orphanedQuestions = await Question.find({ exam: { $exists: false } });
    console.log(`\n🔍 Orphaned questions (not linked to exams): ${orphanedQuestions.length}`);

    // Check for questions linked to non-existent exams
    const allQuestions = await Question.find({ exam: { $exists: true } });
    let brokenLinks = 0;
    for (const question of allQuestions) {
      const examExists = await Exam.findById(question.exam);
      if (!examExists) {
        brokenLinks++;
      }
    }
    console.log(`🔍 Questions with broken exam links: ${brokenLinks}`);

    await mongoose.disconnect();
    console.log("\n✅ Database check completed");

  } catch (error) {
    console.error("❌ Error:", error);
    await mongoose.disconnect();
  }
}

checkQuizData();
