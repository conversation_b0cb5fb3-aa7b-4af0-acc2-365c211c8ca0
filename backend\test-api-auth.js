const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Test API authentication and data loading
async function testAPIAuth() {
  console.log('🔐 Testing API Authentication and Data Loading...\n');

  try {
    // Connect to database first
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    const Syllabus = require('./models/syllabusModel');
    const User = require('./models/userModel');

    // Check database state
    console.log('\n1️⃣ Checking database state...');
    const syllabusCount = await Syllabus.countDocuments();
    const userCount = await User.countDocuments();
    console.log(`Syllabuses in database: ${syllabusCount}`);
    console.log(`Users in database: ${userCount}`);

    if (syllabusCount > 0) {
      const syllabuses = await Syllabus.find().limit(3);
      console.log('\nSample syllabuses:');
      syllabuses.forEach((s, i) => {
        console.log(`${i + 1}. ${s.title} (${s.level}) - Status: ${s.processingStatus}, Active: ${s.isActive}`);
      });
    }

    // Check for admin users
    const adminUsers = await User.find({ isAdmin: true }).limit(3);
    console.log(`\nAdmin users found: ${adminUsers.length}`);
    if (adminUsers.length > 0) {
      console.log('Sample admin users:');
      adminUsers.forEach((u, i) => {
        console.log(`${i + 1}. ${u.name} (${u.email})`);
      });
    }

    // Test API endpoints without authentication
    console.log('\n2️⃣ Testing API endpoints without authentication...');
    const baseURL = 'http://localhost:5000';

    const endpoints = [
      '/api/syllabus',
      '/api/syllabus/subjects/primary',
      '/api/ai-questions/subjects/primary'
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${baseURL}${endpoint}`);
        console.log(`❌ ${endpoint} - Should require authentication but returned ${response.status}`);
      } catch (error) {
        if (error.response && error.response.status === 401) {
          console.log(`✅ ${endpoint} - Correctly requires authentication (401)`);
        } else {
          console.log(`❌ ${endpoint} - Unexpected error: ${error.response?.status} ${error.message}`);
        }
      }
    }

    // Test with invalid token
    console.log('\n3️⃣ Testing API endpoints with invalid token...');
    const invalidToken = 'Bearer invalid-token-12345';

    for (const endpoint of endpoints) {
      try {
        const response = await axios.get(`${baseURL}${endpoint}`, {
          headers: { 'Authorization': invalidToken }
        });
        console.log(`❌ ${endpoint} - Should reject invalid token but returned ${response.status}`);
      } catch (error) {
        if (error.response && error.response.status === 401) {
          console.log(`✅ ${endpoint} - Correctly rejects invalid token (401)`);
        } else {
          console.log(`❌ ${endpoint} - Unexpected error: ${error.response?.status} ${error.message}`);
        }
      }
    }

    // Check if server is responding to basic requests
    console.log('\n4️⃣ Testing server health...');
    try {
      const response = await axios.get(`${baseURL}/api/users/get-user-info`, {
        headers: { 'Authorization': invalidToken }
      });
      console.log('❌ Should reject invalid token');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Server is running and handling authentication correctly');
      } else {
        console.log(`❌ Server issue: ${error.response?.status} ${error.message}`);
      }
    }

    console.log('\n📋 API Authentication Test Summary:');
    console.log('✅ Database connection working');
    console.log('✅ Data exists in database');
    console.log('✅ API endpoints exist and require authentication');
    console.log('✅ Server is running and responding');
    
    console.log('\n🔧 Possible Issues:');
    console.log('1. Frontend not sending authentication token');
    console.log('2. Token format or validation issue');
    console.log('3. CORS or network connectivity issue');
    console.log('4. Frontend error handling not showing real error');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from database');
  }
}

// Test specific syllabus queries
async function testSyllabusQueries() {
  console.log('\n📚 Testing Syllabus Database Queries...\n');

  try {
    await mongoose.connect(process.env.MONGO_URL);
    const Syllabus = require('./models/syllabusModel');

    // Test different query scenarios
    console.log('1️⃣ Testing query scenarios...');
    
    // Query 1: All syllabuses
    const allSyllabuses = await Syllabus.find({});
    console.log(`All syllabuses: ${allSyllabuses.length}`);

    // Query 2: Active syllabuses only
    const activeSyllabuses = await Syllabus.find({ isActive: true });
    console.log(`Active syllabuses: ${activeSyllabuses.length}`);

    // Query 3: Completed syllabuses only
    const completedSyllabuses = await Syllabus.find({ processingStatus: 'completed' });
    console.log(`Completed syllabuses: ${completedSyllabuses.length}`);

    // Query 4: Active AND completed
    const activeCompletedSyllabuses = await Syllabus.find({ 
      isActive: true, 
      processingStatus: 'completed' 
    });
    console.log(`Active AND completed syllabuses: ${activeCompletedSyllabuses.length}`);

    // Query 5: Default query (what the API uses)
    const defaultQuery = await Syllabus.find({ isActive: true })
      .populate("uploadedBy", "name email")
      .populate("approvedBy", "name email")
      .sort({ createdAt: -1 });
    console.log(`Default API query result: ${defaultQuery.length}`);

    if (defaultQuery.length > 0) {
      console.log('\nDefault query results:');
      defaultQuery.forEach((s, i) => {
        console.log(`${i + 1}. ${s.title}`);
        console.log(`   Level: ${s.level}, Classes: ${s.classes?.join(', ') || 'N/A'}`);
        console.log(`   Subject: ${s.subject}`);
        console.log(`   Status: ${s.processingStatus}, Active: ${s.isActive}`);
        console.log(`   UploadedBy: ${s.uploadedBy ? 'Populated' : 'Not populated'}`);
        console.log('');
      });
    }

    // Test subjects query
    console.log('2️⃣ Testing subjects query...');
    const primarySubjects = await Syllabus.getAvailableSubjects('primary');
    console.log(`Primary subjects: [${primarySubjects.join(', ')}]`);

    const secondarySubjects = await Syllabus.getAvailableSubjects('secondary');
    console.log(`Secondary subjects: [${secondarySubjects.join(', ')}]`);

    const advanceSubjects = await Syllabus.getAvailableSubjects('advance');
    console.log(`Advance subjects: [${advanceSubjects.join(', ')}]`);

  } catch (error) {
    console.error('❌ Syllabus query test failed:', error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting API Authentication and Data Tests\n');
  console.log('=' .repeat(60));
  
  await testAPIAuth();
  await testSyllabusQueries();
  
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 All tests completed!');
  
  console.log('\n🔧 Next Steps for Debugging:');
  console.log('1. Check browser console for authentication errors');
  console.log('2. Check if user is logged in and has valid token');
  console.log('3. Check network tab for API request details');
  console.log('4. Verify token format and expiration');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testAPIAuth,
  testSyllabusQueries,
  runAllTests
};
