const mongoose = require('mongoose');
require('dotenv').config();
const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');

async function testAIImprovements() {
  try {
    console.log('🧪 Testing AI Improvements: Subject Alignment & Topic Diversity...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    const aiService = new AIQuestionGenerationService();
    
    // Test 1: Subject validation
    console.log('🔍 Testing Subject Validation...');
    const scienceQuestion = "What are the main parts of a plant?";
    const mathQuestion = "What is 2 + 2?";
    const mixedQuestion = "Calculate the number of leaves on a tree";
    
    console.log(`Science question validation: ${aiService.validateSubjectAlignment(scienceQuestion, 'Science and Technology')}`);
    console.log(`Math question in Science: ${aiService.validateSubjectAlignment(mathQuestion, 'Science and Technology')}`);
    console.log(`Mixed question validation: ${aiService.validateSubjectAlignment(mixedQuestion, 'Science and Technology')}`);
    
    // Test 2: Topic extraction
    console.log('\n📚 Testing Topic Extraction...');
    const syllabusData = await aiService.getSyllabusData(
      'primary',
      '3',
      'Science and Technology',
      '6865aa99b4448a485a03b86f'
    );
    
    const availableTopics = aiService.extractAvailableTopics(syllabusData);
    console.log(`Available topics: ${availableTopics.slice(0, 5).join(', ')}... (${availableTopics.length} total)`);
    
    // Test 3: Topic diversity selection
    console.log('\n🎯 Testing Topic Diversity...');
    const usedTopics = new Map();
    const selectedTopics = [];
    
    for (let i = 0; i < 10; i++) {
      const topic = aiService.selectDiverseTopic(availableTopics, usedTopics, 10);
      selectedTopics.push(topic);
      usedTopics.set(topic, (usedTopics.get(topic) || 0) + 1);
    }
    
    console.log('Selected topics for 10 questions:');
    selectedTopics.forEach((topic, index) => {
      console.log(`${index + 1}. ${topic}`);
    });
    
    console.log('\nTopic usage distribution:');
    usedTopics.forEach((count, topic) => {
      console.log(`${topic}: ${count} times`);
    });
    
    // Test 4: Enhanced prompt generation
    console.log('\n📝 Testing Enhanced Prompt Generation...');
    const enhancedPrompt = await aiService.buildMultipleChoicePrompt(
      'primary',
      '3',
      'Science and Technology',
      'medium',
      ['Plants'],
      '6865aa99b4448a485a03b86f'
    );
    
    console.log('Enhanced prompt features:');
    console.log(`- Contains subject restriction: ${enhancedPrompt.includes('ABSOLUTELY NO OTHER SUBJECTS') ? 'Yes' : 'No'}`);
    console.log(`- Contains forbidden content list: ${enhancedPrompt.includes('FORBIDDEN CONTENT:') ? 'Yes' : 'No'}`);
    console.log(`- Contains topic diversity requirement: ${enhancedPrompt.includes('TOPIC DIVERSITY REQUIREMENT:') ? 'Yes' : 'No'}`);
    console.log(`- Contains strict guidelines: ${enhancedPrompt.includes('STRICT GUIDELINES:') ? 'Yes' : 'No'}`);
    
    // Test 5: Question uniqueness
    console.log('\n🔑 Testing Question Uniqueness...');
    const question1 = "What is photosynthesis?";
    const question2 = "What is photosynthesis!";
    const question3 = "How do plants make food?";
    
    const key1 = aiService.generateQuestionKey(question1);
    const key2 = aiService.generateQuestionKey(question2);
    const key3 = aiService.generateQuestionKey(question3);
    
    console.log(`Question 1 key: "${key1}"`);
    console.log(`Question 2 key: "${key2}"`);
    console.log(`Question 3 key: "${key3}"`);
    console.log(`Keys 1&2 same (should be true): ${key1 === key2}`);
    console.log(`Keys 1&3 different (should be true): ${key1 !== key3}`);
    
    console.log('\n🎉 AI Improvements Test Complete!');
    console.log('\n📋 Summary of Enhancements:');
    console.log('✅ Subject validation system to prevent mixing');
    console.log('✅ Topic diversity selection for varied questions');
    console.log('✅ Enhanced prompts with strict subject restrictions');
    console.log('✅ Forbidden content lists to prevent contamination');
    console.log('✅ Question uniqueness tracking system');
    console.log('✅ Topic usage distribution for balanced coverage');
    
    const validationWorking = aiService.validateSubjectAlignment(scienceQuestion, 'Science and Technology') && 
                             !aiService.validateSubjectAlignment(mathQuestion, 'Science and Technology');
    const topicDiversityWorking = new Set(selectedTopics).size > 1;
    const promptEnhanced = enhancedPrompt.includes('ABSOLUTELY NO OTHER SUBJECTS');
    
    if (validationWorking && topicDiversityWorking && promptEnhanced) {
      console.log('\n🎯 All AI improvements working correctly!');
      console.log('The system should now generate more focused, diverse, and subject-specific questions.');
    } else {
      console.log('\n⚠️ Some improvements may need additional refinement.');
      console.log(`Validation: ${validationWorking}, Diversity: ${topicDiversityWorking}, Prompts: ${promptEnhanced}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

testAIImprovements();
