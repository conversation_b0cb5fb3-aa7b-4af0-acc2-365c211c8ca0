const mongoose = require('mongoose');
const Plan = require('./models/planModel');

async function addGlimpPlan() {
  try {
    // Connect to MongoDB using the same connection string as the main app
    await mongoose.connect('mongodb://hvmgeeks:<EMAIL>:27017,ac-4ojvv6y-shard-00-01.cdg8fdn.mongodb.net:27017,ac-4ojvv6y-shard-00-02.cdg8fdn.mongodb.net:27017/stjoseph?ssl=true&replicaSet=atlas-fsgg6f-shard-0&authSource=admin&retryWrites=true&w=majority&appName=Cluster0');
    console.log('✅ Connected to MongoDB');
    
    // Check if Glimp plan already exists
    const existingPlan = await Plan.findOne({ title: 'Glimp Plan' });
    if (existingPlan) {
      console.log('⚠️ Glimp Plan already exists:', existingPlan.title);
      console.log('📋 Existing plan details:');
      console.log(`   - Duration: ${existingPlan.duration} month(s)`);
      console.log(`   - Price: ${existingPlan.discountedPrice} TZS`);
      console.log(`   - Status: ${existingPlan.status}`);
    } else {
      // Create the Glimp plan
      const glimpPlan = new Plan({
        title: 'Glimp Plan',
        features: [
          '1-month full access',
          'Unlimited quizzes',
          'Personalized profile',
          'AI chat for instant help',
          'Forum for student discussions',
          'Study notes',
          'Past papers',
          'Books',
          'Learning videos',
          'Track progress with rankings'
        ],
        actualPrice: 15000,
        discountedPrice: 13000,
        discountPercentage: 13,
        duration: 1,
        status: true,
      });
      
      const savedPlan = await glimpPlan.save();
      console.log('✅ Glimp Plan created successfully!');
      console.log('📋 New plan details:');
      console.log(`   - ID: ${savedPlan._id}`);
      console.log(`   - Title: ${savedPlan.title}`);
      console.log(`   - Duration: ${savedPlan.duration} month(s)`);
      console.log(`   - Price: ${savedPlan.discountedPrice} TZS`);
    }
    
    // List all plans to verify
    const allPlans = await Plan.find({ status: true }).sort({ duration: 1 });
    console.log('\n📋 All active plans in database:');
    allPlans.forEach((plan, index) => {
      console.log(`${index + 1}. ${plan.title}`);
      console.log(`   - Duration: ${plan.duration} month(s)`);
      console.log(`   - Price: ${plan.discountedPrice} TZS`);
      console.log(`   - Features: ${plan.features.length} items`);
      console.log('');
    });
    
    await mongoose.connection.close();
    console.log('✅ Database connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

addGlimpPlan();
