const express = require('express');
const cors = require('cors');
require('dotenv').config();

// Create a simple webhook listener to debug what ZenoPay is actually sending
const app = express();

app.use(cors());
app.use(express.json());
app.use(express.raw({ type: 'application/json' }));

// Log all incoming requests to the webhook endpoint
app.post('/api/payment/webhook', (req, res) => {
  console.log('\n🔔 WEBHOOK RECEIVED!');
  console.log('=' .repeat(50));
  console.log('📅 Timestamp:', new Date().toISOString());
  console.log('📥 Headers:', JSON.stringify(req.headers, null, 2));
  console.log('📦 Body:', JSON.stringify(req.body, null, 2));
  console.log('📦 Raw Body:', req.body);
  console.log('🔍 Content-Type:', req.headers['content-type']);
  console.log('🔑 x-api-key:', req.headers['x-api-key']);
  console.log('🔑 Expected API Key:', process.env.ZENOPAY_API_KEY);
  console.log('✅ API Key Match:', req.headers['x-api-key'] === process.env.ZENOPAY_API_KEY);
  console.log('=' .repeat(50));
  
  // Always respond with success to avoid webhook retries
  res.status(200).json({
    success: true,
    message: 'Webhook received and logged',
    timestamp: new Date().toISOString()
  });
});

// Test endpoint
app.get('/api/payment/webhook-test', (req, res) => {
  res.json({
    success: true,
    message: 'Webhook debug server is running',
    timestamp: new Date().toISOString(),
    expectedApiKey: process.env.ZENOPAY_API_KEY ? 'Set' : 'Not Set'
  });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`🔍 Webhook Debug Server running on port ${PORT}`);
  console.log(`🔗 Webhook URL: http://localhost:${PORT}/api/payment/webhook`);
  console.log(`🧪 Test URL: http://localhost:${PORT}/api/payment/webhook-test`);
  console.log(`🔑 Expected API Key: ${process.env.ZENOPAY_API_KEY ? 'Set' : 'Not Set'}`);
  console.log('\n⏳ Waiting for webhooks...\n');
});
