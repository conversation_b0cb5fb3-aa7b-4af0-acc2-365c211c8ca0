const mongoose = require('mongoose');
require('dotenv').config();

async function fixBiologyExamTopicV2() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Direct database update
        const db = mongoose.connection.db;
        
        // Find the BS1-Q1 exam
        console.log('\n🔍 Looking for BS1-Q1 exam...');
        
        const biologyExam = await db.collection('exams').findOne({ name: 'BS1-Q1' });

        if (!biologyExam) {
            console.log('❌ BS1-Q1 exam not found');
            return;
        }

        console.log('📝 Found BS1-Q1 exam:');
        console.log(`  Name: ${biologyExam.name}`);
        console.log(`  Subject: ${biologyExam.subject}`);
        console.log(`  Category: ${biologyExam.category}`);
        console.log(`  Topic: ${biologyExam.topic}`);
        console.log(`  Level: ${biologyExam.level}`);
        console.log(`  Class: ${biologyExam.class}`);

        // Update the exam directly
        console.log('\n🔧 Updating exam topic directly...');
        
        const updateResult = await db.collection('exams').updateOne(
            { name: 'BS1-Q1' },
            { 
                $set: {
                    topic: 'Cell Biology',
                    subject: 'Biology'
                }
            }
        );

        console.log('Update result:', updateResult);

        // Verify the update
        const updatedExam = await db.collection('exams').findOne({ name: 'BS1-Q1' });
        
        if (updatedExam) {
            console.log('✅ Verification - Updated BS1-Q1 exam:');
            console.log(`  Name: ${updatedExam.name}`);
            console.log(`  Subject: ${updatedExam.subject}`);
            console.log(`  Category: ${updatedExam.category}`);
            console.log(`  Topic: ${updatedExam.topic}`);
            console.log(`  Level: ${updatedExam.level}`);
            console.log(`  Class: ${updatedExam.class}`);
        }

        console.log('\n✅ Fix completed!');
        
    } catch (error) {
        console.error('❌ Error fixing biology exam:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    fixBiologyExamTopicV2();
}

module.exports = fixBiologyExamTopicV2;
