const router = require("express").Router();
const ForumQuestion = require("../models/forumQuestionModel");
const authMiddleware = require("../middlewares/authMiddleware");
const User = require("../models/userModel");
const NotificationService = require("../services/notificationService");

// add question in the forum

router.post("/add-question", authMiddleware, async (req, res) => {
    const userId = req.body.userId; // Assuming userId is retrieved from the authMiddleware

    // Fetch the requesting user's details
    const user = await User.findById(userId).select('level name');

    if (!user) {
        return res.status(404).send({
            message: "User not found",
            success: false,
        });
    }

    const userLevel = user.level;
    console.log('🎓 Forum Question Creation Debug:', {
        userId: userId,
        userName: user.name,
        userLevel: userLevel,
        userLevelType: typeof userLevel,
        timestamp: new Date().toISOString()
    });

    try {
        const { title, body, userId } = req.body;

        // Ensure level is properly set and normalized
        const normalizedLevel = userLevel ? userLevel.toLowerCase() : 'primary';

        const newForumQuestion = new ForumQuestion({
            title,
            body,
            user: userId,
            level: normalizedLevel
        });

        console.log('💾 Saving forum question with level:', normalizedLevel);
        await newForumQuestion.save();

        console.log('✅ Forum question saved successfully:', {
            questionId: newForumQuestion._id,
            level: newForumQuestion.level,
            title: newForumQuestion.title.substring(0, 50) + '...'
        });

        // Populate user data for immediate response
        await newForumQuestion.populate('user', 'name');

        // Send notification about new forum question
        try {
            console.log('🔔 Sending new forum question notification for:', newForumQuestion.title);
            await NotificationService.notifyNewForumQuestion(newForumQuestion, userId);
            console.log('✅ New forum question notification sent successfully');
        } catch (notifError) {
            console.error('❌ Error sending forum question notification:', notifError);
            // Don't fail the question creation if notification fails
        }

        res.send({
            message: "question added successfully",
            success: true,
            data: newForumQuestion
        });
    } catch (error) {
        console.error('❌ Forum question creation error:', error);
        res.status(500).send({
            message: error.message,
            data: error,
            success: false,
        });
    }
});

// add reply to the question in the forum

router.post("/add-reply", authMiddleware, async (req, res) => {
    const userId = req.body.userId; // Assuming userId is retrieved from the authMiddleware

    try {
        const { text, questionId, userId, isAI } = req.body;

        // Fetch user and question in parallel for better performance
        const [user, question] = await Promise.all([
            User.findById(userId).select('level name'),
            ForumQuestion.findById(questionId)
        ]);

        if (!user) {
            return res.status(404).send({
                message: "User not found",
                success: false,
            });
        }

        if (!question) {
            return res.status(404).send({
                message: "Question not found",
                success: false,
            });
        }

        const userLevel = user.level;
        const now = new Date();
        const replyData = {
            text,
            level: userLevel,
            createdAt: now,
            updatedAt: now
        };

        // Handle AI responses differently
        if (isAI) {
            replyData.isAI = true;
            replyData.aiAuthor = "Brainwave AI";
            replyData.user = null; // No user for AI responses
        } else {
            replyData.user = userId;
            replyData.isAI = false;
        }

        question.replies.push(replyData);
        await question.save();

        res.send({
            message: "reply added successfully",
            success: true,
            data: {
                questionId,
                reply: replyData
            }
        });
    } catch (error) {
        res.status(500).send({
            message: error.message,
            data: error,
            success: false,
        });
    }
});

// get all questions

router.get("/get-all-questions", authMiddleware, async (req, res) => {
    const startTime = performance.now();
    const userId = req.body.userId; // Assuming userId is retrieved from the authMiddleware

    // Fetch the requesting user's details
    const user = await User.findById(userId);

    if (!user) {
        return res.status(404).send({
            message: "User not found",
            success: false,
        });
    }

    const userLevel = user.level ? user.level.toLowerCase() : 'primary'; // Normalize to lowercase
    const isAdmin = user.isAdmin;

    console.log('🔍 Forum Questions Retrieval Debug:', {
        userId: userId,
        userName: user.name,
        userLevel: userLevel,
        originalLevel: user.level,
        isAdmin: isAdmin,
        timestamp: new Date().toISOString()
    });

    try {
        // Extract page and limit from query parameters, with default values
        const page = parseInt(req.query.page) || 1; // Default to page 1
        const limit = parseInt(req.query.limit) || 10; // Default to 10 questions per page

        // Calculate the starting index for pagination
        const skip = (page - 1) * limit;

        let filter = {};

        // Define the filter condition based on level with case-insensitive matching
        if (userLevel === "secondary") {
            filter = {
                $or: [
                    { level: "secondary" },
                    { level: "Secondary" }
                ]
            };
        } else if (userLevel === "advance") {
            filter = {
                $or: [
                    { level: "advance" },
                    { level: "Advance" }
                ]
            };
        } else {
            // Primary level - exclude secondary and advance (both cases)
            filter = {
                level: {
                    $nin: ["secondary", "advance", "Secondary", "Advance"]
                }
            };
        }

        if (isAdmin) {
            filter = {};
        }

        console.log('🎯 Forum filter for level', userLevel, ':', JSON.stringify(filter, null, 2));

        // Debug: Check if there are ANY advance level questions in the database
        if (userLevel === 'advance') {
            const allAdvanceQuestions = await ForumQuestion.find({
                $or: [
                    { level: "advance" },
                    { level: "Advance" }
                ]
            }).select('_id level title createdAt user');

            console.log('🔍 All advance questions in database:', {
                count: allAdvanceQuestions.length,
                questions: allAdvanceQuestions.map(q => ({
                    id: q._id,
                    level: q.level,
                    title: q.title.substring(0, 50),
                    createdAt: q.createdAt,
                    userId: q.user
                }))
            });
        }

        // Fetch questions with the defined filter and pagination
        const questions = await ForumQuestion
            .find(filter)
            .populate("user")
            .populate("replies.user")
            .sort({createdAt: -1})
            .skip(skip)
            .limit(limit);

        // Get the total count of questions matching the filter
        const totalQuestions = await ForumQuestion.countDocuments(filter);

        console.log('📊 Forum retrieval results:', {
            userLevel: userLevel,
            questionsFound: questions.length,
            totalQuestions: totalQuestions,
            page: page,
            limit: limit,
            questionLevels: questions.map(q => ({ id: q._id, level: q.level, title: q.title.substring(0, 30) }))
        });

        const endTime = performance.now();
        // console.log("Time taken in milliseconds by forum endpoint", endTime - startTime);

        res.send({
            message: "Questions fetched successfully",
            data: questions,
            currentPage: page,
            totalPages: Math.ceil(totalQuestions / limit),
            totalQuestions: totalQuestions,
            success: true,
        });
    } catch (error) {
        res.status(500).send({
            message: error.message,
            data: error,
            success: false,
        });
    }
});



router.delete("/delete-question/:questionId", authMiddleware, async (req, res) => {
    try {
        const { questionId } = req.params;
        const deletedQuestion = await ForumQuestion.findByIdAndDelete(questionId);
        if (!deletedQuestion) {
            return res.send({
                message: "Unable to delete question",
                success: false,
            });
        }
        res.send({
            message: "question deleted successfully",
            success: true,
        });
    } catch (error) {
        res.status(500).send({
            message: error.message,
            data: error,
            success: false,
        });
    }
});

router.put("/update-question/:questionId", authMiddleware, async (req, res) => {
    const userId = req.body.userId; // Assuming userId is retrieved from the authMiddleware

    // Fetch the requesting user's details
    const user = await User.findById(userId);

    if (!user) {
        return res.status(404).send({
            message: "User not found",
            success: false,
        });
    }

    const userLevel = user.level;

    try {
        const { questionId } = req.params;
        const { title, body } = req.body;

        const updatedQuestion = await ForumQuestion.findByIdAndUpdate(questionId, { title, body, level: userLevel });
        if (!updatedQuestion) {
            return res.send({
                message: "Unable to update question",
                success: false,
            });
        }
        res.send({
            message: "question updated successfully",
            success: true,
        });
    } catch (error) {
        res.status(500).send({
            message: error.message,
            data: error,
            success: false,
        });
    }
});

router.put("/update-reply-status/:questionId", authMiddleware, async (req, res) => {
    try {
        const { questionId } = req.params;
        const { replyId, status } = req.body;
        if (!questionId || !replyId || typeof (status) !== 'boolean') {
            return res.status(400).send({
                success: false,
                message: "QuestionId, replyId and status are required."
            });
        }
        const question = await ForumQuestion.findById(questionId);
        if (!question) {
            return res.status(404).send({
                success: false,
                message: "Question not found."
            });
        }
        const reply = question.replies.find(r => r._id.equals(replyId));
        if (!reply) {
            return res.status(404).send({
                success: false,
                message: "Reply not found."
            });
        }
        reply.isVerified = status;
        await question.save();
        return res.status(200).send({
            success: true,
            message: "Reply status updated successfully."
        });
    } catch (error) {
        res.status(500).send({
            message: error.message,
            data: error,
            success: false,
        });
    }
});

module.exports = router;