/* Modern Quiz Components Styles */

/* Quiz Card Animations */
@keyframes cardHover {
  0% { transform: translateY(0) scale(1); }
  100% { transform: translateY(-4px) scale(1.02); }
}

@keyframes progressFill {
  0% { width: 0%; }
  100% { width: var(--progress-width); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Quiz Dashboard Grid */
.quiz-dashboard-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* Responsive breakpoints for quiz cards */
@media (max-width: 640px) {
  .quiz-dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .quiz-dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .quiz-dashboard-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1025px) {
  .quiz-dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}

/* Quiz Card Hover Effects */
.quiz-card-modern {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quiz-card-modern:hover {
  animation: cardHover 0.3s ease-out forwards;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Status Badge Styles */
.status-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 10;
  border-radius: 9999px;
  padding: 4px 12px;
  font-size: 0.75rem;
  font-weight: 700;
  color: white;
  backdrop-filter: blur(8px);
}

.status-badge.not-attempted {
  background-color: rgba(59, 130, 246, 0.9);
}

.status-badge.passed {
  background-color: rgba(34, 197, 94, 0.9);
}

.status-badge.failed {
  background-color: rgba(239, 68, 68, 0.9);
}

/* Progress Bar Animation */
.progress-bar {
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 9999px;
  height: 8px;
}

/* Timer Styles */
.quiz-timer {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 700;
  letter-spacing: 0.05em;
}

.quiz-timer.warning {
  animation: pulse 1s infinite;
  color: #dc2626;
}

/* Question Navigation */
.question-nav-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.question-nav-button.current {
  background-color: #3b82f6;
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.question-nav-button.answered {
  background-color: #dcfce7;
  color: #16a34a;
}

.question-nav-button.unanswered {
  background-color: #f3f4f6;
  color: #6b7280;
}

.question-nav-button:hover {
  transform: scale(1.1);
}

/* Answer Option Styles */
.answer-option {
  transition: all 0.2s ease;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  background-color: white;
}

.answer-option:hover {
  border-color: #d1d5db;
  background-color: #f9fafb;
  transform: translateY(-1px);
}

.answer-option.selected {
  border-color: #3b82f6;
  background-color: #eff6ff;
  color: #1e40af;
}

.answer-option-letter {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
  border: 2px solid #d1d5db;
  color: #6b7280;
  transition: all 0.2s ease;
}

.answer-option.selected .answer-option-letter {
  border-color: #3b82f6;
  background-color: #3b82f6;
  color: white;
}

/* Loading States */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Modal Animations */
.modal-backdrop {
  backdrop-filter: blur(4px);
}

.modal-content {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .quiz-card-modern {
    background-color: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .answer-option {
    background-color: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .answer-option:hover {
    background-color: #111827;
    border-color: #4b5563;
  }
}

/* Accessibility */
.quiz-card-modern:focus,
.answer-option:focus,
.question-nav-button:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print Styles */
@media print {
  .quiz-timer,
  .question-nav-button,
  .status-badge {
    display: none;
  }
  
  .quiz-card-modern {
    box-shadow: none;
    border: 1px solid #000;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .quiz-card-modern {
    border-width: 3px;
  }
  
  .answer-option {
    border-width: 3px;
  }
  
  .status-badge {
    border: 2px solid white;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .quiz-card-modern,
  .answer-option,
  .question-nav-button,
  .progress-bar {
    transition: none;
    animation: none;
  }
  
  .quiz-card-modern:hover {
    transform: none;
    animation: none;
  }
}
