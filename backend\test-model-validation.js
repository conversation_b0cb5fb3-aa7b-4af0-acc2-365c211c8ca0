// Simple test for model validation without database connection
console.log('🧪 Testing Syllabus Model Validation...\n');

try {
  const Syllabus = require('./models/syllabusModel');
  
  console.log('✅ Syllabus model loaded successfully');
  
  // Test 1: Check schema structure
  console.log('\n1️⃣ Testing schema structure...');
  const schema = Syllabus.schema;
  
  console.log('Schema paths:');
  console.log('- extractedText required:', schema.paths.extractedText.isRequired);
  console.log('- uploadedBy required:', schema.paths.uploadedBy.isRequired);
  console.log('- classes type:', schema.paths.classes.instance);
  
  // Test 2: Check if validation would pass
  console.log('\n2️⃣ Testing validation rules...');
  
  const testData = {
    title: 'Test Syllabus',
    level: 'primary',
    classes: ['5', '6'],
    subject: 'Mathematics',
    fileName: 'test.pdf',
    originalFileName: 'test.pdf',
    filePath: '/test/path.pdf',
    fileSize: 1000,
    mimeType: 'application/pdf',
    processingStatus: 'pending',
    // extractedText and uploadedBy not provided
  };
  
  const testSyllabus = new Syllabus(testData);
  
  console.log('Test syllabus created with:');
  console.log('- extractedText:', `"${testSyllabus.extractedText}"`);
  console.log('- uploadedBy:', testSyllabus.uploadedBy);
  console.log('- classes:', testSyllabus.classes);
  
  // Test validation without saving
  const validationError = testSyllabus.validateSync();
  if (validationError) {
    console.log('❌ Validation errors:', validationError.message);
  } else {
    console.log('✅ Validation passed - no errors');
  }
  
  console.log('\n🎉 Model validation test completed!');
  
} catch (error) {
  console.error('❌ Test failed:', error.message);
}
