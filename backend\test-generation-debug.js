const mongoose = require('mongoose');
require('dotenv').config();
const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');

async function debugQuestionGeneration() {
  try {
    console.log('🔍 Debugging Question Generation Failure...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    // Check environment variables
    console.log('🔧 Checking Environment Variables...');
    console.log(`- OpenAI API Key: ${process.env.OPENAI_API_KEY ? 'Present' : 'Missing'}`);
    console.log(`- MongoDB URL: ${process.env.MONGO_URL ? 'Present' : 'Missing'}`);
    console.log('');
    
    // Test AI service initialization
    console.log('🤖 Testing AI Service...');
    const aiService = new AIQuestionGenerationService();
    console.log('✅ AI Service initialized successfully\n');
    
    // Test basic parameters
    const testParams = {
      questionTypes: ["multiple_choice"],
      subjects: ["Science and Technology"],
      level: "primary",
      class: "3",
      difficultyLevels: ["medium"],
      syllabusTopics: [],
      totalQuestions: 2,
      questionDistribution: {
        multiple_choice: 2,
        fill_blank: 0,
        picture_based: 0
      },
      selectedSyllabusId: null
    };
    
    console.log('📋 Test Parameters:');
    console.log(JSON.stringify(testParams, null, 2));
    console.log('');
    
    // Test syllabus data retrieval
    console.log('📚 Testing Syllabus Data Retrieval...');
    try {
      const syllabusData = await aiService.getSyllabusData(
        testParams.level,
        testParams.class,
        testParams.subjects[0],
        testParams.selectedSyllabusId
      );
      console.log(`✅ Syllabus data retrieved: ${syllabusData.source}`);
      console.log(`- Has topics: ${syllabusData.topics ? 'Yes' : 'No'}`);
      console.log(`- Has extracted text: ${syllabusData.extractedText ? 'Yes' : 'No'}`);
    } catch (error) {
      console.log(`❌ Syllabus data error: ${error.message}`);
    }
    console.log('');
    
    // Test prompt building
    console.log('📝 Testing Prompt Building...');
    try {
      const prompt = await aiService.buildMultipleChoicePrompt(
        testParams.level,
        testParams.class,
        testParams.subjects[0],
        testParams.difficultyLevels[0],
        testParams.syllabusTopics,
        testParams.selectedSyllabusId
      );
      console.log(`✅ Prompt built successfully (${prompt.length} characters)`);
    } catch (error) {
      console.log(`❌ Prompt building error: ${error.message}`);
    }
    console.log('');
    
    // Test OpenAI API call
    console.log('🌐 Testing OpenAI API Call...');
    try {
      const testPrompt = "Generate a simple test response in JSON format: {\"test\": \"success\"}";
      const response = await aiService.callOpenAI(testPrompt);
      console.log(`✅ OpenAI API call successful`);
      console.log(`Response type: ${typeof response}`);
    } catch (error) {
      console.log(`❌ OpenAI API error: ${error.message}`);
      if (error.response) {
        console.log(`Status: ${error.response.status}`);
        console.log(`Data: ${JSON.stringify(error.response.data)}`);
      }
    }
    console.log('');
    
    // Test question generation (single question)
    console.log('🎯 Testing Single Question Generation...');
    try {
      const questions = await aiService.generateMultipleChoiceQuestions(testParams, 1);
      console.log(`✅ Generated ${questions.length} question(s)`);
      if (questions.length > 0) {
        console.log('Sample question:');
        console.log(`- Name: ${questions[0].name?.substring(0, 50)}...`);
        console.log(`- Topic: ${questions[0].topic}`);
        console.log(`- Class Level: ${questions[0].classLevel}`);
        console.log(`- Type: ${questions[0].type}`);
      }
    } catch (error) {
      console.log(`❌ Question generation error: ${error.message}`);
      console.log(`Stack: ${error.stack}`);
    }
    console.log('');
    
    // Test full generation process
    console.log('🚀 Testing Full Generation Process...');
    try {
      // Use valid ObjectIds for testing
      const validUserId = new mongoose.Types.ObjectId();
      const validExamId = new mongoose.Types.ObjectId();

      console.log(`Using valid ObjectIds: userId=${validUserId}, examId=${validExamId}`);

      const result = await aiService.generateQuestions(
        testParams,
        validUserId,
        validExamId
      );
      
      if (result.success) {
        console.log(`✅ Full generation successful!`);
        console.log(`- Generation ID: ${result.generationId}`);
        console.log(`- Questions generated: ${result.questions?.length || 0}`);
        console.log(`- Generation time: ${result.generationTime}ms`);
      } else {
        console.log(`❌ Full generation failed: ${result.error}`);
      }
    } catch (error) {
      console.log(`❌ Full generation error: ${error.message}`);
      console.log(`Stack: ${error.stack}`);
    }
    
    console.log('\n🎉 Debug Test Complete!');
    
  } catch (error) {
    console.error('❌ Debug test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

debugQuestionGeneration();
