const express = require("express");
const mongoose = require('mongoose');
require("dotenv").config();

const app = express();
const port = 5001; // Use different port

app.use(express.json());

// Simple test route
app.get('/test', (req, res) => {
  res.json({ message: 'Server is working!', timestamp: new Date() });
});

// Test syllabus route
app.get('/test-syllabus', async (req, res) => {
  try {
    const Syllabus = require('./models/syllabusModel');
    const syllabuses = await Syllabus.find({ isActive: true });
    res.json({ 
      message: 'Syllabus test successful', 
      count: syllabuses.length,
      data: syllabuses.map(s => ({
        title: s.title,
        level: s.level,
        classes: s.classes,
        subject: s.subject,
        status: s.processingStatus
      }))
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Connect to MongoDB and start server
async function startServer() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ MongoDB connected successfully');
    
    app.listen(port, () => {
      console.log(`🚀 Test server running on port ${port}`);
      console.log(`📍 Test endpoints:`);
      console.log(`   http://localhost:${port}/test`);
      console.log(`   http://localhost:${port}/test-syllabus`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error.message);
    process.exit(1);
  }
}

startServer();
