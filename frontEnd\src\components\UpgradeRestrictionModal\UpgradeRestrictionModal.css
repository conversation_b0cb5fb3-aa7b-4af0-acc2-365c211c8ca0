/* Upgrade Restriction Modal Styles */
.upgrade-restriction-modal .ant-modal-content {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.upgrade-restriction-modal .ant-modal-body {
  padding: 0;
}

.upgrade-restriction-modal .ant-modal-close {
  top: 15px;
  right: 15px;
  color: white;
  font-size: 18px;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.upgrade-restriction-modal .ant-modal-close:hover {
  opacity: 1;
  transform: scale(1.1);
}

.upgrade-restriction-content {
  background: white;
  border-radius: 20px;
  margin: 3px;
  overflow: hidden;
}

/* Header Section */
.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px 30px 25px;
  text-align: center;
  color: white;
  position: relative;
}

.crown-icon {
  font-size: 48px;
  color: #ffd700;
  margin-bottom: 15px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.modal-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.modal-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

/* Current Plan Card */
.current-plan-card {
  margin: 25px;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  border-radius: 16px;
  padding: 25px;
  border: 2px solid #e6f0ff;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.1);
}

.plan-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.plan-icon {
  font-size: 24px;
  color: #52c41a;
  margin-right: 15px;
  background: rgba(82, 196, 26, 0.1);
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plan-info {
  flex: 1;
}

.plan-name {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 4px 0;
}

.plan-status {
  color: #52c41a;
  font-size: 14px;
  font-weight: 500;
  background: rgba(82, 196, 26, 0.1);
  padding: 4px 12px;
  border-radius: 20px;
}

/* Progress Section */
.progress-section {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.days-remaining {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
  padding: 4px 10px;
  border-radius: 12px;
}

/* Subscription Details */
.subscription-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 10px;
  border: 1px solid #e8f2ff;
}

.detail-icon {
  font-size: 18px;
  color: #1890ff;
  margin-right: 12px;
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.detail-value {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

/* Message Section */
.message-section {
  margin: 0 25px 25px;
}

.message-card {
  background: linear-gradient(135deg, #fff7e6 0%, #fff2d9 100%);
  border: 2px solid #ffd591;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  text-align: center;
}

.message-title {
  font-size: 18px;
  font-weight: 600;
  color: #d46b08;
  margin: 0 0 10px 0;
}

.message-text {
  font-size: 14px;
  color: #8c4a00;
  margin: 0;
  line-height: 1.6;
}

/* Benefits List */
.benefits-list {
  background: #f9f9f9;
  border-radius: 12px;
  padding: 20px;
}

.benefits-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 15px 0;
}

.benefits {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefit-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  font-size: 14px;
  color: #333;
}

.benefit-icon {
  color: #52c41a;
  margin-right: 10px;
  font-size: 16px;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 12px;
  margin: 0 25px 25px;
}

.continue-button {
  flex: 2;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.continue-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.close-button {
  flex: 1;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  border: 2px solid #d9d9d9;
  transition: all 0.3s ease;
}

.close-button:hover {
  border-color: #1890ff;
  color: #1890ff;
  transform: translateY(-1px);
}

/* Footer Note */
.footer-note {
  background: #f0f8ff;
  padding: 15px 25px;
  border-top: 1px solid #e6f0ff;
  text-align: center;
}

.footer-note p {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .upgrade-restriction-modal .ant-modal-content {
    margin: 10px;
    border-radius: 16px;
  }
  
  .modal-header {
    padding: 25px 20px 20px;
  }
  
  .modal-title {
    font-size: 24px;
  }
  
  .current-plan-card,
  .message-section {
    margin: 20px;
  }
  
  .subscription-details {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    margin: 0 20px 20px;
  }
  
  .continue-button,
  .close-button {
    flex: none;
  }
}
