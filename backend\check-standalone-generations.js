const mongoose = require("mongoose");
require("dotenv").config();

async function checkStandaloneGenerations() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log("✅ Connected to MongoDB");

    const { AIQuestionGeneration } = require("./models/aiQuestionGenerationModel");
    const Exam = require("./models/examModel");

    // Find generations without examId or with null examId
    const standaloneGenerations = await AIQuestionGeneration.find({
      $or: [
        { examId: null },
        { examId: { $exists: false } }
      ]
    });

    console.log(`📊 Found ${standaloneGenerations.length} standalone AI generations`);

    if (standaloneGenerations.length > 0) {
      console.log("\n📋 Standalone generations:");
      for (const gen of standaloneGenerations) {
        console.log(`\n- Generation ${gen._id}:`);
        console.log(`  Status: ${gen.generationStatus}`);
        console.log(`  Questions: ${gen.generatedQuestions.length}`);
        console.log(`  Level: ${gen.generationParams?.level}`);
        console.log(`  Class: ${gen.generationParams?.class}`);
        console.log(`  Subjects: ${gen.generationParams?.subjects?.join(", ")}`);
        console.log(`  Created: ${gen.createdAt}`);
      }
    }

    // Also check for any AI generations that reference non-existent exams
    const allGenerations = await AIQuestionGeneration.find({ examId: { $ne: null } });
    console.log(`\n📊 Checking ${allGenerations.length} generations with examId...`);
    
    let orphanedCount = 0;
    for (const gen of allGenerations) {
      const exam = await Exam.findById(gen.examId);
      if (!exam) {
        orphanedCount++;
        console.log(`❌ Generation ${gen._id} references non-existent exam ${gen.examId}`);
      }
    }
    
    if (orphanedCount === 0) {
      console.log("✅ All generations with examId reference valid exams");
    } else {
      console.log(`⚠️ Found ${orphanedCount} orphaned generations`);
    }

    // Check recent exams to see if any were auto-created
    const recentExams = await Exam.find().sort({ createdAt: -1 }).limit(10);
    console.log(`\n📋 Recent exams (last 10):`);
    recentExams.forEach(exam => {
      const isAutoGenerated = exam.name.match(/^[PSA]\d+[A-Z]{1,2}-[A-Z0-9]{2}$/);
      console.log(`- ${exam.name} (${exam.level} Class ${exam.class}) ${isAutoGenerated ? '🤖 AUTO' : ''}`);
    });

    await mongoose.disconnect();
    console.log("\n✅ Check completed");

  } catch (error) {
    console.error("❌ Error:", error);
    await mongoose.disconnect();
  }
}

checkStandaloneGenerations();
