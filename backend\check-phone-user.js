const mongoose = require('mongoose');
const User = require('./models/userModel');

async function checkPhoneUser() {
  try {
    // Connect to MongoDB using the same connection string as the main app
    await mongoose.connect('mongodb://hvmgeeks:<EMAIL>:27017,ac-4ojvv6y-shard-00-01.cdg8fdn.mongodb.net:27017,ac-4ojvv6y-shard-00-02.cdg8fdn.mongodb.net:27017/stjoseph?ssl=true&replicaSet=atlas-fsgg6f-shard-0&authSource=admin&retryWrites=true&w=majority&appName=Cluster0');
    console.log('✅ Connected to MongoDB');
    
    // Search for user with phone number 0765528549
    const phoneNumber = '0765528549';
    console.log(`🔍 Searching for user with phone number: ${phoneNumber}`);
    
    const user = await User.findOne({ phoneNumber: phoneNumber });
    
    if (user) {
      console.log('\n👤 User found:');
      console.log('=====================================');
      console.log(`📧 ID: ${user._id}`);
      console.log(`👤 Name: ${user.name || 'N/A'}`);
      console.log(`👤 First Name: ${user.firstName || 'N/A'}`);
      console.log(`👤 Middle Name: ${user.middleName || 'N/A'}`);
      console.log(`👤 Last Name: ${user.lastName || 'N/A'}`);
      console.log(`🔑 Username: ${user.username || 'N/A'}`);
      console.log(`📧 Email: ${user.email || 'N/A'}`);
      console.log(`📱 Phone: ${user.phoneNumber}`);
      console.log(`🏫 School: ${user.school || 'N/A'}`);
      console.log(`📚 Level: ${user.level || 'N/A'}`);
      console.log(`📖 Class: ${user.class || 'N/A'}`);
      console.log(`👑 Admin: ${user.isAdmin ? 'Yes' : 'No'}`);
      console.log(`🚫 Blocked: ${user.isBlocked ? 'Yes' : 'No'}`);
      console.log(`💳 Payment Required: ${user.paymentRequired ? 'Yes' : 'No'}`);
      console.log(`📅 Created: ${user.createdAt || 'N/A'}`);
      console.log(`📅 Updated: ${user.updatedAt || 'N/A'}`);
      console.log('=====================================');
    } else {
      console.log('\n❌ No user found with phone number:', phoneNumber);
      
      // Let's also check for similar phone numbers
      console.log('\n🔍 Checking for similar phone numbers...');
      const similarUsers = await User.find({ 
        phoneNumber: { $regex: '765528549', $options: 'i' } 
      });
      
      if (similarUsers.length > 0) {
        console.log(`\n📱 Found ${similarUsers.length} user(s) with similar phone numbers:`);
        similarUsers.forEach((user, index) => {
          console.log(`${index + 1}. ${user.name || user.firstName || 'Unknown'} - ${user.phoneNumber}`);
        });
      } else {
        console.log('❌ No similar phone numbers found');
      }
    }
    
    // Also show total user count
    const totalUsers = await User.countDocuments();
    console.log(`\n📊 Total users in database: ${totalUsers}`);
    
    await mongoose.connection.close();
    console.log('\n✅ Database connection closed');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

checkPhoneUser();
