const axios = require('axios');

const testXPSystem = async () => {
  try {
    console.log('🔍 Testing XP System via API...\n');
    
    // Test the XP leaderboard API to see current XP values
    console.log('1️⃣ Checking current XP leaderboard...');
    const leaderboardResponse = await axios.get('http://localhost:5000/api/quiz/xp-leaderboard?limit=5');
    
    if (leaderboardResponse.data && leaderboardResponse.data.length > 0) {
      console.log('✅ Leaderboard API working');
      console.log(`📊 Found ${leaderboardResponse.data.length} users`);
      
      const topUser = leaderboardResponse.data[0];
      console.log(`🏆 Top User: ${topUser.name}`);
      console.log(`   Total XP: ${topUser.totalXP || 0}`);
      console.log(`   Level: ${topUser.currentLevel || 1}`);
      console.log(`   Streak: ${topUser.currentStreak || 0}`);
      console.log(`   Quizzes Taken: ${topUser.totalQuizzesTaken || 0}\n`);
      
      // Show all users' XP
      console.log('📋 All Users XP:');
      leaderboardResponse.data.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.name}: ${user.totalXP || 0} XP`);
      });
      
    } else {
      console.log('❌ No users found in leaderboard');
    }
    
    console.log('\n2️⃣ Testing quiz completion flow...');
    console.log('   Please take a quiz manually and check:');
    console.log('   - XP points are awarded after completion');
    console.log('   - XP points show in result page');
    console.log('   - Ranking updates in real-time');
    console.log('   - Check browser console for any errors');
    
    console.log('\n3️⃣ Checking recent reports...');
    try {
      // This would require authentication, so we'll skip for now
      console.log('   (Skipping reports check - requires authentication)');
    } catch (error) {
      console.log('   Reports check failed (expected without auth)');
    }
    
    console.log('\n✅ XP System API test completed!');
    console.log('💡 To fully test:');
    console.log('   1. Take a quiz in the browser');
    console.log('   2. Check if XP shows in result page');
    console.log('   3. Check if ranking updates');
    console.log('   4. Check browser console for errors');
    
  } catch (error) {
    console.error('❌ XP System test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
};

testXPSystem();
