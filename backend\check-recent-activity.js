const mongoose = require('mongoose');
require('dotenv').config();
const User = require('./models/userModel');
const XPTransaction = require('./models/xpTransactionModel');

async function checkRecentActivity() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('Connected to MongoDB');
    
    // Check recent XP transactions
    const recentTransactions = await XPTransaction.find({})
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('user', 'name');
    
    console.log('Recent XP transactions:');
    if (recentTransactions.length === 0) {
      console.log('No XP transactions found');
    } else {
      recentTransactions.forEach((tx, i) => {
        console.log(`${i + 1}. ${tx.user?.name}: +${tx.xpAmount} XP (${tx.transactionType}) at ${tx.createdAt}`);
      });
    }
    
    // Check if Brainwave user has any recent activity
    const brainwaveUser = await User.findOne({name: 'Brainwave✓'});
    if (brainwaveUser) {
      console.log('\nBrainwave user current stats:');
      console.log('Total XP:', brainwaveUser.totalXP);
      console.log('Lifetime XP:', brainwaveUser.lifetimeXP);
      console.log('Season XP:', brainwaveUser.seasonXP);
      console.log('Last updated:', brainwaveUser.updatedAt);
      
      // Check XP transactions for this user
      const userTransactions = await XPTransaction.find({user: brainwaveUser._id})
        .sort({ createdAt: -1 })
        .limit(5);
      
      console.log('\nBrainwave XP transactions:');
      if (userTransactions.length === 0) {
        console.log('No XP transactions found for this user');
      } else {
        userTransactions.forEach((tx, i) => {
          console.log(`${i + 1}. +${tx.xpAmount} XP (${tx.transactionType}) at ${tx.createdAt}`);
        });
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkRecentActivity();
