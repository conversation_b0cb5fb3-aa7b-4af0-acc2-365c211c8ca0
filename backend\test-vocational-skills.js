// Test script to verify Vocational Skills is available in subjects
const { primarySubjects, primaryKiswahiliSubjects } = require('../frontEnd/src/data/Subjects.jsx');

console.log('🧪 TESTING VOCATIONAL SKILLS AVAILABILITY\n');
console.log('=' .repeat(60));

console.log('📚 PRIMARY SUBJECTS:');
console.log('Total subjects:', primarySubjects.length);
console.log('Subjects list:', primarySubjects);

const hasVocationalSkills = primarySubjects.includes('Vocational Skills');
console.log('\n🔍 VOCATIONAL SKILLS CHECK:');
console.log('✅ "Vocational Skills" found:', hasVocationalSkills ? 'YES' : 'NO');

if (hasVocationalSkills) {
  const index = primarySubjects.indexOf('Vocational Skills');
  console.log('📍 Position in array:', index + 1, 'of', primarySubjects.length);
} else {
  console.log('❌ "Vocational Skills" is missing from primary subjects!');
}

console.log('\n📚 PRIMARY KISWAHILI SUBJECTS:');
console.log('Total subjects:', primaryKiswahiliSubjects.length);
console.log('Subjects list:', primaryKiswahiliSubjects);

const hasUjuziWaKitaaluma = primaryKiswahiliSubjects.includes('Ujuzi wa Kitaaluma');
console.log('\n🔍 UJUZI WA KITAALUMA CHECK:');
console.log('✅ "Ujuzi wa Kitaaluma" found:', hasUjuziWaKitaaluma ? 'YES' : 'NO');

if (hasUjuziWaKitaaluma) {
  const index = primaryKiswahiliSubjects.indexOf('Ujuzi wa Kitaaluma');
  console.log('📍 Position in array:', index + 1, 'of', primaryKiswahiliSubjects.length);
} else {
  console.log('❌ "Ujuzi wa Kitaaluma" is missing from primary Kiswahili subjects!');
}

console.log('\n📊 SUMMARY:');
console.log('=' .repeat(60));
if (hasVocationalSkills && hasUjuziWaKitaaluma) {
  console.log('✅ SUCCESS: Vocational Skills is properly configured');
  console.log('✅ Available in both English and Kiswahili');
  console.log('✅ Admins should be able to add materials for this subject');
} else {
  console.log('❌ ISSUE: Vocational Skills configuration incomplete');
  if (!hasVocationalSkills) {
    console.log('❌ Missing from English subjects');
  }
  if (!hasUjuziWaKitaaluma) {
    console.log('❌ Missing from Kiswahili subjects');
  }
}

console.log('\n🔧 TROUBLESHOOTING TIPS:');
console.log('1. Make sure you select "primary" level in the admin form');
console.log('2. Check that the subject dropdown is properly populated');
console.log('3. Verify that the form is using the correct subjects array');
console.log('4. Clear browser cache if subjects don\'t appear');

console.log('\n🎯 NEXT STEPS:');
console.log('1. Login as admin');
console.log('2. Go to Admin Panel > Study Materials');
console.log('3. Click "Add New Materials"');
console.log('4. Select any material type (Videos, Notes, etc.)');
console.log('5. Choose "primary" as the level');
console.log('6. Look for "Vocational Skills" in the subject dropdown');
