/**
 * Environment Variables Validation Utility
 * 
 * This utility validates that all required environment variables are present
 * and have appropriate values before the application starts.
 */

const chalk = require('chalk');

class EnvironmentValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.requiredVars = new Map();
    this.optionalVars = new Map();
    
    this.setupValidationRules();
  }

  /**
   * Setup validation rules for environment variables
   */
  setupValidationRules() {
    // Critical application variables
    this.addRequired('NODE_ENV', 'string', ['development', 'production', 'test']);
    this.addRequired('PORT', 'number');
    this.addRequired('MONGO_URL', 'string');
    
    // Security variables
    this.addRequired('JWT_SECRET', 'string', null, 32); // Minimum 32 characters
    this.addRequired('SESSION_SECRET', 'string', null, 32);
    
    // Email configuration
    this.addRequired('SENDER_EMAIL', 'email');
    this.addRequired('SENDER_EMAIL_PASSWORD', 'string');
    this.addRequired('OWNER_EMAIL', 'email');
    
    // AWS configuration
    this.addRequired('AWS_ACCESS_KEY_ID', 'string');
    this.addRequired('AWS_SECRET_ACCESS_KEY', 'string');
    this.addRequired('AWS_REGION', 'string');
    this.addRequired('AWS_S3_BUCKET_NAME', 'string');
    
    // Optional but recommended
    this.addOptional('MONGO_URL_FALLBACK', 'string');
    this.addOptional('CLIENT_URL', 'url');
    this.addOptional('OPENAI_API_KEY', 'string');
    this.addOptional('SENTRY_DSN', 'url');
  }

  /**
   * Add required environment variable
   */
  addRequired(name, type, allowedValues = null, minLength = null) {
    this.requiredVars.set(name, { type, allowedValues, minLength });
  }

  /**
   * Add optional environment variable
   */
  addOptional(name, type, allowedValues = null, minLength = null) {
    this.optionalVars.set(name, { type, allowedValues, minLength });
  }

  /**
   * Validate all environment variables
   */
  validate() {
    console.log(chalk.blue('🔍 Validating environment configuration...'));
    
    // Validate required variables
    this.validateRequired();
    
    // Validate optional variables
    this.validateOptional();
    
    // Check for security issues
    this.checkSecurity();
    
    // Display results
    this.displayResults();
    
    // Exit if critical errors found
    if (this.errors.length > 0) {
      console.log(chalk.red('\n❌ Environment validation failed!'));
      console.log(chalk.red('Please fix the above errors before starting the application.'));
      process.exit(1);
    }
    
    console.log(chalk.green('✅ Environment validation passed!'));
  }

  /**
   * Validate required environment variables
   */
  validateRequired() {
    for (const [name, rules] of this.requiredVars) {
      const value = process.env[name];
      
      if (!value) {
        this.errors.push(`Missing required environment variable: ${name}`);
        continue;
      }
      
      this.validateValue(name, value, rules, true);
    }
  }

  /**
   * Validate optional environment variables
   */
  validateOptional() {
    for (const [name, rules] of this.optionalVars) {
      const value = process.env[name];
      
      if (value) {
        this.validateValue(name, value, rules, false);
      }
    }
  }

  /**
   * Validate individual environment variable value
   */
  validateValue(name, value, rules, isRequired) {
    const { type, allowedValues, minLength } = rules;
    
    // Type validation
    if (!this.validateType(value, type)) {
      const message = `Invalid type for ${name}: expected ${type}`;
      if (isRequired) {
        this.errors.push(message);
      } else {
        this.warnings.push(message);
      }
      return;
    }
    
    // Allowed values validation
    if (allowedValues && !allowedValues.includes(value)) {
      const message = `Invalid value for ${name}: must be one of [${allowedValues.join(', ')}]`;
      if (isRequired) {
        this.errors.push(message);
      } else {
        this.warnings.push(message);
      }
      return;
    }
    
    // Minimum length validation
    if (minLength && value.length < minLength) {
      const message = `${name} must be at least ${minLength} characters long`;
      if (isRequired) {
        this.errors.push(message);
      } else {
        this.warnings.push(message);
      }
      return;
    }
  }

  /**
   * Validate value type
   */
  validateType(value, type) {
    switch (type) {
      case 'string':
        return typeof value === 'string' && value.length > 0;
      
      case 'number':
        return !isNaN(Number(value));
      
      case 'boolean':
        return ['true', 'false', '1', '0'].includes(value.toLowerCase());
      
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(value);
      
      case 'url':
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      
      default:
        return true;
    }
  }

  /**
   * Check for common security issues
   */
  checkSecurity() {
    // Check for weak JWT secret
    const jwtSecret = process.env.JWT_SECRET;
    if (jwtSecret && jwtSecret.length < 32) {
      this.warnings.push('JWT_SECRET should be at least 32 characters for better security');
    }
    
    // Check for default/weak secrets
    const weakSecrets = ['secret', 'password', '123456', 'admin'];
    if (jwtSecret && weakSecrets.some(weak => jwtSecret.toLowerCase().includes(weak))) {
      this.errors.push('JWT_SECRET appears to contain weak/common words');
    }
    
    // Check for production environment security
    if (process.env.NODE_ENV === 'production') {
      if (process.env.DEBUG === 'true') {
        this.warnings.push('DEBUG should be disabled in production');
      }
      
      if (!process.env.HELMET_ENABLED || process.env.HELMET_ENABLED !== 'true') {
        this.warnings.push('HELMET_ENABLED should be true in production');
      }
    }
    
    // Check for exposed credentials in development
    if (process.env.NODE_ENV === 'development') {
      if (process.env.VERBOSE_LOGGING !== 'true') {
        this.warnings.push('Consider enabling VERBOSE_LOGGING in development');
      }
    }
  }

  /**
   * Display validation results
   */
  displayResults() {
    if (this.errors.length > 0) {
      console.log(chalk.red('\n❌ Errors:'));
      this.errors.forEach(error => {
        console.log(chalk.red(`  • ${error}`));
      });
    }
    
    if (this.warnings.length > 0) {
      console.log(chalk.yellow('\n⚠️  Warnings:'));
      this.warnings.forEach(warning => {
        console.log(chalk.yellow(`  • ${warning}`));
      });
    }
    
    // Display environment summary
    console.log(chalk.blue('\n📊 Environment Summary:'));
    console.log(chalk.blue(`  • Environment: ${process.env.NODE_ENV || 'undefined'}`));
    console.log(chalk.blue(`  • Port: ${process.env.PORT || 'undefined'}`));
    console.log(chalk.blue(`  • Database: ${process.env.MONGO_URL ? 'Configured' : 'Not configured'}`));
    console.log(chalk.blue(`  • Email: ${process.env.SENDER_EMAIL ? 'Configured' : 'Not configured'}`));
    console.log(chalk.blue(`  • AWS: ${process.env.AWS_ACCESS_KEY_ID ? 'Configured' : 'Not configured'}`));
    console.log(chalk.blue(`  • OpenAI: ${process.env.OPENAI_API_KEY ? 'Configured' : 'Not configured'}`));
    console.log(chalk.blue(`  • Payment: ${process.env.ZENOPAY_ACCOUNT_ID ? 'Configured' : 'Not configured'}`));
  }

  /**
   * Get environment configuration summary
   */
  getConfigSummary() {
    return {
      environment: process.env.NODE_ENV,
      port: process.env.PORT,
      database: !!process.env.MONGO_URL,
      email: !!process.env.SENDER_EMAIL,
      aws: !!process.env.AWS_ACCESS_KEY_ID,
      openai: !!process.env.OPENAI_API_KEY,
      payment: !!process.env.ZENOPAY_ACCOUNT_ID,
      features: {
        userRegistration: process.env.FEATURE_USER_REGISTRATION === 'true',
        emailVerification: process.env.FEATURE_EMAIL_VERIFICATION === 'true',
        paymentGateway: process.env.FEATURE_PAYMENT_GATEWAY === 'true',
        aiAssistance: process.env.FEATURE_AI_ASSISTANCE === 'true',
        videoStreaming: process.env.FEATURE_VIDEO_STREAMING === 'true'
      }
    };
  }
}

/**
 * Validate environment variables
 */
function validateEnvironment() {
  const validator = new EnvironmentValidator();
  validator.validate();
  return validator.getConfigSummary();
}

module.exports = {
  EnvironmentValidator,
  validateEnvironment
};
