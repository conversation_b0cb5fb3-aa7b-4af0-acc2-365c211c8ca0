const mongoose = require('mongoose');
const User = require('./models/userModel');
const Report = require('./models/reportModel');
const XPTransaction = require('./models/xpTransactionModel');
const enhancedXPService = require('./services/enhancedXPService');
const streakTrackingService = require('./services/streakTrackingService');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/brainwave', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

class EnhancedXPMigration {
  constructor() {
    this.migrationStats = {
      totalUsers: 0,
      usersProcessed: 0,
      usersWithReports: 0,
      usersWithLegacyPoints: 0,
      totalXPAwarded: 0,
      errors: []
    };
  }

  /**
   * Main migration function
   */
  async migrate() {
    try {
      console.log('🚀 Starting Enhanced XP System Migration...\n');

      // Get all non-admin users
      const users = await User.find({ isAdmin: { $ne: true } });
      this.migrationStats.totalUsers = users.length;

      console.log(`📊 Found ${users.length} users to migrate\n`);

      for (const user of users) {
        try {
          await this.migrateUser(user);
          this.migrationStats.usersProcessed++;
          
          if (this.migrationStats.usersProcessed % 10 === 0) {
            console.log(`✅ Processed ${this.migrationStats.usersProcessed}/${this.migrationStats.totalUsers} users`);
          }
        } catch (error) {
          console.error(`❌ Error migrating user ${user.name} (${user._id}):`, error.message);
          this.migrationStats.errors.push({
            userId: user._id,
            userName: user.name,
            error: error.message
          });
        }
      }

      // Print final statistics
      this.printMigrationStats();

    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }

  /**
   * Migrate individual user
   */
  async migrateUser(user) {
    console.log(`\n🔄 Migrating user: ${user.name} (${user._id})`);

    // Initialize activity tracking if not exists
    if (!user.activityTracking) {
      user.activityTracking = streakTrackingService.initializeActivityTracking();
    }

    // Get user's quiz reports
    const reports = await Report.find({ user: user._id })
      .populate('exam', 'subject name difficulty duration')
      .sort({ createdAt: 1 });

    if (reports.length > 0) {
      await this.migrateUserWithReports(user, reports);
      this.migrationStats.usersWithReports++;
    } else if (user.totalPointsEarned > 0) {
      await this.migrateUserWithLegacyPoints(user);
      this.migrationStats.usersWithLegacyPoints++;
    } else {
      await this.migrateNewUser(user);
    }

    await user.save();
    console.log(`✅ Completed migration for ${user.name}`);
  }

  /**
   * Migrate user with existing quiz reports
   */
  async migrateUserWithReports(user, reports) {
    console.log(`  📚 Processing ${reports.length} quiz reports...`);

    // Calculate historical streaks
    await streakTrackingService.calculateHistoricalStreaks(user._id);

    // Recalculate XP based on actual performance
    let totalCalculatedXP = 0;
    const xpTransactions = [];

    for (const report of reports) {
      try {
        const { result } = report;
        const examData = report.exam || {};

        // Calculate XP for this quiz using enhanced system
        const xpResult = await enhancedXPService.calculateQuizXP({
          userId: user._id,
          examData: {
            subject: examData.subject || 'General',
            difficulty: examData.difficulty || 'medium',
            duration: examData.duration || 30,
            _id: examData._id
          },
          result: result,
          timeSpent: result.timeSpent || 0,
          isFirstAttempt: true, // Assume first attempt for historical data
          previousScore: null
        });

        totalCalculatedXP += xpResult.xpAwarded;

        // Create XP transaction record
        xpTransactions.push({
          userId: user._id,
          amount: xpResult.xpAwarded,
          transactionType: 'quiz_completion',
          sourceId: examData._id,
          sourceModel: 'exams',
          breakdown: xpResult.breakdown,
          quizData: {
            examId: examData._id,
            subject: examData.subject || 'General',
            difficulty: examData.difficulty || 'medium',
            questionsTotal: (result.correctAnswers?.length || 0) + (result.wrongAnswers?.length || 0),
            questionsCorrect: result.correctAnswers?.length || 0,
            timeSpent: result.timeSpent || 0,
            score: result.score || 0,
            isFirstAttempt: true,
          },
          season: user.currentSeason || '2024-S1',
          metadata: {
            migrationSource: 'historical_report',
            originalReportId: report._id,
            ...xpResult.metadata
          },
          createdAt: report.createdAt,
          updatedAt: report.createdAt
        });

      } catch (error) {
        console.warn(`    ⚠️ Error processing report ${report._id}:`, error.message);
      }
    }

    // Update user XP totals
    user.totalXP = totalCalculatedXP;
    user.lifetimeXP = totalCalculatedXP;
    user.seasonXP = totalCalculatedXP;

    // Calculate level based on new XP
    const levelInfo = enhancedXPService.checkLevelUp(user);
    user.currentLevel = levelInfo.newLevel;
    user.xpToNextLevel = levelInfo.xpToNextLevel;

    // Update XP stats
    if (!user.xpStats) user.xpStats = {};
    user.xpStats.averageXPPerQuiz = reports.length > 0 ? Math.round(totalCalculatedXP / reports.length) : 0;
    user.xpStats.lastXPGain = reports.length > 0 ? reports[reports.length - 1].createdAt : new Date();
    user.xpStats.xpFromQuizzes = totalCalculatedXP;

    // Bulk insert XP transactions
    if (xpTransactions.length > 0) {
      await XPTransaction.insertMany(xpTransactions);
    }

    this.migrationStats.totalXPAwarded += totalCalculatedXP;

    console.log(`    ✅ Calculated ${totalCalculatedXP} XP from ${reports.length} reports`);
    console.log(`    📈 Level: ${user.currentLevel}, Streak: ${user.currentStreak}`);
  }

  /**
   * Migrate user with legacy points but no reports
   */
  async migrateUserWithLegacyPoints(user) {
    console.log(`  💰 Processing legacy points: ${user.totalPointsEarned}`);

    // Estimate performance based on points
    const estimatedQuizzes = Math.max(1, Math.floor(user.totalPointsEarned / 100));
    const estimatedAverageScore = Math.min(95, Math.max(60, 60 + (user.totalPointsEarned / estimatedQuizzes / 10)));
    const estimatedPassRate = Math.min(0.9, Math.max(0.6, user.totalPointsEarned / (estimatedQuizzes * 120)));

    // Calculate XP based on estimated performance
    const baseXPPerQuiz = 50; // Conservative estimate
    const performanceMultiplier = estimatedAverageScore / 70; // Scale based on estimated performance
    const totalEstimatedXP = Math.round(estimatedQuizzes * baseXPPerQuiz * performanceMultiplier);

    // Estimate streaks (users with more points likely have better consistency)
    const estimatedCurrentStreak = Math.min(estimatedQuizzes, Math.floor(user.totalPointsEarned / 300));
    const estimatedBestStreak = Math.min(estimatedQuizzes, Math.floor(user.totalPointsEarned / 200));
    const estimatedLoginStreak = Math.min(30, Math.floor(user.totalPointsEarned / 500));

    // Update user data
    user.totalXP = totalEstimatedXP;
    user.lifetimeXP = totalEstimatedXP;
    user.seasonXP = totalEstimatedXP;
    user.totalQuizzesTaken = estimatedQuizzes;
    user.averageScore = Math.round(estimatedAverageScore);
    user.currentStreak = estimatedCurrentStreak;
    user.bestStreak = estimatedBestStreak;

    // Update activity tracking
    user.activityTracking.dailyLoginStreak = estimatedLoginStreak;
    user.activityTracking.bestLoginStreak = estimatedBestStreak;
    user.activityTracking.quizCompletionStreak = estimatedQuizzes;
    user.activityTracking.bestQuizStreak = estimatedQuizzes;
    user.activityTracking.quizzesPassed = Math.round(estimatedQuizzes * estimatedPassRate);
    user.activityTracking.quizzesFailed = estimatedQuizzes - user.activityTracking.quizzesPassed;

    // Calculate level
    const levelInfo = enhancedXPService.checkLevelUp(user);
    user.currentLevel = levelInfo.newLevel;
    user.xpToNextLevel = levelInfo.xpToNextLevel;

    // Create migration transaction record
    const migrationTransaction = new XPTransaction({
      userId: user._id,
      amount: totalEstimatedXP,
      transactionType: 'migration_legacy_points',
      breakdown: {
        baseXP: estimatedQuizzes * baseXPPerQuiz,
        performanceBonus: totalEstimatedXP - (estimatedQuizzes * baseXPPerQuiz),
        estimatedQuizzes: estimatedQuizzes,
        estimatedAverageScore: estimatedAverageScore,
      },
      season: user.currentSeason || '2024-S1',
      metadata: {
        migrationSource: 'legacy_points',
        originalPoints: user.totalPointsEarned,
        estimatedData: {
          quizzes: estimatedQuizzes,
          averageScore: estimatedAverageScore,
          passRate: estimatedPassRate
        }
      }
    });

    await migrationTransaction.save();
    this.migrationStats.totalXPAwarded += totalEstimatedXP;

    console.log(`    ✅ Estimated ${totalEstimatedXP} XP from ${user.totalPointsEarned} legacy points`);
    console.log(`    📊 Estimated: ${estimatedQuizzes} quizzes, ${estimatedAverageScore}% avg score`);
  }

  /**
   * Migrate new user with no data
   */
  async migrateNewUser(user) {
    console.log(`  🆕 Setting up new user...`);

    // Set default values
    user.totalXP = 0;
    user.lifetimeXP = 0;
    user.seasonXP = 0;
    user.currentLevel = 1;
    user.xpToNextLevel = 100;
    user.totalQuizzesTaken = 0;
    user.averageScore = 0;
    user.currentStreak = 0;
    user.bestStreak = 0;

    console.log(`    ✅ Initialized new user with default values`);
  }

  /**
   * Print migration statistics
   */
  printMigrationStats() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 MIGRATION COMPLETED - STATISTICS');
    console.log('='.repeat(60));
    console.log(`Total Users: ${this.migrationStats.totalUsers}`);
    console.log(`Users Processed: ${this.migrationStats.usersProcessed}`);
    console.log(`Users with Reports: ${this.migrationStats.usersWithReports}`);
    console.log(`Users with Legacy Points: ${this.migrationStats.usersWithLegacyPoints}`);
    console.log(`Total XP Awarded: ${this.migrationStats.totalXPAwarded.toLocaleString()}`);
    console.log(`Errors: ${this.migrationStats.errors.length}`);

    if (this.migrationStats.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      this.migrationStats.errors.forEach(error => {
        console.log(`  - ${error.userName} (${error.userId}): ${error.error}`);
      });
    }

    console.log('\n✅ Migration completed successfully!');
    console.log('='.repeat(60));
  }
}

// Run migration if called directly
if (require.main === module) {
  const runMigration = async () => {
    try {
      await connectDB();
      const migration = new EnhancedXPMigration();
      await migration.migrate();
      process.exit(0);
    } catch (error) {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    }
  };

  runMigration();
}

module.exports = EnhancedXPMigration;
