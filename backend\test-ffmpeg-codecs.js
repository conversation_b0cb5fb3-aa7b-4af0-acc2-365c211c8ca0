const ffmpeg = require("fluent-ffmpeg");
const ffmpegStatic = require("ffmpeg-static");

// Configure FFmpeg
ffmpeg.setFfmpegPath(ffmpegStatic);

async function testFFmpegCodecs() {
  console.log('🧪 Testing FFmpeg codecs...');
  console.log('FFmpeg path:', ffmpegStatic);
  
  try {
    // Test 1: Check available formats
    console.log('\n1️⃣ Checking available formats...');
    await new Promise((resolve, reject) => {
      ffmpeg()
        .getAvailableFormats((err, formats) => {
          if (err) {
            reject(err);
            return;
          }
          
          console.log('Available audio formats:');
          Object.keys(formats).forEach(format => {
            if (formats[format].description.toLowerCase().includes('audio') || 
                ['mp3', 'wav', 'aac', 'ogg'].includes(format)) {
              console.log(`   - ${format}: ${formats[format].description}`);
            }
          });
          resolve();
        });
    });
    
    // Test 2: Check available codecs
    console.log('\n2️⃣ Checking available codecs...');
    await new Promise((resolve, reject) => {
      ffmpeg()
        .getAvailableCodecs((err, codecs) => {
          if (err) {
            reject(err);
            return;
          }
          
          console.log('Available audio codecs:');
          const audioCodecs = [];
          Object.keys(codecs).forEach(codec => {
            if (codecs[codec].type === 'audio') {
              audioCodecs.push(`   - ${codec}: ${codecs[codec].description}`);
            }
          });
          
          // Show first 20 audio codecs
          audioCodecs.slice(0, 20).forEach(codec => console.log(codec));
          if (audioCodecs.length > 20) {
            console.log(`   ... and ${audioCodecs.length - 20} more`);
          }
          
          // Check for specific codecs we need
          console.log('\n🔍 Checking for specific codecs:');
          const neededCodecs = ['mp3', 'pcm_s16le', 'aac', 'libmp3lame'];
          neededCodecs.forEach(codec => {
            if (codecs[codec]) {
              console.log(`   ✅ ${codec}: Available`);
            } else {
              console.log(`   ❌ ${codec}: Not available`);
            }
          });
          
          resolve();
        });
    });
    
    // Test 3: Check available encoders
    console.log('\n3️⃣ Checking available encoders...');
    await new Promise((resolve, reject) => {
      ffmpeg()
        .getAvailableEncoders((err, encoders) => {
          if (err) {
            reject(err);
            return;
          }
          
          console.log('Available audio encoders:');
          const audioEncoders = [];
          Object.keys(encoders).forEach(encoder => {
            if (encoders[encoder].type === 'audio') {
              audioEncoders.push(`   - ${encoder}: ${encoders[encoder].description}`);
            }
          });
          
          // Show first 15 audio encoders
          audioEncoders.slice(0, 15).forEach(encoder => console.log(encoder));
          if (audioEncoders.length > 15) {
            console.log(`   ... and ${audioEncoders.length - 15} more`);
          }
          
          resolve();
        });
    });
    
    console.log('\n✅ FFmpeg codec test completed');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testFFmpegCodecs();
