const jwt = require("jsonwebtoken");
const User = require("../models/userModel");

module.exports = async (req, res, next) => {
  try {
    // Check if user is authenticated first
    const token = req.header("authorization")?.replace("Bearer ", "");
    if (!token) {
      return res.status(401).send({
        message: "Access denied. No token provided.",
        success: false,
      });
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(401).send({
        message: "Access denied. User not found.",
        success: false,
      });
    }

    // Check if user is admin
    if (!user.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    req.user = user;
    next();
  } catch (error) {
    return res.status(401).send({
      message: "Access denied. Invalid token.",
      success: false,
    });
  }
};
