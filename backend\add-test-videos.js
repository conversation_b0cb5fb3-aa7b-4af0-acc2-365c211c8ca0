const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Video model
const videoSchema = new mongoose.Schema({
  className: String,
  subject: String,
  title: String,
  level: String,
  videoID: String,
  videoUrl: String,
  thumbnail: String,
  additionalClasses: [String],
  subtitles: Array,
  hasSubtitles: Boolean,
  subtitleGenerationStatus: String
});

const Videos = mongoose.model('videos', videoSchema);

async function addTestVideos() {
  try {
    console.log('🎬 Adding test videos...');
    
    const testVideos = [
      {
        className: '1',
        subject: 'Mathematics',
        title: 'Basic Addition and Subtraction',
        level: 'primary',
        videoID: 'dQw4w9WgXcQ', // Rick Roll video ID for testing
        hasSubtitles: false,
        subtitleGenerationStatus: 'not_requested'
      },
      {
        className: '2',
        subject: 'Science',
        title: 'Plants and Animals',
        level: 'primary',
        videoID: 'dQw4w9WgXcQ',
        hasSubtitles: false,
        subtitleGenerationStatus: 'not_requested'
      },
      {
        className: '3',
        subject: 'English',
        title: 'Reading Comprehension',
        level: 'primary',
        videoID: 'dQw4w9WgXcQ',
        hasSubtitles: false,
        subtitleGenerationStatus: 'not_requested'
      },
      {
        className: '1',
        subject: 'Mathematics',
        title: 'Introduction to Algebra',
        level: 'secondary',
        videoID: 'dQw4w9WgXcQ',
        hasSubtitles: false,
        subtitleGenerationStatus: 'not_requested'
      },
      {
        className: '2',
        subject: 'Physics',
        title: 'Forces and Motion',
        level: 'secondary',
        videoID: 'dQw4w9WgXcQ',
        hasSubtitles: false,
        subtitleGenerationStatus: 'not_requested'
      }
    ];
    
    // Check if videos already exist
    const existingCount = await Videos.countDocuments();
    console.log(`📊 Existing videos: ${existingCount}`);
    
    if (existingCount === 0) {
      await Videos.insertMany(testVideos);
      console.log('✅ Test videos added successfully!');
    } else {
      console.log('ℹ️ Videos already exist in database');
    }
    
    // Show final count
    const finalCount = await Videos.countDocuments();
    console.log(`📊 Total videos now: ${finalCount}`);
    
  } catch (error) {
    console.error('❌ Error adding test videos:', error);
  } finally {
    mongoose.connection.close();
  }
}

addTestVideos();
