const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const Plan = require('./models/planModel');

async function testPaymentVerificationSystem() {
  try {
    console.log('🧪 Testing Payment Verification System...\n');
    
    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    // Test 1: Check if verification service is running
    console.log('\n1️⃣ Testing verification service status...');
    try {
      const response = await axios.get('http://localhost:5000/api/payment/verification-status');
      console.log('✅ Verification service status:', response.data);
    } catch (error) {
      console.log('❌ Could not check verification service status:', error.message);
    }
    
    // Test 2: Find recent pending subscriptions
    console.log('\n2️⃣ Finding recent pending subscriptions...');
    const recentPending = await Subscription.find({
      paymentStatus: 'pending',
      status: 'pending',
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    }).populate('user', 'firstName lastName username')
      .populate('activePlan', 'title discountedPrice')
      .sort({ createdAt: -1 })
      .limit(5);
    
    console.log(`📋 Found ${recentPending.length} recent pending subscriptions`);
    
    if (recentPending.length > 0) {
      recentPending.forEach((sub, index) => {
        const latestPayment = sub.paymentHistory[sub.paymentHistory.length - 1];
        console.log(`   ${index + 1}. ${sub.user?.firstName} ${sub.user?.lastName} - ${latestPayment?.orderId} - ${sub.activePlan?.title}`);
      });
      
      // Test 3: Manual verification of first pending payment
      console.log('\n3️⃣ Testing manual verification...');
      const testSubscription = recentPending[0];
      const testOrderId = testSubscription.paymentHistory[testSubscription.paymentHistory.length - 1]?.orderId;
      
      if (testOrderId) {
        console.log(`🔍 Testing manual verification for order: ${testOrderId}`);
        
        try {
          const verifyResponse = await axios.post('http://localhost:5000/api/payment/verify-payment', {
            orderId: testOrderId
          });
          console.log('✅ Manual verification response:', verifyResponse.data);
        } catch (error) {
          console.log('❌ Manual verification failed:', error.response?.data || error.message);
        }
      }
    } else {
      console.log('ℹ️ No recent pending subscriptions found to test');
    }
    
    // Test 4: Create a test webhook simulation
    console.log('\n4️⃣ Testing webhook simulation...');
    
    if (recentPending.length > 0) {
      const testSub = recentPending[0];
      const testOrder = testSub.paymentHistory[testSub.paymentHistory.length - 1];
      
      const webhookPayload = {
        order_id: testOrder.orderId,
        payment_status: 'COMPLETED',
        reference: `TEST_${Date.now()}`,
        amount: testOrder.amount.toString(),
        currency: 'TZS',
        timestamp: new Date().toISOString()
      };
      
      console.log('📤 Simulating webhook with payload:', JSON.stringify(webhookPayload, null, 2));
      
      try {
        const webhookResponse = await axios.post('http://localhost:5000/api/payment/webhook', webhookPayload, {
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.ZENOPAY_API_KEY
          }
        });
        
        console.log('✅ Webhook simulation successful:', webhookResponse.data);
        
        // Check if subscription was activated
        setTimeout(async () => {
          const updatedSub = await Subscription.findById(testSub._id);
          console.log(`📊 Subscription status after webhook: ${updatedSub.status} / ${updatedSub.paymentStatus}`);
        }, 2000);
        
      } catch (error) {
        console.log('❌ Webhook simulation failed:', error.response?.data || error.message);
      }
    }
    
    // Test 5: Check system health
    console.log('\n5️⃣ System health check...');
    
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ subscriptionStatus: 'active' });
    const freeUsers = await User.countDocuments({ subscriptionStatus: 'free' });
    const totalSubscriptions = await Subscription.countDocuments();
    const activeSubscriptions = await Subscription.countDocuments({ status: 'active' });
    const pendingSubscriptions = await Subscription.countDocuments({ status: 'pending' });
    
    console.log('📊 System Statistics:');
    console.log(`   👥 Total Users: ${totalUsers}`);
    console.log(`   ✅ Active Users: ${activeUsers}`);
    console.log(`   🆓 Free Users: ${freeUsers}`);
    console.log(`   💳 Total Subscriptions: ${totalSubscriptions}`);
    console.log(`   ✅ Active Subscriptions: ${activeSubscriptions}`);
    console.log(`   ⏳ Pending Subscriptions: ${pendingSubscriptions}`);
    
    // Test 6: Recommendations
    console.log('\n6️⃣ Recommendations:');
    
    if (pendingSubscriptions > 0) {
      console.log(`⚠️ You have ${pendingSubscriptions} pending subscriptions`);
      console.log('💡 The payment verification service will check these automatically');
      console.log('💡 You can also manually verify specific orders using the verify-payment endpoint');
    }
    
    if (freeUsers > activeUsers) {
      console.log('💡 Consider running the subscription fix script to check for missed activations');
    }
    
    console.log('\n✅ Payment verification system test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    mongoose.disconnect();
  }
}

// Run the test
testPaymentVerificationSystem();
