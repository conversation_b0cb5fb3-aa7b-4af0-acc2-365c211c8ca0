.fast-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
}

.fast-loader.small {
  padding: 0.5rem;
}

.fast-loader.large {
  padding: 2rem;
}

.fast-spinner {
  display: flex;
  gap: 0.25rem;
}

.spinner-dot {
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border-radius: 50%;
  animation: fastBounce 0.6s infinite alternate;
}

.spinner-dot:nth-child(2) {
  animation-delay: 0.1s;
}

.spinner-dot:nth-child(3) {
  animation-delay: 0.2s;
}

.loader-text {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.fast-loader.small .loader-text {
  font-size: 0.75rem;
}

@keyframes fastBounce {
  0% {
    transform: translateY(0);
    opacity: 0.7;
  }
  100% {
    transform: translateY(-8px);
    opacity: 1;
  }
}
