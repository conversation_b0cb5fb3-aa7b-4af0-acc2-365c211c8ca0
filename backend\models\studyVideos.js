const mongoose = require("mongoose");

const videoSchema = new mongoose.Schema({
  className: {
    type: String,
    required: true,
  },
  // Array of additional classes that can access this video
  additionalClasses: {
    type: [String],
    default: [],
    required: false,
  },
  subject: {
    type: String,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  videoID: {
    type: String,
    required: true,
  },
  videoUrl: {
    type: String,
    required: false,
  },
  thumbnail: {
    type: String,
    required: false,
  },
  level: {
    type: String,
    enum: ["primary", "secondary", "advance"],
    default: "primary",
    required: false,
  },
  // Subtitle support
  subtitles: {
    type: [{
      language: {
        type: String,
        required: true,
        default: "en"
      },
      languageName: {
        type: String,
        required: true,
        default: "English"
      },
      url: {
        type: String,
        required: true
      },
      isDefault: {
        type: Boolean,
        default: false
      },
      isAutoGenerated: {
        type: Boolean,
        default: false
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
    }],
    default: []
  },
  hasSubtitles: {
    type: Boolean,
    default: false
  },
  subtitleGenerationStatus: {
    type: String,
    enum: ["pending", "processing", "completed", "failed", "not_requested"],
    default: "not_requested"
  }
});

const Videos = mongoose.model("videos", videoSchema);
module.exports = Videos;