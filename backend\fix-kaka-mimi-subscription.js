const axios = require('axios');
require('dotenv').config();

async function fixKakaMimiSubscription() {
  try {
    console.log('🔧 Fixing kaka.mimi subscription...\n');
    
    // The user has 3 pending subscriptions, let's simulate webhook for the most recent one
    const orderIds = [
      'ORDER_1752948904290_df9b37', // Standard Membership - most recent
      'ORDER_1752948889090_df9b37', // Premium Membership  
      'ORDER_1752948874422_df9b37'  // Basic Membership
    ];
    
    console.log('📋 Found pending order IDs:');
    orderIds.forEach((id, index) => {
      console.log(`   ${index + 1}. ${id}`);
    });
    
    // Let's fix the most recent one (Standard Membership)
    const orderIdToFix = orderIds[0];
    console.log(`\n🎯 Fixing order: ${orderIdToFix}`);
    
    // Create webhook payload to simulate successful payment
    const webhookPayload = {
      order_id: orderIdToFix,
      payment_status: 'COMPLETED',
      reference: `REF_MANUAL_FIX_${Date.now()}`,
      amount: '500', // Standard Membership amount
      currency: 'TZS',
      buyer_name: 'kaka mimi',
      buyer_phone: null,
      timestamp: new Date().toISOString(),
      metadata: {
        manual_fix: true,
        fixed_by: 'admin',
        reason: 'webhook_not_received'
      }
    };
    
    console.log('📤 Simulating webhook with payload:');
    console.log(JSON.stringify(webhookPayload, null, 2));
    
    // Send webhook to local server (make sure server is running)
    const webhookUrl = 'http://localhost:5000/api/payment/webhook';
    
    try {
      const response = await axios.post(webhookUrl, webhookPayload, {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.ZENOPAY_API_KEY
        },
        timeout: 10000
      });
      
      if (response.status === 200) {
        console.log('\n✅ SUCCESS! Webhook processed successfully!');
        console.log('🎉 kaka.mimi subscription should now be active!');
        console.log('📱 User should see success on next login!');
        console.log('\n📋 Response:', JSON.stringify(response.data, null, 2));
      } else {
        console.log(`\n⚠️ Unexpected response status: ${response.status}`);
        console.log('📋 Response:', response.data);
      }
      
    } catch (webhookError) {
      console.log('\n❌ Webhook simulation failed!');
      console.log('Error:', webhookError.message);
      
      if (webhookError.response) {
        console.log('Status:', webhookError.response.status);
        console.log('Response:', webhookError.response.data);
      }
      
      if (webhookError.code === 'ECONNREFUSED') {
        console.log('\n💡 SOLUTION: Make sure the server is running!');
        console.log('   Run: npm start (in server directory)');
        console.log('   Then run this script again.');
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Also create a function to fix multiple users at once
async function fixAllPendingSubscriptions() {
  console.log('\n🔧 FIXING ALL PENDING SUBSCRIPTIONS...\n');
  
  // Common pending order patterns we've seen
  const pendingOrders = [
    // kaka.mimi orders
    { orderId: 'ORDER_1752948904290_df9b37', amount: '500', user: 'kaka.mimi' },
    { orderId: 'ORDER_1752948889090_df9b37', amount: '90000', user: 'kaka.mimi' },
    { orderId: 'ORDER_1752948874422_df9b37', amount: '20000', user: 'kaka.mimi' },
  ];
  
  for (const order of pendingOrders) {
    console.log(`🔄 Processing ${order.user} - ${order.orderId}...`);
    
    const webhookPayload = {
      order_id: order.orderId,
      payment_status: 'COMPLETED',
      reference: `REF_BULK_FIX_${Date.now()}`,
      amount: order.amount,
      currency: 'TZS',
      timestamp: new Date().toISOString(),
      metadata: {
        bulk_fix: true,
        user: order.user
      }
    };
    
    try {
      const response = await axios.post('http://localhost:5000/api/payment/webhook', webhookPayload, {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.ZENOPAY_API_KEY
        },
        timeout: 10000
      });
      
      if (response.status === 200) {
        console.log(`✅ Fixed ${order.user} - ${order.orderId}`);
      }
      
    } catch (error) {
      console.log(`❌ Failed ${order.user} - ${order.orderId}: ${error.message}`);
    }
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n🎉 Bulk fix completed!');
}

// Run the fix
console.log('🚀 Starting subscription fix...\n');
console.log('Choose an option:');
console.log('1. Fix kaka.mimi only');
console.log('2. Fix all pending subscriptions');

// For now, let's just fix kaka.mimi
fixKakaMimiSubscription();
