const mongoose = require('mongoose');
require('dotenv').config();
const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');
const Syllabus = require('./models/syllabusModel');

async function testImprovedAISyllabus() {
  try {
    console.log('🧪 Testing Improved AI Question Generation with Enhanced Syllabus Following...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    const aiService = new AIQuestionGenerationService();
    
    // Test with a known syllabus ID
    const testSyllabusId = '6865aa99b4448a485a03b86f';
    
    console.log('📚 Testing enhanced syllabus data retrieval...');
    const syllabusData = await aiService.getSyllabusData(
      'primary',
      '3',
      'Science and Technology',
      testSyllabusId
    );
    
    console.log('Enhanced syllabus data structure:');
    console.log(`- Source: ${syllabusData.source}`);
    console.log(`- Has competencies: ${syllabusData.competencies ? syllabusData.competencies.length : 0} items`);
    console.log(`- Has learning objectives: ${syllabusData.learningObjectives ? syllabusData.learningObjectives.length : 0} items`);
    console.log(`- Has learning activities: ${syllabusData.learningActivities ? syllabusData.learningActivities.length : 0} items`);
    console.log(`- Has extracted text: ${syllabusData.extractedText ? 'Yes' : 'No'}`);
    
    if (syllabusData.competencies && syllabusData.competencies.length > 0) {
      console.log('\n📋 Sample competencies:');
      syllabusData.competencies.slice(0, 2).forEach((comp, index) => {
        console.log(`${index + 1}. ${comp.name}: ${comp.description}`);
        if (comp.specificCompetences && comp.specificCompetences.length > 0) {
          console.log(`   Specific competences: ${comp.specificCompetences.length} items`);
        }
      });
    }
    
    if (syllabusData.learningObjectives && syllabusData.learningObjectives.length > 0) {
      console.log('\n🎯 Sample learning objectives:');
      syllabusData.learningObjectives.slice(0, 3).forEach((obj, index) => {
        console.log(`${index + 1}. ${obj.objective} (Class: ${obj.classLevel || 'N/A'})`);
      });
    }
    
    console.log('\n📝 Testing enhanced prompt building...');
    const prompt = await aiService.buildMultipleChoicePrompt(
      'primary',
      '3',
      'Science and Technology',
      'medium',
      [],
      testSyllabusId
    );
    
    console.log('Enhanced prompt features:');
    console.log(`- Contains competencies: ${prompt.includes('MAIN COMPETENCIES:') ? 'Yes' : 'No'}`);
    console.log(`- Contains learning objectives: ${prompt.includes('LEARNING OBJECTIVES:') ? 'Yes' : 'No'}`);
    console.log(`- Contains class-specific focus: ${prompt.includes('CLASS LEVEL FOCUS:') ? 'Yes' : 'No'}`);
    console.log(`- Contains subject restriction: ${prompt.includes('ONLY - DO NOT mix with other subjects') ? 'Yes' : 'No'}`);
    console.log(`- Contains uniqueness instruction: ${prompt.includes('avoid repetitive') ? 'Yes' : 'No'}`);
    
    console.log('\n📊 Testing question uniqueness system...');
    const questionKey1 = aiService.generateQuestionKey("What is the capital of Tanzania?");
    const questionKey2 = aiService.generateQuestionKey("What is the capital of Tanzania!");
    const questionKey3 = aiService.generateQuestionKey("What are the main parts of a plant?");
    
    console.log(`Question key 1: "${questionKey1}"`);
    console.log(`Question key 2: "${questionKey2}"`);
    console.log(`Question key 3: "${questionKey3}"`);
    console.log(`Keys 1 & 2 are same (should be true): ${questionKey1 === questionKey2}`);
    console.log(`Keys 1 & 3 are different (should be true): ${questionKey1 !== questionKey3}`);
    
    console.log('\n🎉 Enhanced AI Question Generation Test Complete!');
    console.log('\n📋 Summary of Improvements:');
    console.log('✅ Enhanced syllabus data extraction with competencies and learning activities');
    console.log('✅ Class-level specific content targeting');
    console.log('✅ Subject-specific content restriction');
    console.log('✅ Question uniqueness checking system');
    console.log('✅ Competency-aligned question generation');
    console.log('✅ Learning objectives integration');
    
    if (syllabusData.competencies && syllabusData.competencies.length > 0 && 
        prompt.includes('MAIN COMPETENCIES:') && 
        prompt.includes('CLASS LEVEL FOCUS:')) {
      console.log('\n🎯 All enhancements working correctly!');
      console.log('AI should now generate more focused, unique, and curriculum-aligned questions.');
    } else {
      console.log('\n⚠️ Some enhancements may need additional work.');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

testImprovedAISyllabus();
