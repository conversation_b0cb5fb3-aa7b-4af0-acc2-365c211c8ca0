const axios = require('axios');
const jwt = require('jsonwebtoken');
const mongoose = require('mongoose');
require('dotenv').config();

async function testCompleteFrontendFlow() {
  try {
    console.log('🧪 Testing Complete Frontend Flow for AI Question Generation...');
    
    // Connect to database to get a real user
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    const User = require('./models/userModel');
    const user = await User.findOne({ isAdmin: true });
    
    if (!user) {
      console.log('❌ No admin user found for testing');
      return;
    }
    
    console.log('👤 Using admin user:', user.name, '(ID:', user._id + ')');
    
    // Step 1: Generate JWT token (like frontend login)
    const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET, { expiresIn: '1h' });
    console.log('🔑 Generated JWT token');
    
    // Step 2: Test user authentication (like frontend checking user session)
    console.log('\n📋 Step 1: Testing user authentication...');
    const userResponse = await axios.post('http://localhost:5000/api/users/get-user-info', 
      { userId: user._id },
      { headers: { Authorization: `Bearer ${token}` } }
    );
    console.log('✅ User authentication successful');
    
    // Step 3: Test question generation with realistic form data
    console.log('\n📋 Step 2: Testing AI question generation...');
    
    const formData = {
      examId: undefined, // No exam selected
      questionTypes: ['multiple_choice', 'fill_blank'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      difficultyLevels: ['medium'],
      syllabusTopics: ['Basic arithmetic', 'Addition'],
      totalQuestions: 5,
      questionDistribution: {
        multiple_choice: 3,
        fill_blank: 2,
        picture_based: 0
      },
      userId: user._id.toString()
    };
    
    console.log('📤 Sending form data:', JSON.stringify(formData, null, 2));
    
    const questionResponse = await axios.post(
      'http://localhost:5000/api/ai-questions/generate-questions',
      formData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 120000 // 2 minutes
      }
    );
    
    console.log('✅ Question generation successful!');
    console.log('📊 Response status:', questionResponse.status);
    console.log('📊 Success:', questionResponse.data.success);
    console.log('📊 Message:', questionResponse.data.message);
    console.log('📊 Questions generated:', questionResponse.data.data?.questions?.length || 0);
    
    if (questionResponse.data.data?.questions?.length > 0) {
      console.log('\n📝 Generated questions preview:');
      questionResponse.data.data.questions.slice(0, 2).forEach((q, i) => {
        console.log(`${i + 1}. [${q.questionType}] ${q.name}`);
        if (q.options) {
          console.log(`   Correct: ${q.correctOption} - ${q.options[q.correctOption]}`);
        } else if (q.correctAnswer) {
          console.log(`   Answer: ${q.correctAnswer}`);
        }
      });
    }
    
    console.log('\n🎉 Complete frontend flow test successful!');
    console.log('💡 The backend is working correctly. If the frontend is failing,');
    console.log('   check browser console for JavaScript errors or network issues.');
    
  } catch (error) {
    console.error('\n❌ Frontend flow test failed:', error.message);
    
    if (error.response) {
      console.error('📊 HTTP Status:', error.response.status);
      console.error('📊 Response data:', JSON.stringify(error.response.data, null, 2));
    } else if (error.request) {
      console.error('📡 Network error - no response received');
    } else {
      console.error('⚠️ Request setup error:', error.message);
    }
    
    console.log('\n🔍 Debugging suggestions:');
    console.log('1. Check if both frontend (port 3000) and backend (port 5000) are running');
    console.log('2. Check browser console for JavaScript errors');
    console.log('3. Check browser network tab for failed requests');
    console.log('4. Verify user is logged in properly in the frontend');
    console.log('5. Check if CORS is properly configured');
    
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

testCompleteFrontendFlow();
