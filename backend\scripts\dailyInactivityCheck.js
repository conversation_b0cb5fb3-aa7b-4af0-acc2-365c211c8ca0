require('dotenv').config();
const mongoose = require('mongoose');
const inactivityPenaltyService = require('../services/inactivityPenaltyService');

async function runDailyInactivityCheck() {
  try {
    console.log('🚀 Starting daily inactivity check...');
    console.log('📅 Date:', new Date().toISOString());
    
    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    // Get current stats
    const statsBefore = await inactivityPenaltyService.getInactivityStats();
    console.log('📊 Stats before penalty check:', statsBefore);

    // Apply inactivity penalties
    const result = await inactivityPenaltyService.checkInactivityPenalties();
    
    // Get updated stats
    const statsAfter = await inactivityPenaltyService.getInactivityStats();
    console.log('📊 Stats after penalty check:', statsAfter);

    console.log(`✅ Daily inactivity check completed`);
    console.log(`⚠️ Penalties applied: ${result.penaltiesApplied}`);
    
    // Close database connection
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');

  } catch (error) {
    console.error('❌ Error in daily inactivity check:', error);
    process.exit(1);
  }
}

// Run the check
runDailyInactivityCheck();
