const mongoose = require('mongoose');
require('dotenv').config();

const Exam = require('../models/examModel');

async function fixExamClasses() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Find and fix the problematic SST exam
        console.log('\n🔍 Looking for problematic exams...');
        
        // Find exams with incorrect class values for their level
        const problematicExams = await Exam.find({
            $or: [
                // Secondary level with non-Form class values
                {
                    $and: [
                        { $or: [{ level: "secondary" }, { level: "Secondary" }] },
                        { class: { $nin: ["Form-1", "Form-2", "Form-3", "Form-4", "1", "2", "3", "4"] } }
                    ]
                },
                // Advance level with non-Form class values
                {
                    $and: [
                        { $or: [{ level: "advance" }, { level: "Advance" }] },
                        { class: { $nin: ["Form-5", "Form-6", "5", "6"] } }
                    ]
                }
            ]
        });

        console.log(`📊 Found ${problematicExams.length} problematic exams`);

        if (problematicExams.length === 0) {
            console.log('✅ No problematic exams found!');
            return;
        }

        // Show what we found
        console.log('\n🎯 Problematic exams:');
        problematicExams.forEach(exam => {
            console.log(`  - ${exam.name}: Level=${exam.level}, Class=${exam.class}`);
        });

        // Fix each problematic exam
        console.log('\n🔧 Fixing exams...');

        for (const exam of problematicExams) {
            let newLevel = exam.level;
            let newClass = exam.class;

            // Determine correct level and class based on current values
            if (exam.level.toLowerCase() === 'secondary') {
                // For exams marked as secondary level
                if (['1', '2', '3', '4', '5', '6', '7'].includes(exam.class)) {
                    // These are primary classes, so change level to primary
                    newLevel = 'primary';
                    console.log(`  📝 ${exam.name}: Level "${exam.level}" → "${newLevel}" (class ${exam.class} is primary level)`);
                } else if (!exam.class.startsWith('Form-')) {
                    // If it's a number, convert to Form-X for secondary
                    if (['1', '2', '3', '4'].includes(exam.class)) {
                        newClass = `Form-${exam.class}`;
                        console.log(`  📝 ${exam.name}: Class "${exam.class}" → "${newClass}" (secondary level should use Form-X format)`);
                    }
                }
            } else if (exam.level.toLowerCase() === 'advance') {
                // For exams marked as advance level
                if (['1', '2', '3', '4', '5', '6', '7'].includes(exam.class)) {
                    // Classes 1-4 are secondary, 5-7 could be primary or advance
                    if (['1', '2', '3', '4'].includes(exam.class)) {
                        newLevel = 'secondary';
                        newClass = `Form-${exam.class}`;
                        console.log(`  📝 ${exam.name}: Level "${exam.level}" → "${newLevel}", Class "${exam.class}" → "${newClass}"`);
                    } else if (['5', '6'].includes(exam.class)) {
                        // These should be Form-5, Form-6 for advance
                        newClass = `Form-${exam.class}`;
                        console.log(`  📝 ${exam.name}: Class "${exam.class}" → "${newClass}" (advance level should use Form-X format)`);
                    } else if (exam.class === '7') {
                        // Class 7 is primary
                        newLevel = 'primary';
                        console.log(`  📝 ${exam.name}: Level "${exam.level}" → "${newLevel}" (class 7 is primary level)`);
                    }
                }
            }

            // Update the exam if level or class changed
            const updates = {};
            if (newLevel !== exam.level) {
                updates.level = newLevel;
            }
            if (newClass !== exam.class) {
                updates.class = newClass;
            }

            if (Object.keys(updates).length > 0) {
                await Exam.findByIdAndUpdate(exam._id, updates);
                console.log(`  ✅ Updated ${exam.name}:`, updates);
            }
        }

        // Verify the fixes
        console.log('\n🔍 Verifying fixes...');

        const primaryExams = await Exam.find({
            $or: [{ level: "primary" }, { level: "Primary" }]
        });

        const primaryClasses = [...new Set(primaryExams.map(e => e.class).filter(Boolean))].sort();
        console.log('📊 Primary exam classes after fix:', primaryClasses);

        const secondaryExams = await Exam.find({
            $or: [{ level: "secondary" }, { level: "Secondary" }]
        });

        const secondaryClasses = [...new Set(secondaryExams.map(e => e.class).filter(Boolean))].sort();
        console.log('📊 Secondary exam classes after fix:', secondaryClasses);

        const advanceExams = await Exam.find({
            $or: [{ level: "advance" }, { level: "Advance" }]
        });

        const advanceClasses = [...new Set(advanceExams.map(e => e.class).filter(Boolean))].sort();
        console.log('📊 Advance exam classes after fix:', advanceClasses);

        console.log('\n✅ Exam class fix completed!');
        
    } catch (error) {
        console.error('❌ Error fixing exam classes:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    fixExamClasses();
}

module.exports = fixExamClasses;
