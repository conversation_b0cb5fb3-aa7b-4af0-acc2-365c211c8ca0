const mongoose = require('mongoose');
require('dotenv').config();

const Exam = require('../models/examModel');
const User = require('../models/userModel');

async function debugExamClasses() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Get a secondary user for testing
        const secondaryUser = await User.findOne({ level: 'secondary' });
        if (!secondaryUser) {
            console.log('❌ No secondary user found');
            return;
        }

        console.log(`👤 Testing with secondary user: ${secondaryUser.name} (Level: ${secondaryUser.level})`);

        // Get all exams that a secondary user would see
        const secondaryExams = await Exam.find({
            $or: [
                { level: "secondary" },
                { level: "Secondary" }
            ]
        });

        console.log(`\n📊 Found ${secondaryExams.length} secondary exams`);

        // Analyze class values
        const classValues = secondaryExams.map(exam => exam.class).filter(Boolean);
        const uniqueClasses = [...new Set(classValues)].sort();

        console.log('\n🎯 Unique class values in secondary exams:');
        uniqueClasses.forEach(className => {
            const count = classValues.filter(c => c === className).length;
            console.log(`  - "${className}": ${count} exams`);
        });

        // Check for problematic class values
        console.log('\n🔍 Checking for problematic class values:');
        const problematicClasses = uniqueClasses.filter(className => {
            // Secondary should have Form-1, Form-2, Form-3, Form-4
            return !['Form-1', 'Form-2', 'Form-3', 'Form-4', '1', '2', '3', '4'].includes(className);
        });

        if (problematicClasses.length > 0) {
            console.log('❌ Found problematic class values:');
            problematicClasses.forEach(className => {
                const examsWithClass = secondaryExams.filter(exam => exam.class === className);
                console.log(`  - "${className}": ${examsWithClass.length} exams`);
                examsWithClass.slice(0, 3).forEach(exam => {
                    console.log(`    * ${exam.name} (Level: ${exam.level}, Class: ${exam.class})`);
                });
            });
        } else {
            console.log('✅ All class values look correct');
        }

        // Show sample exams
        console.log('\n📝 Sample secondary exams:');
        secondaryExams.slice(0, 5).forEach(exam => {
            console.log(`  - ${exam.name}: Level=${exam.level}, Class=${exam.class}, Questions=${exam.questions?.length || 0}`);
        });

        // Check what the frontend would generate for availableClasses
        const frontendAvailableClasses = [...new Set(secondaryExams.map(e => e.class).filter(Boolean))].sort();
        console.log('\n🖥️ Frontend would generate these availableClasses:');
        frontendAvailableClasses.forEach(className => {
            console.log(`  - "${className}"`);
        });

        console.log('\n✅ Debug completed!');
        
    } catch (error) {
        console.error('❌ Error debugging exam classes:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    debugExamClasses();
}

module.exports = debugExamClasses;
