const mongoose = require('mongoose');
const User = require('./models/userModel');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/brainwave', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const updateSubscriptionStatuses = async () => {
  try {
    await connectDB();
    
    console.log('🔄 Updating subscription statuses for testing...\n');
    
    // Get all non-admin users
    const users = await User.find({ isAdmin: { $ne: true } }).limit(10);
    
    if (users.length === 0) {
      console.log('❌ No users found');
      return;
    }
    
    console.log(`👥 Found ${users.length} users to update`);
    
    // Update users with different subscription statuses
    for (let i = 0; i < users.length; i++) {
      const user = users[i];
      let subscriptionStatus, subscriptionEndDate, subscriptionPlan;

      if (i % 5 === 0) {
        // Premium Activated users (6-month or yearly plans)
        subscriptionStatus = 'active';
        subscriptionPlan = 'premium';
        subscriptionEndDate = new Date(Date.now() + 180 * 24 * 60 * 60 * 1000); // 6 months from now
      } else if (i % 5 === 1) {
        // Standard Activated users (intermediate plans)
        subscriptionStatus = 'active';
        subscriptionPlan = 'standard';
        subscriptionEndDate = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000); // 3 months from now
      } else if (i % 5 === 2) {
        // Basic Activated users (can log in and use system)
        subscriptionStatus = 'active';
        subscriptionPlan = 'basic';
        subscriptionEndDate = new Date(Date.now() + 60 * 24 * 60 * 60 * 1000); // 2 months from now
      } else if (i % 5 === 3) {
        // Expired users (had subscription but expired)
        subscriptionStatus = 'expired';
        subscriptionPlan = 'basic';
        subscriptionEndDate = new Date(Date.now() - 10 * 24 * 60 * 60 * 1000); // 10 days ago
      } else {
        // Users with no plan (never had subscription)
        subscriptionStatus = 'free';
        subscriptionPlan = null;
        subscriptionEndDate = null;
      }

      await User.findByIdAndUpdate(user._id, {
        subscriptionStatus: subscriptionStatus,
        subscriptionEndDate: subscriptionEndDate,
        subscriptionPlan: subscriptionPlan
      });

      console.log(`✅ Updated ${user.name}: ${subscriptionStatus} (${subscriptionPlan || 'no plan'}) ${subscriptionEndDate ? `(ends: ${subscriptionEndDate.toDateString()})` : ''}`);
    }
    
    console.log('\n🎉 Subscription statuses updated successfully!');
    
    // Show summary
    const premiumCount = await User.countDocuments({
      subscriptionStatus: 'active',
      subscriptionPlan: 'premium',
      isAdmin: { $ne: true }
    });

    const standardCount = await User.countDocuments({
      subscriptionStatus: 'active',
      subscriptionPlan: 'standard',
      isAdmin: { $ne: true }
    });

    const basicCount = await User.countDocuments({
      subscriptionStatus: 'active',
      subscriptionPlan: 'basic',
      isAdmin: { $ne: true }
    });

    const expiredCount = await User.countDocuments({
      subscriptionStatus: 'expired',
      isAdmin: { $ne: true }
    });

    const freeCount = await User.countDocuments({
      subscriptionStatus: 'free',
      isAdmin: { $ne: true }
    });

    console.log('\n📊 Summary:');
    console.log(`🔵 Premium Activated (6-month/yearly): ${premiumCount}`);
    console.log(`🟡 Standard Activated (intermediate): ${standardCount}`);
    console.log(`🟢 Basic Activated (can use system): ${basicCount}`);
    console.log(`🔴 Expired (no current plan): ${expiredCount}`);
    console.log(`⚪ Free (never had plan): ${freeCount}`);
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Update failed:', error);
    process.exit(1);
  }
};

updateSubscriptionStatuses();
