/* Modern Premium Subscription Modal */
.subscription-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

.subscription-modal {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 24px;
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
  max-width: 800px; /* Reduced from 1000px for better laptop fit */
  width: 90%; /* Reduced from 95% for better laptop spacing */
  max-height: 90vh; /* Reduced from 95vh for better laptop fit */
  overflow: hidden;
  animation: slideUp 0.4s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem; /* Reduced from 2rem for more compact header */
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.modal-title {
  font-size: 1.5rem; /* Reduced from 1.8rem for better laptop fit */
  font-weight: 700;
  margin: 0;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.modal-content {
  padding: 1.5rem;
  max-height: calc(95vh - 120px);
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Plans Grid */
.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.plan-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.plan-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.plan-card:hover {
  transform: translateY(-8px);
  border-color: #667eea;
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.2);
}

.plan-card:hover::before {
  transform: scaleX(1);
}

.plan-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.plan-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.plan-badge {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.plan-price {
  text-align: center;
  margin-bottom: 1.5rem;
}

.price-amount {
  display: block;
  font-size: 2rem;
  font-weight: 800;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.price-original {
  display: block;
  font-size: 1rem;
  color: #9ca3af;
  text-decoration: line-through;
  margin-bottom: 0.25rem;
}

.price-period {
  display: block;
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

.plan-features {
  margin-bottom: 1.5rem;
}

.feature {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.feature-icon {
  color: #10b981;
  font-weight: 700;
  font-size: 1rem;
}

.feature-text {
  color: #374151;
  font-size: 0.9rem;
  font-weight: 500;
}

.select-plan-btn {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.select-plan-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Payment Step */
.payment-step {
  max-width: 500px;
  margin: 0 auto;
}

.selected-plan-summary {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  padding: 1.5rem;
  border-radius: 16px;
  text-align: center;
  margin-bottom: 2rem;
  border: 1px solid #bfdbfe;
}

.selected-plan-summary h3 {
  color: #1e40af;
  margin: 0 0 0.5rem 0;
  font-size: 1.3rem;
}

.plan-price-summary {
  color: #1e40af;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0;
}

.payment-info {
  margin-bottom: 2rem;
}

.phone-section {
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 12px;
  margin-bottom: 1rem;
}

.info-item .info-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.phone-display {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  justify-content: space-between;
}

.phone-edit {
  flex: 1;
}

.phone-input {
  width: 100%;
  padding: 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 0.75rem;
  transition: border-color 0.3s ease;
}

.phone-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.phone-input.valid {
  border-color: #10b981;
}

.phone-input.invalid {
  border-color: #ef4444;
}

.phone-validation {
  margin-bottom: 0.5rem;
}

.validation-message {
  font-size: 0.8rem;
  font-weight: 500;
}

.validation-message.valid {
  color: #10b981;
}

.validation-message.invalid {
  color: #ef4444;
}

.phone-actions {
  display: flex;
  gap: 0.5rem;
}

.edit-phone-btn {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.edit-phone-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.save-phone-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.save-phone-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.save-phone-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.cancel-phone-btn {
  background: #f3f4f6;
  color: #374151;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.cancel-phone-btn:hover {
  background: #e5e7eb;
}

.phone-note {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid #bfdbfe;
  margin-top: 0.5rem;
}

.phone-note small {
  color: #1e40af;
  font-weight: 500;
  line-height: 1.4;
}

.info-label {
  font-weight: 600;
  color: #374151;
}

.info-value {
  color: #6b7280;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.info-value.updated {
  color: #10b981;
  font-weight: 600;
}

.updated-indicator {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  animation: fadeInScale 0.5s ease-out;
}

.payment-actions {
  display: flex;
  gap: 1rem;
}

.back-btn {
  flex: 1;
  background: #f3f4f6;
  color: #374151;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #e5e7eb;
}

.pay-btn {
  flex: 2;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.pay-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.pay-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Success Step - Removed (no longer used) */

/* Payment Modal Close Button */
.payment-modal-close {
  position: absolute;
  top: -10px;
  right: -10px;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e5e7eb;
  color: #666;
  font-size: 18px;
  font-weight: bold;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.payment-modal-close:hover {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border-color: #ff4d4f;
  transform: scale(1.1);
}

/* Try Again Section */
.payment-try-again {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.connection-issue {
  color: #d46b08;
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
}

.try-again-button {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.try-again-button:hover {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* Phone Instructions */
.phone-instruction {
  background: rgba(24, 144, 255, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.2);
  border-radius: 12px;
  padding: 20px;
  margin: 15px 0;
  text-align: center;
}

.phone-instruction h4 {
  color: #1890ff;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 10px 0;
}

.phone-number {
  color: #1890ff;
  font-size: 16px;
  font-weight: 600;
  background: rgba(24, 144, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  display: inline-block;
  margin-bottom: 15px;
}

.instruction-steps {
  text-align: left;
  max-width: 300px;
  margin: 0 auto;
}

.instruction-steps p {
  color: #4a5568;
  font-size: 14px;
  line-height: 1.6;
  margin: 8px 0;
}

.try-again-text {
  color: #8c4a00;
  font-size: 13px;
  margin-bottom: 10px;
  font-style: italic;
}

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
  .subscription-modal .ant-modal {
    width: 98% !important;
    max-width: 380px !important;
    margin: 5px auto !important;
  }

  .subscription-modal .ant-modal-content {
    margin: 0;
    border-radius: 16px !important;
  }

  .subscription-modal .ant-modal-body {
    padding: 10px !important;
    max-height: 98vh;
    overflow: hidden;
  }

  .modal-content {
    padding: 0.75rem;
    overflow: hidden;
  }

  /* Success step styles removed - no longer used */

  .phone-instruction {
    padding: 8px;
    margin: 6px 0;
    border-radius: 8px;
  }

  .phone-instruction h4 {
    font-size: 13px;
    margin-bottom: 4px;
  }

  .phone-number {
    font-size: 12px;
    padding: 4px 8px;
    margin: 4px 0;
  }

  .instruction-steps {
    max-width: 100%;
  }

  .instruction-steps p {
    font-size: 11px;
    margin: 1px 0;
  }

  .payment-try-again {
    padding: 8px;
    margin: 8px 0;
  }

  .connection-issue {
    font-size: 12px !important;
    margin-bottom: 4px !important;
  }

  .try-again-text {
    font-size: 10px !important;
    margin-bottom: 6px !important;
  }

  .try-again-button {
    width: 100%;
    padding: 8px 12px;
    font-size: 11px;
    border-radius: 6px;
  }

  .payment-steps {
    margin-bottom: 0.75rem;
  }

  .step {
    padding: 0.4rem;
    gap: 0.4rem;
    margin-bottom: 0.3rem;
    border-radius: 6px;
  }

  .step-number {
    width: 18px;
    height: 18px;
    font-size: 0.6rem;
  }

  .step-text {
    font-size: 0.7rem;
  }

  /* Success actions removed - no longer used */
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .subscription-modal .ant-modal {
    width: 95% !important;
    max-width: 500px !important;
    margin: 10px auto !important;
  }

  .subscription-modal .ant-modal-content {
    margin: 0;
    border-radius: 18px !important;
  }

  .subscription-modal .ant-modal-body {
    padding: 15px !important;
    max-height: 95vh;
    overflow: hidden;
  }

  .modal-content {
    padding: 1rem;
    overflow: hidden;
  }

  /* Success step styles removed - no longer used */

  .phone-instruction {
    padding: 12px;
    margin: 8px 0;
    border-radius: 10px;
  }

  .phone-instruction h4 {
    font-size: 15px;
    margin-bottom: 6px;
  }

  .phone-number {
    font-size: 14px;
    padding: 6px 12px;
    margin: 6px 0;
  }

  .instruction-steps {
    max-width: 100%;
  }

  .instruction-steps p {
    font-size: 13px;
    margin: 2px 0;
  }

  .payment-try-again {
    padding: 12px;
    margin: 10px 0;
  }

  .connection-issue {
    font-size: 14px !important;
    margin-bottom: 6px !important;
  }

  .try-again-text {
    font-size: 12px !important;
    margin-bottom: 8px !important;
  }

  .try-again-button {
    width: 100%;
    padding: 10px 16px;
    font-size: 13px;
    border-radius: 8px;
  }

  .payment-steps {
    margin-bottom: 1rem;
  }

  .step {
    padding: 0.6rem;
    gap: 0.6rem;
    margin-bottom: 0.4rem;
    border-radius: 8px;
  }

  .step-number {
    width: 22px;
    height: 22px;
    font-size: 0.7rem;
  }

  .step-text {
    font-size: 0.85rem;
  }

  .success-actions {
    flex-direction: row;
    gap: 0.75rem;
    margin-top: 1rem;
    justify-content: center;
  }

  .check-status-btn,
  .done-btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    border-radius: 8px;
    min-width: 120px;
  }
}

/* Laptop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .subscription-modal {
    max-width: 700px; /* Optimized for laptop screens */
    width: 85%; /* Better fit for laptop screens */
    max-height: 85vh; /* More compact for laptop screens */
  }

  .modal-header {
    padding: 1.2rem; /* More compact header for laptops */
  }

  .modal-title {
    font-size: 1.4rem; /* Smaller title for laptop screens */
  }

  .modal-content {
    padding: 1.5rem; /* Reduced padding for laptops */
  }

  .plans-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); /* Better grid for laptops */
    gap: 1.5rem; /* Reduced gap for laptops */
  }

  .plan-card {
    padding: 1.5rem; /* More compact cards for laptops */
  }

  .plan-title {
    font-size: 1.3rem; /* Smaller plan titles for laptops */
  }

  .plan-price {
    font-size: 2rem; /* Smaller price for laptops */
  }

  .plan-features li {
    font-size: 0.9rem; /* Smaller feature text for laptops */
    padding: 0.4rem 0; /* Reduced padding for laptops */
  }

  .choose-plan-btn {
    padding: 0.8rem 1.5rem; /* More compact buttons for laptops */
    font-size: 0.95rem; /* Smaller button text for laptops */
  }
}

/* Desktop Devices (1025px+) */
@media (min-width: 1025px) {
  .subscription-modal .ant-modal {
    width: 90% !important;
    max-width: 1000px !important;
    margin: 20px auto !important;
  }

  .subscription-modal .ant-modal-content {
    margin: 0;
    border-radius: 24px !important;
  }

  .subscription-modal .ant-modal-body {
    padding: 20px !important;
    max-height: 90vh;
    overflow: hidden;
  }

  .modal-content {
    padding: 1.5rem;
    overflow: hidden;
  }

  .success-step {
    padding: 1rem 0;
  }

  .payment-modal-close {
    top: 16px;
    right: 16px;
    width: 36px;
    height: 36px;
    font-size: 18px;
  }

  .success-animation {
    margin: 1rem 0;
  }

  .pulse-circle {
    width: 80px;
    height: 80px;
  }

  .phone-icon {
    font-size: 32px;
  }

  .success-step h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .phone-instruction {
    padding: 15px;
    margin: 10px 0;
    border-radius: 12px;
  }

  .phone-instruction h4 {
    font-size: 16px;
    margin-bottom: 8px;
  }

  .phone-number {
    font-size: 15px;
    padding: 8px 16px;
    margin: 8px 0;
  }

  .instruction-steps {
    max-width: 100%;
  }

  .instruction-steps p {
    font-size: 14px;
    margin: 3px 0;
  }

  .payment-try-again {
    padding: 15px;
    margin: 12px 0;
  }

  .connection-issue {
    font-size: 16px !important;
    margin-bottom: 8px !important;
  }

  .try-again-text {
    font-size: 14px !important;
    margin-bottom: 10px !important;
  }

  .try-again-button {
    padding: 12px 20px;
    font-size: 14px;
    border-radius: 10px;
  }

  .payment-steps {
    margin-bottom: 1.5rem;
  }

  .step {
    padding: 0.75rem;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 8px;
  }

  .step-number {
    width: 24px;
    height: 24px;
    font-size: 0.75rem;
  }

  .step-text {
    font-size: 0.9rem;
  }

  .success-actions {
    flex-direction: row;
    gap: 1rem;
    margin-top: 1.5rem;
    justify-content: center;
  }

  .check-status-btn,
  .done-btn {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    border-radius: 10px;
    min-width: 150px;
  }

  .phone-icon {
    font-size: 2.5rem;
  }
}

@media (min-width: 1025px) {
  /* Desktop Styles */
  .subscription-modal .ant-modal {
    width: 60% !important;
    max-width: 550px !important;
  }

  .subscription-modal .ant-modal-body {
    padding: 25px !important;
  }

  .phone-instruction {
    padding: 22px;
  }

  .phone-instruction h4 {
    font-size: 18px;
  }

  .phone-number {
    font-size: 16px;
  }

  .instruction-steps p {
    font-size: 14px;
  }

  .try-again-button {
    padding: 12px 20px;
    font-size: 15px;
  }

  .pulse-circle {
    width: 100px;
    height: 100px;
  }

  .phone-icon {
    font-size: 3rem;
  }
}

/* Success animation styles removed - no longer used */

.payment-steps {
  margin-bottom: 1rem;
}

.step {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f9fafb;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.step-number {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.step-text {
  color: #374151;
  font-weight: 500;
}

/* Success actions styles removed - no longer used */

.check-status-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  font-size: 0.9rem;
}

.check-status-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.done-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.done-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 3rem;
}

.spinner, .btn-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.btn-spinner {
  width: 16px;
  height: 16px;
  border-width: 2px;
  margin: 0;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(50px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .subscription-modal {
    width: 98%;
    margin: 1%;
  }

  .modal-header {
    padding: 1.5rem;
  }

  .modal-title {
    font-size: 1.4rem;
  }

  .modal-content {
    padding: 1.5rem;
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .payment-actions {
    flex-direction: column;
  }

  .price-amount {
    font-size: 1.6rem;
  }

  .success-actions {
    flex-direction: column;
  }

  .check-status-btn,
  .done-btn {
    width: 100%;
  }

  .phone-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .phone-actions {
    width: 100%;
  }

  .save-phone-btn,
  .cancel-phone-btn {
    flex: 1;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}

/* Simplified Phone Display Styles */
.phone-display-simple {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.valid-phone {
  color: #059669;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.invalid-phone-warning {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.invalid-phone {
  color: #dc2626;
  font-weight: 500;
}

.update-phone-btn {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.update-phone-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.payment-note {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 12px;
  margin-top: 16px;
}

.payment-note p {
  color: #1e40af;
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
}
