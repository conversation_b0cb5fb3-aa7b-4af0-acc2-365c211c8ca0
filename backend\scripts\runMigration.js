#!/usr/bin/env node

/**
 * Migration Runner Script
 * 
 * This script runs the migration to move Form-5 and Form-6 data 
 * from secondary level to the new advance level.
 * 
 * Usage:
 *   node server/scripts/runMigration.js
 * 
 * Or from the server directory:
 *   node scripts/runMigration.js
 */

require('dotenv').config();
const { migrateToAdvanceLevel, connectDB } = require('./migrateToAdvanceLevel');

const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const askConfirmation = () => {
  return new Promise((resolve) => {
    console.log("⚠️  IMPORTANT: This migration will move all Form-5 and Form-6 data from secondary to advance level.");
    console.log("📋 This includes:");
    console.log("   - Users in Form-5 and Form-6 classes");
    console.log("   - Study materials (videos, notes, books, past papers)");
    console.log("   - Exams for Form-5 and Form-6");
    console.log("   - Forum questions from migrated users");
    console.log("");
    console.log("🔄 This operation will modify your database.");
    console.log("💾 Make sure you have a backup of your database before proceeding.");
    console.log("");
    
    rl.question('Do you want to continue? (yes/no): ', (answer) => {
      resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
    });
  });
};

const runMigrationWithConfirmation = async () => {
  try {
    console.log("🚀 Advance Level Migration Script");
    console.log("==================================");
    
    const confirmed = await askConfirmation();
    
    if (!confirmed) {
      console.log("❌ Migration cancelled by user.");
      rl.close();
      process.exit(0);
    }
    
    console.log("\n✅ Starting migration...");
    
    await connectDB();
    await migrateToAdvanceLevel();
    
    console.log("\n🎉 Migration completed successfully!");
    console.log("📝 Summary:");
    console.log("   - All Form-5 and Form-6 users have been moved to advance level");
    console.log("   - All related study materials have been updated");
    console.log("   - All related exams have been updated");
    console.log("   - All related forum questions have been updated");
    console.log("");
    console.log("🔄 You may need to restart your application for changes to take full effect.");
    
  } catch (error) {
    console.error("❌ Migration failed:", error.message);
    console.error("🔧 Please check your database connection and try again.");
  } finally {
    rl.close();
    process.exit(0);
  }
};

// Run the migration
runMigrationWithConfirmation();
