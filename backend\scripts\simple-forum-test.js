const mongoose = require('mongoose');
require('dotenv').config();

async function simpleForumTest() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Direct database queries
        const db = mongoose.connection.db;

        // Check all forum questions
        const allQuestions = await db.collection('forum-questions').find({}).toArray();
        console.log(`\n📊 Total forum questions: ${allQuestions.length}`);

        // Group by level
        const byLevel = {};
        allQuestions.forEach(q => {
            const level = q.level || 'undefined';
            byLevel[level] = (byLevel[level] || 0) + 1;
        });

        console.log('\n📊 Questions by level:');
        Object.keys(byLevel).forEach(level => {
            console.log(`  ${level}: ${byLevel[level]} questions`);
        });

        // Check users
        const allUsers = await db.collection('users').find({}).toArray();
        console.log(`\n👥 Total users: ${allUsers.length}`);

        const usersByLevel = {};
        allUsers.forEach(u => {
            const level = u.level || 'undefined';
            usersByLevel[level] = (usersByLevel[level] || 0) + 1;
        });

        console.log('\n👥 Users by level:');
        Object.keys(usersByLevel).forEach(level => {
            console.log(`  ${level}: ${usersByLevel[level]} users`);
        });

        // Test specific filtering
        console.log('\n🧪 Testing forum filtering:');

        // Primary filter test
        const primaryFilter = {
            level: { $nin: ["secondary", "advance", "Secondary", "Advance"] }
        };
        const primaryQuestions = await db.collection('forum-questions').find(primaryFilter).toArray();
        console.log(`Primary filter result: ${primaryQuestions.length} questions`);

        // Secondary filter test
        const secondaryFilter = {
            $or: [
                { level: "secondary" },
                { level: "Secondary" }
            ]
        };
        const secondaryQuestions = await db.collection('forum-questions').find(secondaryFilter).toArray();
        console.log(`Secondary filter result: ${secondaryQuestions.length} questions`);

        // Advance filter test
        const advanceFilter = {
            $or: [
                { level: "advance" },
                { level: "Advance" }
            ]
        };
        const advanceQuestions = await db.collection('forum-questions').find(advanceFilter).toArray();
        console.log(`Advance filter result: ${advanceQuestions.length} questions`);

        console.log('\n✅ Simple forum test completed!');
        
    } catch (error) {
        console.error('❌ Error in simple forum test:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    simpleForumTest();
}

module.exports = simpleForumTest;
