const mongoose = require('mongoose');
require('dotenv').config();

const checkVideos = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    // Check studyvideos collection
    const db = mongoose.connection.db;
    const studyVideos = await db.collection('studyvideos').find().limit(5).toArray();
    
    console.log(`📹 Found ${studyVideos.length} videos in studyvideos collection`);
    
    if (studyVideos.length > 0) {
      console.log('\n📹 Sample videos:');
      studyVideos.forEach((video, index) => {
        console.log(`\n${index + 1}. ${video.title || 'No title'}`);
        console.log(`   - Subject: ${video.subject || 'No subject'}`);
        console.log(`   - Class: ${video.className || 'No class'}`);
        console.log(`   - Level: ${video.level || 'No level'}`);
        console.log(`   - Thumbnail: ${video.thumbnail || 'NO THUMBNAIL'}`);
        console.log(`   - VideoID: ${video.videoID || 'No videoID'}`);
        console.log(`   - VideoURL: ${video.videoUrl || 'No videoUrl'}`);
        console.log(`   - All fields: ${Object.keys(video).join(', ')}`);
      });
      
      // Count videos with thumbnails
      const withThumbnails = await db.collection('studyvideos').countDocuments({
        thumbnail: { $exists: true, $ne: null, $ne: '' }
      });
      console.log(`\n🖼️ Videos with thumbnails: ${withThumbnails}`);
      
    } else {
      console.log('❌ No videos found in studyvideos collection');
    }
    
    // Also check users collection for authentication
    const users = await db.collection('users').find().limit(2).toArray();
    console.log(`\n👤 Found ${users.length} users`);
    if (users.length > 0) {
      console.log('Sample user fields:', Object.keys(users[0]));
    }
    
    await mongoose.disconnect();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
};

checkVideos();
