const mongoose = require('mongoose');
const axios = require('axios');
require('dotenv').config();

async function fixNiniSubscription() {
  try {
    console.log('🔧 Fixing nini.nini subscription status...\n');

    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    // Define schemas
    const subscriptionSchema = new mongoose.Schema({}, { strict: false, strictPopulate: false });
    const userSchema = new mongoose.Schema({}, { strict: false });
    const planSchema = new mongoose.Schema({}, { strict: false });

    const Subscription = mongoose.model('Subscription', subscriptionSchema);
    const User = mongoose.model('User', userSchema);
    const Plan = mongoose.model('Plan', planSchema);

    // Find nini.nini user
    const niniUser = await User.findOne({ username: 'nini.nini' });
    if (!niniUser) {
      console.log('❌ User nini.nini not found');
      return;
    }

    console.log('👤 Found user nini.nini:');
    console.log(`   📧 Email: ${niniUser.email}`);
    console.log(`   📱 Phone: ${niniUser.phoneNumber}`);
    console.log(`   📊 Current subscription status: ${niniUser.subscriptionStatus}`);
    console.log(`   💳 Payment required: ${niniUser.paymentRequired}`);

    // Find nini's subscriptions
    const niniSubscriptions = await Subscription.find({ user: niniUser._id })
      .populate('activePlan')
      .sort({ createdAt: -1 });

    console.log(`\n📋 Found ${niniSubscriptions.length} subscriptions for nini.nini:`);
    
    for (let i = 0; i < niniSubscriptions.length; i++) {
      const sub = niniSubscriptions[i];
      console.log(`\n   ${i + 1}. Subscription ${sub._id}:`);
      console.log(`      📊 Status: ${sub.status}`);
      console.log(`      💳 Payment Status: ${sub.paymentStatus}`);
      console.log(`      📅 Start Date: ${sub.startDate}`);
      console.log(`      📅 End Date: ${sub.endDate}`);
      console.log(`      📦 Plan: ${sub.activePlan?.title || 'No plan'}`);
      console.log(`      🕐 Created: ${sub.createdAt}`);
      console.log(`      🕐 Updated: ${sub.updatedAt}`);
    }

    // Find the most recent subscription
    const latestSubscription = niniSubscriptions[0];
    
    if (!latestSubscription) {
      console.log('\n❌ No subscriptions found for nini.nini');
      return;
    }

    console.log('\n🎯 Latest subscription analysis:');
    console.log(`   📊 Status: ${latestSubscription.status}`);
    console.log(`   💳 Payment Status: ${latestSubscription.paymentStatus}`);

    // Check if subscription needs to be activated
    if (latestSubscription.paymentStatus === 'pending' || latestSubscription.status === 'pending') {
      console.log('\n🔄 Subscription needs activation. Checking payment...');

      // Try to find a recent payment or activate manually
      const now = new Date();
      const timeSinceCreation = now - new Date(latestSubscription.createdAt);
      const minutesAgo = Math.floor(timeSinceCreation / (1000 * 60));

      console.log(`⏰ Subscription created ${minutesAgo} minutes ago`);

      if (minutesAgo < 120) { // If created within last 2 hours
        console.log('💡 Recent subscription detected, activating...');

        // Calculate subscription dates
        const startDate = new Date();
        const endDate = new Date();
        const planDuration = latestSubscription.activePlan?.duration || 6;
        endDate.setMonth(endDate.getMonth() + planDuration);

        const formattedStartDate = startDate.toISOString().split('T')[0];
        const formattedEndDate = endDate.toISOString().split('T')[0];

        // Update subscription
        latestSubscription.paymentStatus = 'paid';
        latestSubscription.status = 'active';
        latestSubscription.startDate = formattedStartDate;
        latestSubscription.endDate = formattedEndDate;

        await latestSubscription.save();
        console.log('✅ Subscription activated');

        // Update user status
        niniUser.subscriptionStatus = 'active';
        niniUser.paymentRequired = false;
        niniUser.subscriptionStartDate = startDate;
        niniUser.subscriptionEndDate = endDate;

        await niniUser.save();
        console.log('✅ User status updated');

        // Simulate webhook to ensure all systems are updated
        console.log('\n📡 Sending webhook to ensure real-time update...');
        
        const webhookPayload = {
          order_id: `MANUAL_FIX_NINI_${Date.now()}`,
          payment_status: 'COMPLETED',
          reference: `REF_NINI_FIX_${Date.now()}`,
          amount: latestSubscription.activePlan?.discountedPrice?.toString() || '500',
          currency: 'TZS',
          buyer_name: 'nini nini',
          buyer_phone: niniUser.phoneNumber,
          buyer_email: niniUser.email,
          timestamp: new Date().toISOString(),
          metadata: {
            manual_fix: true,
            user: 'nini.nini',
            reason: 'subscription_status_not_updating'
          }
        };

        try {
          const webhookResponse = await axios.post('http://localhost:5000/api/payment/webhook', webhookPayload, {
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': process.env.ZENOPAY_API_KEY
            },
            timeout: 10000
          });

          if (webhookResponse.status === 200) {
            console.log('✅ Webhook processed successfully');
            console.log('🎉 nini.nini subscription should now be active in real-time!');
          }
        } catch (webhookError) {
          console.log('⚠️ Webhook failed but subscription is still activated:', webhookError.message);
        }

      } else {
        console.log('⚠️ Subscription is too old, manual review needed');
      }
    } else {
      console.log('\n✅ Subscription is already active');
    }

    // Final verification
    console.log('\n🎯 FINAL STATUS:');
    console.log('=' .repeat(40));

    const updatedUser = await User.findById(niniUser._id);
    const updatedSubscription = await Subscription.findById(latestSubscription._id).populate('activePlan');

    console.log('👤 User Status:');
    console.log(`   📊 Subscription Status: ${updatedUser.subscriptionStatus}`);
    console.log(`   💳 Payment Required: ${updatedUser.paymentRequired}`);
    console.log(`   📅 Subscription End Date: ${updatedUser.subscriptionEndDate}`);

    console.log('\n📋 Subscription Status:');
    console.log(`   📊 Status: ${updatedSubscription.status}`);
    console.log(`   💳 Payment Status: ${updatedSubscription.paymentStatus}`);
    console.log(`   📅 Start Date: ${updatedSubscription.startDate}`);
    console.log(`   📅 End Date: ${updatedSubscription.endDate}`);
    console.log(`   📦 Plan: ${updatedSubscription.activePlan?.title}`);

    console.log('\n🎉 nini.nini subscription fix completed!');
    console.log('💡 User should now see active subscription status immediately');

  } catch (error) {
    console.error('❌ Error fixing nini.nini subscription:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Disconnected from database');
  }
}

fixNiniSubscription();
