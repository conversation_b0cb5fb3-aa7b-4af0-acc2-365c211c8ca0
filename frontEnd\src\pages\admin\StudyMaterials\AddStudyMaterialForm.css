.add-material-form {
  max-width: 100%;
}

.form-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

.form-header-icon {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
}

.form-header-icon svg {
  font-size: 3rem;
  color: #3498db;
  margin-bottom: 0.5rem;
}

.form-header-icon h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.material-form {
  max-width: 100%;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-item-half {
  margin-bottom: 1rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-input::placeholder {
  color: #95a5a6;
}

/* Ant Design Form Item Styling */
.ant-form-item-label > label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.ant-select-selector {
  border: 2px solid #e8e8e8 !important;
  border-radius: 8px !important;
  padding: 0.25rem 0.5rem !important;
  height: auto !important;
  min-height: 48px !important;
}

.ant-select-focused .ant-select-selector {
  border-color: #3498db !important;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1) !important;
}

.ant-select-selection-item {
  line-height: 32px !important;
  font-size: 1rem !important;
}

.ant-select-selection-placeholder {
  line-height: 32px !important;
  color: #95a5a6 !important;
  font-size: 1rem !important;
}

/* Upload Method Selector */
.upload-method-section {
  margin-bottom: 2rem;
}

.upload-method-selector {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
  margin-top: 0.5rem;
}

.method-option {
  border: 2px solid #e8e8e8;
  border-radius: 12px;
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.method-option:hover {
  border-color: #3498db;
  background: #f8fafc;
}

.method-option.active {
  border-color: #3498db;
  background: #f0f8ff;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.method-icon {
  font-size: 2rem;
  color: #3498db;
  margin-bottom: 0.5rem;
}

.method-option span {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.method-option p {
  font-size: 0.875rem;
  color: #7f8c8d;
  margin: 0;
}

/* Upload Section Styling */
.upload-section {
  margin-bottom: 1.5rem;
}

.document-upload .ant-upload-list,
.thumbnail-upload .ant-upload-list,
.video-upload .ant-upload-list {
  margin-top: 1rem;
}

.upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  background: #f8fafc;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #3498db;
  background: #f0f8ff;
}

.upload-area.small {
  padding: 1.5rem;
}

/* Enhanced drag-and-drop styles for thumbnail */
.thumbnail-drop-zone {
  position: relative;
  transition: all 0.3s ease;
}

.thumbnail-drop-zone:hover {
  border-color: #3498db;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
}

.thumbnail-drop-zone.drag-over {
  border-color: #2ecc71;
  background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%);
  border-style: solid;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

.upload-icon {
  font-size: 2.5rem;
  color: #3498db;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.thumbnail-drop-zone:hover .upload-icon {
  color: #2980b9;
  transform: scale(1.1);
}

.upload-area p {
  margin: 0.25rem 0;
  color: #374151;
  font-weight: 500;
}

.upload-hint {
  font-size: 0.875rem;
  color: #6b7280 !important;
  font-weight: 400 !important;
}

/* Upload Progress Section */
.upload-progress-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-header {
  margin-bottom: 1rem;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.upload-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 0.5rem;
}

.upload-speed,
.estimated-time {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.progress-text {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1rem;
}

.progress-percentage {
  font-weight: 700;
  color: #3498db;
  font-size: 1.1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3498db, #2980b9);
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-details {
  text-align: center;
  margin-top: 1rem;
}

.uploading-info,
.processing-info {
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 500;
}

.uploading-info {
  background: #e3f2fd;
  border: 1px solid #bbdefb;
  color: #1976d2;
}

.processing-info {
  background: #e8f5e8;
  border: 1px solid #c8e6c9;
  color: #388e3c;
}

.uploading-info span,
.processing-info span {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 600;
}

.uploading-info small,
.processing-info small {
  color: #666;
  font-size: 0.8rem;
  font-weight: 400;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 2px solid #f0f0f0;
}

.cancel-btn {
  background: #95a5a6 !important;
  border-color: #95a5a6 !important;
  color: white !important;
  font-weight: 500 !important;
  height: 48px !important;
  padding: 0 2rem !important;
  border-radius: 8px !important;
}

.cancel-btn:hover {
  background: #7f8c8d !important;
  border-color: #7f8c8d !important;
}

.submit-btn {
  background: #3498db !important;
  border-color: #3498db !important;
  font-weight: 500 !important;
  height: 48px !important;
  padding: 0 2rem !important;
  border-radius: 8px !important;
}

.submit-btn:hover {
  background: #2980b9 !important;
  border-color: #2980b9 !important;
}

/* File Upload List Styling */
.ant-upload-list-item {
  border-radius: 8px !important;
  border: 1px solid #e8e8e8 !important;
  background: #f8fafc !important;
}

.ant-upload-list-item-name {
  color: #2c3e50 !important;
  font-weight: 500 !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .upload-method-selector {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }
}

@media (max-width: 768px) {
  .form-card {
    padding: 1.5rem;
    margin: 0 0.5rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .form-actions {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .cancel-btn,
  .submit-btn {
    width: 100%;
  }
  
  .upload-area {
    padding: 1.5rem;
  }
  
  .upload-icon {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .form-card {
    padding: 1rem;
  }
  
  .form-header-icon svg {
    font-size: 2.5rem;
  }
  
  .form-header-icon h3 {
    font-size: 1.3rem;
  }
  
  .upload-area {
    padding: 1rem;
  }
  
  .upload-area p {
    font-size: 0.9rem;
  }

  .upload-method-selector {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .method-option {
    padding: 1rem;
  }

  .method-icon {
    font-size: 1.5rem;
  }

  .upload-progress-section {
    margin: 1.5rem 0;
    padding: 1rem;
  }

  .progress-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .processing-info {
    padding: 0.5rem;
    font-size: 0.9rem;
  }
}

/* Additional Classes Section */
.additional-classes-section {
  margin-bottom: 1.5rem;
}

.additional-classes-info {
  margin-bottom: 0.75rem;
}

.additional-classes-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.additional-classes-note {
  margin-top: 0.5rem;
}

.additional-classes-note small {
  color: #888;
  font-style: italic;
  display: block;
  margin-top: 0.25rem;
}

/* Multi-select styling */
.ant-select-multiple .ant-select-selection-item {
  background: #e3f2fd;
  border: 1px solid #2196f3;
  color: #1976d2;
  border-radius: 4px;
  font-size: 0.85rem;
}

.ant-select-multiple .ant-select-selection-item-remove {
  color: #1976d2;
}

.ant-select-multiple .ant-select-selection-item-remove:hover {
  color: #d32f2f;
}
