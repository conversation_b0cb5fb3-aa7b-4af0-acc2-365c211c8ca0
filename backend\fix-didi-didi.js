const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const Plan = require('./models/planModel');

async function fixDidiDidi() {
  try {
    console.log('🔧 Checking and fixing didi.didi subscription...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    // Find the user
    const user = await User.findOne({ username: 'didi.didi' });
    if (!user) {
      console.log('❌ User didi.didi not found');
      return;
    }
    
    console.log('👤 User found:', user.firstName, user.lastName);
    console.log('   Current status:', user.subscriptionStatus);
    console.log('   Payment required:', user.paymentRequired);
    
    // Find their subscription
    const subscription = await Subscription.findOne({ user: user._id })
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    if (!subscription) {
      console.log('❌ No subscription found for this user');
      return;
    }
    
    console.log('\n💳 Latest subscription:');
    console.log('   Plan:', subscription.activePlan?.title);
    console.log('   Amount:', subscription.activePlan?.discountedPrice);
    console.log('   Status:', subscription.status);
    console.log('   Payment Status:', subscription.paymentStatus);
    console.log('   Created:', subscription.createdAt);
    
    const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
    const orderId = latestPayment?.orderId;
    
    console.log('   Order ID:', orderId);
    
    if (!orderId) {
      console.log('❌ No order ID found');
      return;
    }
    
    // Method 1: Try to verify payment status with ZenoPay API
    console.log('\n🔍 Method 1: Checking payment status with ZenoPay API...');
    
    try {
      const statusUrl = `https://api.zenoapi.com/api/v1/payments/${orderId}`;
      const response = await axios.get(statusUrl, {
        headers: {
          "x-api-key": process.env.ZENOPAY_API_KEY,
        },
        timeout: 15000
      });
      
      console.log('📊 ZenoPay API Response:', JSON.stringify(response.data, null, 2));
      
      if (response.data && response.data.data && response.data.data.length > 0) {
        const paymentData = response.data.data[0];
        
        if (paymentData.payment_status === 'COMPLETED') {
          console.log('✅ Payment is COMPLETED according to ZenoPay! Activating...');
          await activateSubscription(subscription, user, paymentData);
          return;
        } else {
          console.log(`⏳ Payment status: ${paymentData.payment_status}`);
        }
      } else {
        console.log('⏳ No payment data found in ZenoPay API response');
      }
      
    } catch (apiError) {
      console.log('❌ ZenoPay API check failed:', apiError.message);
    }
    
    // Method 2: Try manual verification endpoint
    console.log('\n🔍 Method 2: Using manual verification endpoint...');
    
    try {
      const verifyResponse = await axios.post('http://localhost:5000/api/payment/verify-payment', {
        orderId: orderId
      });
      
      console.log('✅ Manual verification response:', verifyResponse.data);
      
      // Check if subscription was activated
      const updatedSub = await Subscription.findById(subscription._id);
      if (updatedSub.status === 'active') {
        console.log('🎉 Subscription was activated by verification service!');
        return;
      }
      
    } catch (verifyError) {
      console.log('❌ Manual verification failed:', verifyError.response?.data || verifyError.message);
    }
    
    // Method 3: Simulate webhook (if payment is legitimate)
    console.log('\n🔍 Method 3: Simulating webhook for recent payment...');
    
    const timeSinceCreated = Date.now() - new Date(subscription.createdAt).getTime();
    const minutesAgo = Math.floor(timeSinceCreated / (1000 * 60));
    
    console.log(`⏰ Subscription was created ${minutesAgo} minutes ago`);
    
    if (minutesAgo < 60) { // If created within last hour, likely legitimate
      console.log('💡 Recent subscription detected, simulating successful webhook...');
      
      const webhookPayload = {
        order_id: orderId,
        payment_status: 'COMPLETED',
        reference: `MANUAL_FIX_DIDI_${Date.now()}`,
        amount: subscription.activePlan?.discountedPrice?.toString() || '500',
        currency: 'TZS',
        buyer_name: 'didi didi',
        buyer_phone: user.phoneNumber,
        timestamp: new Date().toISOString(),
        metadata: {
          manual_fix: true,
          user: 'didi.didi',
          reason: 'webhook_not_received'
        }
      };
      
      try {
        const webhookResponse = await axios.post('http://localhost:5000/api/payment/webhook', webhookPayload, {
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': process.env.ZENOPAY_API_KEY
          }
        });
        
        console.log('✅ Webhook simulation successful:', webhookResponse.data);
        
        // Verify the fix worked
        const finalUser = await User.findById(user._id);
        const finalSub = await Subscription.findById(subscription._id);
        
        console.log('\n🎉 Final Status:');
        console.log('   User Status:', finalUser.subscriptionStatus);
        console.log('   Payment Required:', finalUser.paymentRequired);
        console.log('   Subscription Status:', finalSub.status);
        console.log('   Payment Status:', finalSub.paymentStatus);
        
        if (finalUser.subscriptionStatus === 'active' && finalSub.status === 'active') {
          console.log('✅ SUCCESS! didi.didi subscription is now active!');
          console.log('📱 User should now see active status on subscription page');
        }
        
      } catch (webhookError) {
        console.log('❌ Webhook simulation failed:', webhookError.response?.data || webhookError.message);
      }
    } else {
      console.log('⚠️ Subscription is older, manual review recommended');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

async function activateSubscription(subscription, user, paymentData) {
  try {
    console.log('🔄 Activating subscription...');
    
    // Calculate subscription dates
    const startDate = new Date();
    const endDate = new Date();
    const planDuration = subscription.activePlan?.duration || 6; // Standard is 6 months
    endDate.setMonth(endDate.getMonth() + planDuration);
    
    const formattedStartDate = startDate.toISOString().split('T')[0];
    const formattedEndDate = endDate.toISOString().split('T')[0];
    
    // Update subscription
    subscription.paymentStatus = 'paid';
    subscription.status = 'active';
    subscription.startDate = formattedStartDate;
    subscription.endDate = formattedEndDate;
    
    // Update payment history
    if (subscription.paymentHistory.length > 0) {
      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      latestPayment.paymentStatus = 'paid';
      latestPayment.referenceId = paymentData.reference || `API_VERIFIED_${Date.now()}`;
    }
    
    await subscription.save();
    
    // Update user status
    user.subscriptionStatus = 'active';
    user.paymentRequired = false;
    user.subscriptionEndDate = new Date(formattedEndDate);
    
    await user.save();
    
    console.log('✅ Subscription activated successfully!');
    console.log(`📅 Period: ${formattedStartDate} to ${formattedEndDate}`);
    
  } catch (error) {
    console.error('❌ Error activating subscription:', error.message);
  }
}

// Run the fix
fixDidiDidi();
