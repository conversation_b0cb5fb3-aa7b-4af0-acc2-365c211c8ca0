const router = require("express").Router();
const authMiddleware = require("../middlewares/authMiddleware");
const NotificationService = require("../services/notificationService");
const OnlineStatusService = require("../services/onlineStatusService");

// Get user notifications
router.get("/", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { page, limit, unreadOnly, type } = req.query;
    
    const result = await NotificationService.getUserNotifications(userId, {
      page: parseInt(page) || 1,
      limit: parseInt(limit) || 20,
      unreadOnly: unreadOnly === 'true',
      type: type || null
    });
    
    res.send({
      message: "Notifications retrieved successfully",
      success: true,
      data: result
    });
    
  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get unread notification count
router.get("/unread-count", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    
    const result = await NotificationService.getUserNotifications(userId, {
      limit: 1,
      unreadOnly: true
    });
    
    res.send({
      message: "Unread count retrieved successfully",
      success: true,
      data: {
        unreadCount: result.unreadCount
      }
    });
    
  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Mark notification as read
router.put("/:id/read", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const notificationId = req.params.id;
    
    const notification = await NotificationService.markAsRead(notificationId, userId);
    
    res.send({
      message: "Notification marked as read",
      success: true,
      data: notification
    });
    
  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Mark all notifications as read
router.put("/mark-all-read", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    
    const result = await NotificationService.markAllAsRead(userId);
    
    res.send({
      message: "All notifications marked as read",
      success: true,
      data: result
    });
    
  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Update user activity (heartbeat)
router.post("/heartbeat", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    await OnlineStatusService.updateUserActivity(userId);

    res.send({
      message: "Activity updated",
      success: true
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Set user online
router.post("/online", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    await OnlineStatusService.setUserOnline(userId);

    res.send({
      message: "User set as online",
      success: true
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Set user offline
router.post("/offline", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    await OnlineStatusService.setUserOffline(userId);

    res.send({
      message: "User set as offline",
      success: true
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get online users
router.get("/online-users", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { limit } = req.query;
    
    const users = await OnlineStatusService.getOnlineUsers({
      limit: parseInt(limit) || 50,
      excludeUserId: userId
    });
    
    res.send({
      message: "Online users retrieved successfully",
      success: true,
      data: users
    });
    
  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get online count
router.get("/online-count", async (req, res) => {
  try {
    const count = await OnlineStatusService.getOnlineCount();
    
    res.send({
      message: "Online count retrieved successfully",
      success: true,
      data: {
        onlineCount: count
      }
    });
    
  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Check user online status
router.get("/status/:userId", authMiddleware, async (req, res) => {
  try {
    const targetUserId = req.params.userId;

    const status = await OnlineStatusService.getUserStatus(targetUserId);

    res.send({
      message: "User status retrieved successfully",
      success: true,
      data: status
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Admin notification routes
router.post("/admin/send", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { title, message, recipients, specificUsers, level, class: targetClass, priority } = req.body;

    // Check if user is admin
    const User = require("../models/userModel");
    const adminUser = await User.findById(userId);
    if (!adminUser || !adminUser.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    const result = await NotificationService.sendAdminNotification({
      title,
      message,
      recipients,
      specificUsers,
      level,
      class: targetClass,
      priority,
      senderId: userId
    });

    res.send({
      message: "Notification sent successfully",
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

router.get("/admin/sent", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;

    // Check if user is admin
    const User = require("../models/userModel");
    const adminUser = await User.findById(userId);
    if (!adminUser || !adminUser.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    const result = await NotificationService.getAdminSentNotifications(userId);

    res.send({
      message: "Admin notifications retrieved successfully",
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

router.delete("/admin/:notificationId", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const { notificationId } = req.params;

    // Check if user is admin
    const User = require("../models/userModel");
    const adminUser = await User.findById(userId);
    if (!adminUser || !adminUser.isAdmin) {
      return res.status(403).send({
        message: "Access denied. Admin privileges required.",
        success: false,
      });
    }

    const result = await NotificationService.deleteAdminNotification(notificationId, userId);

    res.send({
      message: "Notification deleted successfully",
      success: true,
      data: result
    });

  } catch (error) {
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

module.exports = router;
