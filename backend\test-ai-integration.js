const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Test AI integration with syllabus data
async function testAIIntegration() {
  console.log('🤖 Testing AI Integration with Syllabus Data...\n');

  try {
    // Connect to database to verify data
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    const Syllabus = require('./models/syllabusModel');

    // Check current syllabus data
    console.log('\n1️⃣ Current syllabus data...');
    const syllabuses = await Syllabus.find({ isActive: true, processingStatus: 'completed' });
    console.log(`Found ${syllabuses.length} active completed syllabuses:`);
    
    syllabuses.forEach((syllabus, index) => {
      console.log(`${index + 1}. ${syllabus.subject} (${syllabus.level}) - Classes: ${syllabus.classes.join(', ')}`);
    });

    // Test API endpoints that should work
    console.log('\n2️⃣ Testing API endpoints...');
    const baseURL = 'http://localhost:5000';

    // Test with a mock token (this will fail auth but show endpoint structure)
    const testToken = 'Bearer test-token';
    
    try {
      console.log('Testing /api/syllabus/subjects/primary...');
      const response = await axios.get(`${baseURL}/api/syllabus/subjects/primary`, {
        headers: { 'Authorization': testToken }
      });
      console.log('❌ Should require valid authentication');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Endpoint exists and requires authentication');
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.message);
      }
    }

    try {
      console.log('Testing /api/ai-questions/subjects/primary...');
      const response = await axios.get(`${baseURL}/api/ai-questions/subjects/primary`, {
        headers: { 'Authorization': testToken }
      });
      console.log('❌ Should require valid authentication');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Endpoint exists and requires authentication');
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.message);
      }
    }

    // Test syllabus methods directly
    console.log('\n3️⃣ Testing syllabus methods...');
    
    // Test getAvailableSubjects
    const primarySubjects = await Syllabus.getAvailableSubjects('primary');
    console.log('Primary subjects from syllabus:', primarySubjects);
    
    const primaryClass5Subjects = await Syllabus.getAvailableSubjects('primary', '5');
    console.log('Primary Class 5 subjects from syllabus:', primaryClass5Subjects);
    
    // Test findForAIGeneration
    if (primaryClass5Subjects.length > 0) {
      const testSubject = primaryClass5Subjects[0];
      const syllabus = await Syllabus.findForAIGeneration('primary', '5', testSubject);
      if (syllabus) {
        console.log(`✅ Found syllabus for Primary Class 5 ${testSubject}`);
        console.log(`   Title: ${syllabus.title}`);
        console.log(`   Classes covered: ${syllabus.classes.join(', ')}`);
        console.log(`   Extracted text length: ${syllabus.extractedText.length}`);
        
        // Test getTopicsForAI method
        const topics = syllabus.getTopicsForAI();
        console.log(`   Topics available: ${Object.keys(topics).length}`);
        if (Object.keys(topics).length > 0) {
          console.log(`   Sample topics: ${Object.keys(topics).slice(0, 3).join(', ')}`);
        }
      } else {
        console.log(`❌ No syllabus found for Primary Class 5 ${testSubject}`);
      }
    }

    console.log('\n🎯 Integration Test Summary:');
    console.log('✅ Database connection working');
    console.log('✅ Syllabus data exists and is accessible');
    console.log('✅ API endpoints exist and require authentication');
    console.log('✅ Syllabus methods working correctly');
    
    if (syllabuses.length > 0) {
      console.log('✅ AI question generation should now see syllabus-based subjects');
      console.log('\n📋 Expected behavior in AI interface:');
      console.log('1. Select "Primary" level');
      console.log('2. Should see subjects from uploaded syllabuses');
      console.log('3. Select classes that have syllabuses');
      console.log('4. Should see topics from actual curriculum');
    } else {
      console.log('⚠️  No completed syllabuses found - upload and process PDFs first');
    }

  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from database');
  }
}

// Test the specific case we found
async function testSpecificCase() {
  console.log('\n🎯 Testing Specific Case: Primary Science and Technology...\n');

  try {
    await mongoose.connect(process.env.MONGO_URL);
    const Syllabus = require('./models/syllabusModel');

    // Test the specific syllabus we found
    const testCases = [
      { level: 'primary', class: '3', subject: 'science and technology' },
      { level: 'primary', class: '4', subject: 'science and technology' },
      { level: 'primary', class: '5', subject: 'science and technology' },
      { level: 'primary', class: '6', subject: 'science and technology' },
    ];

    console.log('Testing AI generation lookup for uploaded syllabus...');
    for (const testCase of testCases) {
      const result = await Syllabus.findForAIGeneration(
        testCase.level, 
        testCase.class, 
        testCase.subject
      );
      
      if (result) {
        console.log(`✅ ${testCase.level} Class ${testCase.class} ${testCase.subject} - FOUND`);
        console.log(`   Topics available: ${result.extractedTopics.length}`);
        console.log(`   Quality score: ${result.qualityScore}`);
      } else {
        console.log(`❌ ${testCase.level} Class ${testCase.class} ${testCase.subject} - NOT FOUND`);
      }
    }

    // Test subjects lookup
    console.log('\nTesting subjects lookup...');
    const subjects = await Syllabus.getAvailableSubjects('primary');
    console.log('Available primary subjects:', subjects);

    const class5Subjects = await Syllabus.getAvailableSubjects('primary', '5');
    console.log('Available primary class 5 subjects:', class5Subjects);

  } catch (error) {
    console.error('❌ Specific test failed:', error.message);
  } finally {
    await mongoose.disconnect();
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting AI Integration Tests\n');
  console.log('=' .repeat(60));
  
  await testAIIntegration();
  await testSpecificCase();
  
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 AI Integration tests completed!');
  
  console.log('\n🔧 Next Steps:');
  console.log('1. Open http://localhost:3000/admin/ai-questions');
  console.log('2. Select "Primary" level');
  console.log('3. Should see "science and technology" in subjects');
  console.log('4. Select classes 3, 4, 5, or 6');
  console.log('5. Generate questions using actual syllabus content');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testAIIntegration,
  testSpecificCase,
  runAllTests
};
