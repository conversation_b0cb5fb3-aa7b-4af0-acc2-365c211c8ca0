/* Simple CSS animations to replace Framer Motion */

/* Responsive Quiz Grid */
.quiz-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(2, 1fr); /* Mobile: 2 cards */
}

/* Tablets: 3 cards */
@media (min-width: 768px) {
  .quiz-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Laptops: 4 cards */
@media (min-width: 1024px) {
  .quiz-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Large screens: 5 cards */
@media (min-width: 1280px) {
  .quiz-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDelay {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInStagger {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-fade-in-delay {
  animation: fadeInDelay 0.6s ease-out 0.1s forwards;
}

.animate-fade-in-delay-2 {
  animation: fadeInDelay 0.6s ease-out 0.2s forwards;
}

.animate-fade-in-stagger {
  animation: fadeInStagger 0.6s ease-out forwards;
}

/* Hover effects */
.hover-scale:hover {
  transform: scale(1.05);
}

.hover-scale:active {
  transform: scale(0.95);
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Responsive grid */
.quiz-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

@media (max-width: 768px) {
  .quiz-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Focus states */
button:focus,
input:focus,
select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Category tags responsive design */
.category-tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
}

/* Ensure single line text in category buttons */
.category-button-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* MOBILE: Force horizontal layout with 3+ tags per line */
@media (max-width: 768px) {
  .category-tags-container {
    gap: 0.25rem; /* Tighter spacing for mobile */
    justify-content: flex-start;
  }

  .mobile-category-button {
    flex: 0 0 auto; /* Don't grow or shrink */
    min-width: calc(33.333% - 0.5rem); /* At least 3 per row */
    max-width: calc(50% - 0.25rem); /* Max 2 per row if needed */
    font-size: 0.75rem; /* Smaller text */
    padding: 0.375rem 0.5rem; /* Smaller padding */
  }

  .mobile-category-button .category-button-text {
    font-size: 0.75rem;
    max-width: none; /* Remove text width limit */
  }

  .mobile-category-button .category-count {
    font-size: 0.625rem; /* Even smaller count */
    padding: 0.125rem 0.375rem;
  }
}

/* Tablets and larger - normal behavior */
@media (min-width: 769px) {
  .category-tags-container {
    gap: 0.75rem;
  }
}
