const mongoose = require('mongoose');
const User = require('./models/userModel');
const Exam = require('./models/examModel');
const Report = require('./models/reportModel');
const XPTransaction = require('./models/xpTransactionModel');
const Question = require('./models/questionModel');
const enhancedXPService = require('./services/enhancedXPService');
require('dotenv').config();

async function testQuizXPFlow() {
  try {
    console.log('🔍 Testing Quiz XP Flow...\n');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');

    // Find a test user (non-admin)
    const testUser = await User.findOne({ isAdmin: { $ne: true } }).limit(1);
    if (!testUser) {
      console.log('❌ No test user found');
      return;
    }

    console.log(`👤 Test User: ${testUser.name} (${testUser.email})`);
    console.log(`📊 Current XP: ${testUser.totalXP || 0}`);
    console.log(`🎯 Current Level: ${testUser.currentLevel || 1}\n`);

    // Find a test exam
    const testExam = await Exam.findOne().limit(1);
    if (!testExam) {
      console.log('❌ No test exam found');
      return;
    }

    console.log(`📝 Test Exam: ${testExam.name}`);
    console.log(`📚 Subject: ${testExam.subject || 'General'}`);
    console.log(`⏱️ Duration: ${testExam.duration || 30} minutes`);
    console.log(`❓ Questions: ${testExam.questions?.length || 0}\n`);

    // Simulate quiz result
    const mockResult = {
      correctAnswers: [
        { name: 'Question 1', userAnswer: 'A' },
        { name: 'Question 2', userAnswer: 'B' },
        { name: 'Question 3', userAnswer: 'C' }
      ],
      wrongAnswers: [
        { name: 'Question 4', userAnswer: 'D' }
      ],
      verdict: 'Pass',
      score: 75,
      points: 30,
      totalQuestions: 4,
      timeSpent: 300 // 5 minutes
    };

    console.log('🎯 Mock Quiz Result:');
    console.log(`   Score: ${mockResult.score}%`);
    console.log(`   Verdict: ${mockResult.verdict}`);
    console.log(`   Correct: ${mockResult.correctAnswers.length}`);
    console.log(`   Wrong: ${mockResult.wrongAnswers.length}`);
    console.log(`   Time Spent: ${mockResult.timeSpent} seconds\n`);

    // Test XP calculation
    console.log('💰 Calculating XP...');
    const xpResult = await enhancedXPService.calculateQuizXP({
      userId: testUser._id,
      examData: {
        _id: testExam._id,
        subject: testExam.subject || 'General',
        difficulty: testExam.difficulty || 'medium',
        duration: testExam.duration || 30,
        name: testExam.name
      },
      result: mockResult,
      timeSpent: mockResult.timeSpent,
      isFirstAttempt: true,
      previousScore: null
    });

    console.log(`   XP Awarded: ${xpResult.xpAwarded}`);
    console.log(`   Breakdown:`, JSON.stringify(xpResult.breakdown, null, 2));

    // Test XP awarding
    console.log('\n🏆 Awarding XP...');
    const xpAwardResult = await enhancedXPService.awardXP({
      userId: testUser._id,
      xpAmount: xpResult.xpAwarded,
      transactionType: 'quiz_completion',
      sourceId: testExam._id,
      sourceModel: 'exams',
      breakdown: xpResult.breakdown,
      quizData: {
        examId: testExam._id,
        subject: testExam.subject || 'General',
        difficulty: testExam.difficulty || 'medium',
        questionsTotal: mockResult.totalQuestions,
        questionsCorrect: mockResult.correctAnswers.length,
        timeSpent: mockResult.timeSpent,
        score: mockResult.score,
        isFirstAttempt: true,
      },
      metadata: {
        testRun: true,
        verdict: mockResult.verdict
      }
    });

    console.log(`   Success: ${xpAwardResult.success}`);
    console.log(`   New Total XP: ${xpAwardResult.newTotalXP}`);
    console.log(`   Level Up: ${xpAwardResult.levelUp}`);
    console.log(`   New Level: ${xpAwardResult.newLevel}\n`);

    // Verify user was updated
    const updatedUser = await User.findById(testUser._id);
    console.log('✅ User Updated:');
    console.log(`   Total XP: ${updatedUser.totalXP}`);
    console.log(`   Current Level: ${updatedUser.currentLevel}`);
    console.log(`   Lifetime XP: ${updatedUser.lifetimeXP}`);
    console.log(`   Season XP: ${updatedUser.seasonXP}\n`);

    // Check XP transaction was created
    const transaction = await XPTransaction.findOne({ 
      user: testUser._id,
      transactionType: 'quiz_completion'
    }).sort({ createdAt: -1 });

    if (transaction) {
      console.log('✅ XP Transaction Created:');
      console.log(`   XP Amount: ${transaction.xpAmount}`);
      console.log(`   Transaction Type: ${transaction.transactionType}`);
      console.log(`   Created: ${transaction.createdAt}\n`);
    } else {
      console.log('❌ No XP transaction found\n');
    }

    // Test creating a report (simulating frontend flow)
    console.log('📝 Testing Report Creation...');
    const newReport = new Report({
      user: testUser._id,
      exam: testExam._id,
      result: mockResult
    });

    await newReport.save();
    console.log('✅ Report created successfully\n');

    console.log('🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testQuizXPFlow();
