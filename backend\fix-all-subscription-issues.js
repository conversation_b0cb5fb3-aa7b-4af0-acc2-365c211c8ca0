const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const Plan = require('./models/planModel');

async function fixAllSubscriptionIssues() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    console.log('🔍 Finding all users with subscription issues...\n');
    
    // Get all users with subscription issues
    const usersWithIssues = [];
    
    // Find all users who have subscriptions but status is free
    const freeUsers = await User.find({ subscriptionStatus: 'free' });
    
    for (const user of freeUsers) {
      const subscriptions = await Subscription.find({ user: user._id })
        .populate('activePlan', 'title duration discountedPrice')
        .sort({ createdAt: -1 });
      
      if (subscriptions.length > 0) {
        const paidSubscriptions = subscriptions.filter(sub => sub.paymentStatus === 'paid');
        const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
        const pendingSubscriptions = subscriptions.filter(sub => sub.paymentStatus === 'pending');
        
        // Check for issues
        const issues = [];
        
        if (paidSubscriptions.length > 0) {
          issues.push('HAS_PAID_BUT_FREE_STATUS');
        }
        
        if (activeSubscriptions.length > 0) {
          issues.push('HAS_ACTIVE_BUT_FREE_STATUS');
        }
        
        if (pendingSubscriptions.length > 1) {
          issues.push('MULTIPLE_PENDING_PAYMENTS');
        }
        
        if (issues.length > 0) {
          usersWithIssues.push({
            user,
            subscriptions,
            issues,
            paidCount: paidSubscriptions.length,
            activeCount: activeSubscriptions.length,
            pendingCount: pendingSubscriptions.length
          });
        }
      }
    }
    
    console.log(`🚨 Found ${usersWithIssues.length} users with subscription issues\n`);
    
    if (usersWithIssues.length === 0) {
      console.log('🎉 No subscription issues found! All users have correct status.');
      return;
    }
    
    // Display and fix issues
    for (let i = 0; i < usersWithIssues.length; i++) {
      const userData = usersWithIssues[i];
      const user = userData.user;
      
      console.log(`${i + 1}. 👤 ${user.firstName} ${user.lastName} (@${user.username})`);
      console.log(`   Issues: ${userData.issues.join(', ')}`);
      console.log(`   Subscriptions: ${userData.subscriptions.length} total (${userData.paidCount} paid, ${userData.activeCount} active, ${userData.pendingCount} pending)`);
      
      // Auto-fix logic
      let fixed = false;
      
      // If user has paid subscriptions but status is free
      if (userData.paidCount > 0 && user.subscriptionStatus === 'free') {
        console.log('   🔧 Fixing: User has paid subscriptions but status is free');
        
        const paidSub = userData.subscriptions.find(sub => sub.paymentStatus === 'paid');
        if (paidSub) {
          user.subscriptionStatus = 'active';
          user.paymentRequired = false;
          
          if (paidSub.endDate) {
            user.subscriptionEndDate = new Date(paidSub.endDate);
          }
          
          await user.save();
          console.log('   ✅ Fixed user status');
          fixed = true;
        }
      }
      
      // If user has active subscriptions but status is free
      if (userData.activeCount > 0 && user.subscriptionStatus === 'free') {
        console.log('   🔧 Fixing: User has active subscriptions but status is free');
        
        const activeSub = userData.subscriptions.find(sub => sub.status === 'active');
        if (activeSub) {
          user.subscriptionStatus = 'active';
          user.paymentRequired = false;
          
          if (activeSub.endDate) {
            user.subscriptionEndDate = new Date(activeSub.endDate);
          }
          
          await user.save();
          console.log('   ✅ Fixed user status');
          fixed = true;
        }
      }
      
      // If user has multiple pending subscriptions, activate the most recent one
      if (userData.pendingCount > 1 && userData.activeCount === 0) {
        console.log('   🔧 Fixing: Multiple pending subscriptions, activating most recent');
        
        const latestPending = userData.subscriptions.find(sub => sub.paymentStatus === 'pending');
        if (latestPending && latestPending.activePlan) {
          // Calculate subscription dates
          const startDate = new Date();
          const endDate = new Date();
          const planDuration = latestPending.activePlan.duration || 1;
          endDate.setMonth(endDate.getMonth() + planDuration);
          
          const formattedStartDate = startDate.toISOString().split('T')[0];
          const formattedEndDate = endDate.toISOString().split('T')[0];
          
          // Update subscription
          latestPending.paymentStatus = 'paid';
          latestPending.status = 'active';
          latestPending.startDate = formattedStartDate;
          latestPending.endDate = formattedEndDate;
          
          // Update payment history
          if (latestPending.paymentHistory.length > 0) {
            const latestPayment = latestPending.paymentHistory[latestPending.paymentHistory.length - 1];
            latestPayment.paymentStatus = 'paid';
            latestPayment.referenceId = `AUTO_FIX_${Date.now()}`;
          }
          
          await latestPending.save();
          
          // Update user status
          user.subscriptionStatus = 'active';
          user.paymentRequired = false;
          user.subscriptionEndDate = new Date(formattedEndDate);
          
          await user.save();
          
          console.log(`   ✅ Activated ${latestPending.activePlan.title} subscription`);
          console.log(`   📅 Period: ${formattedStartDate} to ${formattedEndDate}`);
          fixed = true;
        }
      }
      
      if (!fixed) {
        console.log('   ⚠️ Could not auto-fix this user - manual review needed');
      }
      
      console.log('');
    }
    
    console.log('🎉 Subscription fix process completed!');
    
    // Final verification
    console.log('\n📊 Running final verification...');
    const stillBrokenUsers = await User.find({ 
      subscriptionStatus: 'free',
      paymentRequired: true 
    });
    
    let actuallyBroken = 0;
    for (const user of stillBrokenUsers) {
      const subs = await Subscription.find({ 
        user: user._id,
        $or: [
          { paymentStatus: 'paid' },
          { status: 'active' }
        ]
      });
      
      if (subs.length > 0) {
        actuallyBroken++;
      }
    }
    
    console.log(`📈 Users still with issues: ${actuallyBroken}`);
    
    if (actuallyBroken === 0) {
      console.log('🎉 All subscription issues have been resolved!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the fix
fixAllSubscriptionIssues();
