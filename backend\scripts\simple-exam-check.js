const mongoose = require('mongoose');
require('dotenv').config();

async function simpleExamCheck() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Direct database query
        const db = mongoose.connection.db;
        
        // Find all secondary exams
        const secondaryExams = await db.collection('exams').find({
            level: { $in: ["secondary", "Secondary"] }
        }).toArray();

        console.log(`📊 Found ${secondaryExams.length} secondary exams`);

        // Look for biology or Form-1 exams
        const form1Exams = secondaryExams.filter(exam => 
            exam.class === 'Form-1' || exam.class === '1'
        );

        console.log(`📊 Found ${form1Exams.length} Form-1 exams`);

        form1Exams.forEach((exam, index) => {
            console.log(`\n📝 Exam ${index + 1}:`);
            console.log(`  Name: ${exam.name}`);
            console.log(`  Subject: ${exam.subject}`);
            console.log(`  Category: ${exam.category}`);
            console.log(`  Topic: ${exam.topic}`);
            console.log(`  Level: ${exam.level}`);
            console.log(`  Class: ${exam.class}`);
            
            // Check topic specifically
            if (exam.topic) {
                console.log(`  ✅ Topic exists: "${exam.topic}"`);
            } else {
                console.log(`  ❌ Topic missing or empty`);
            }
        });

        console.log('\n✅ Check completed!');
        
    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    simpleExamCheck();
}

module.exports = simpleExamCheck;
