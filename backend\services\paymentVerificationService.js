const axios = require('axios');
const Subscription = require('../models/subscriptionModel');
const User = require('../models/userModel');
require('dotenv').config();

class PaymentVerificationService {
  constructor() {
    this.isRunning = false;
    this.checkInterval = 15000; // Check every 15 seconds (more frequent)
    this.maxRetries = 5;
    this.autoFixEnabled = false; // SECURITY FIX: Disable automatic fixing to prevent unauthorized activations
    this.requireStrictVerification = true; // SECURITY FIX: Require strict payment verification
  }

  // Start the payment verification service
  start() {
    if (this.isRunning) {
      console.log('⚠️ Payment verification service is already running');
      return;
    }

    this.isRunning = true;
    console.log('🚀 Starting payment verification service...');
    console.log(`⏰ Checking every ${this.checkInterval / 1000} seconds`);
    
    this.intervalId = setInterval(() => {
      this.checkPendingPayments();
    }, this.checkInterval);
  }

  // Stop the payment verification service
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.isRunning = false;
      console.log('🛑 Payment verification service stopped');
    }
  }

  // SMART CHECK: Check all pending payments and auto-fix legitimate ones
  async checkPendingPayments() {
    try {
      console.log('🔍 SMART CHECK: Scanning for payment issues...');

      // Find all pending subscriptions created in the last 48 hours (extended window)
      const twoDaysAgo = new Date(Date.now() - 48 * 60 * 60 * 1000);

      const pendingSubscriptions = await Subscription.find({
        paymentStatus: 'pending',
        status: 'pending',
        createdAt: { $gte: twoDaysAgo }
      }).populate('user', 'firstName lastName username email')
        .populate('activePlan', 'title duration discountedPrice');

      // Also check for users with paid subscriptions but free status
      const usersWithIssues = await User.find({
        subscriptionStatus: 'free',
        paymentRequired: true
      });

      let totalIssues = pendingSubscriptions.length;
      let fixedIssues = 0;

      if (pendingSubscriptions.length > 0) {
        console.log(`📋 Found ${pendingSubscriptions.length} pending payments to verify`);

        for (const subscription of pendingSubscriptions) {
          const fixed = await this.smartVerifyAndFix(subscription);
          if (fixed) fixedIssues++;
          // Wait a bit between requests to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // Check for users with subscription mismatches
      for (const user of usersWithIssues) {
        const userSubs = await Subscription.find({ user: user._id })
          .populate('activePlan', 'title duration discountedPrice');

        const paidSubs = userSubs.filter(sub => sub.paymentStatus === 'paid');
        const activeSubs = userSubs.filter(sub => sub.status === 'active');

        if (paidSubs.length > 0 || activeSubs.length > 0) {
          totalIssues++;
          const fixed = await this.fixUserStatus(user, userSubs);
          if (fixed) fixedIssues++;
        }
      }

      if (totalIssues > 0) {
        console.log(`🔧 SMART CHECK RESULTS: Fixed ${fixedIssues}/${totalIssues} payment issues`);
      } else {
        console.log('✅ No payment issues found - all users have correct status');
      }

    } catch (error) {
      console.error('❌ Error in smart payment check:', error.message);
    }
  }

  // Verify a specific subscription payment with ZenoPay
  async verifySubscriptionPayment(subscription) {
    try {
      if (!subscription.paymentHistory || subscription.paymentHistory.length === 0) {
        console.log(`⚠️ No payment history for subscription ${subscription._id}`);
        return;
      }

      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      const orderId = latestPayment.orderId;

      console.log(`🔍 Verifying payment for order: ${orderId}`);

      // Check payment status with ZenoPay API
      const statusUrl = `https://api.zenoapi.com/api/v1/payments/${orderId}`;
      
      try {
        const response = await axios.get(statusUrl, {
          headers: {
            "x-api-key": process.env.ZENOPAY_API_KEY,
          },
          timeout: 15000
        });

        console.log(`📊 Payment status response for ${orderId}:`, response.data);

        // Check if payment is completed
        if (response.data && response.data.data && response.data.data.length > 0) {
          const paymentData = response.data.data[0];
          
          if (paymentData.payment_status === 'COMPLETED') {
            console.log(`✅ Payment ${orderId} is COMPLETED! Activating subscription...`);
            await this.activateSubscription(subscription, paymentData);
          } else {
            console.log(`⏳ Payment ${orderId} status: ${paymentData.payment_status}`);
          }
        } else {
          console.log(`⏳ No payment data found for ${orderId}`);
        }

      } catch (apiError) {
        console.log(`❌ Error checking payment status for ${orderId}:`, apiError.message);
        
        // If we can't verify with API, check if payment is old enough to auto-activate
        const paymentAge = Date.now() - new Date(subscription.createdAt).getTime();
        const oneHourInMs = 60 * 60 * 1000;
        
        if (paymentAge > oneHourInMs) {
          console.log(`⏰ Payment ${orderId} is over 1 hour old, considering auto-activation`);
          // You could implement auto-activation logic here for old payments
        }
      }

    } catch (error) {
      console.error(`❌ Error verifying subscription ${subscription._id}:`, error.message);
    }
  }

  // Activate a subscription when payment is confirmed
  async activateSubscription(subscription, paymentData) {
    try {
      console.log(`🔄 Activating subscription ${subscription._id}...`);

      // Calculate subscription dates
      const startDate = new Date();
      const endDate = new Date();
      const planDuration = subscription.activePlan?.duration || 1;
      endDate.setMonth(endDate.getMonth() + planDuration);

      const formattedStartDate = startDate.toISOString().split('T')[0];
      const formattedEndDate = endDate.toISOString().split('T')[0];

      // Update subscription
      subscription.paymentStatus = 'paid';
      subscription.status = 'active';
      subscription.startDate = formattedStartDate;
      subscription.endDate = formattedEndDate;

      // Update payment history
      if (subscription.paymentHistory.length > 0) {
        const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
        latestPayment.paymentStatus = 'paid';
        latestPayment.referenceId = paymentData.reference || `AUTO_VERIFIED_${Date.now()}`;
      }

      await subscription.save();

      // Update user status
      const user = await User.findById(subscription.user);
      if (user) {
        user.subscriptionStatus = 'active';
        user.paymentRequired = false;
        user.subscriptionEndDate = new Date(formattedEndDate);
        await user.save();
        
        console.log(`✅ Activated subscription for ${user.firstName} ${user.lastName}`);
        console.log(`📅 Subscription period: ${formattedStartDate} to ${formattedEndDate}`);
        console.log(`📋 Plan: ${subscription.activePlan?.title}`);
      }

    } catch (error) {
      console.error(`❌ Error activating subscription ${subscription._id}:`, error.message);
    }
  }

  // Manual verification for a specific order ID
  async verifyOrderId(orderId) {
    try {
      const subscription = await Subscription.findOne({
        'paymentHistory.orderId': orderId
      }).populate('user', 'firstName lastName username')
        .populate('activePlan', 'title duration discountedPrice');

      if (!subscription) {
        console.log(`❌ No subscription found for order ID: ${orderId}`);
        return false;
      }

      await this.verifySubscriptionPayment(subscription);
      return true;

    } catch (error) {
      console.error(`❌ Error verifying order ${orderId}:`, error.message);
      return false;
    }
  }

  // SECURITY FIX: Strict verification without auto-activation
  async smartVerifyAndFix(subscription) {
    try {
      const timeSinceCreated = Date.now() - new Date(subscription.createdAt).getTime();
      const hoursAgo = Math.floor(timeSinceCreated / (1000 * 60 * 60));

      console.log(`🔍 Smart checking: ${subscription.user?.firstName} ${subscription.user?.lastName} - ${hoursAgo}h ago`);

      // SECURITY FIX: Remove auto-activation logic
      if (!this.autoFixEnabled) {
        console.log(`🔒 Auto-fix disabled for security. Manual verification required for: ${subscription.user?.username}`);

        // Only verify with ZenoPay API, don't auto-activate
        if (subscription.paymentHistory.length > 0) {
          const payment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
          if (payment.orderId && payment.orderId.startsWith('ORDER_')) {
            console.log(`📋 Payment found: ${payment.orderId} - requires manual verification`);
            await this.logPendingVerification(subscription, payment.orderId);
          }
        }
        return false; // Don't auto-activate
      }

      // Legacy auto-fix logic (disabled by default)
      if (hoursAgo < 24 && subscription.paymentHistory.length > 0) {
        const payment = subscription.paymentHistory[subscription.paymentHistory.length - 1];

        if (payment.orderId && payment.orderId.startsWith('ORDER_')) {
          console.log(`⚠️ Would auto-fix payment: ${payment.orderId} (but auto-fix is disabled)`);
          return false;
        }
      }

      // Try ZenoPay API verification (but don't auto-activate)
      const apiResult = await this.verifySubscriptionPaymentOnly(subscription);
      return apiResult;

    } catch (error) {
      console.error(`❌ Smart verification failed for ${subscription._id}:`, error.message);
      return false;
    }
  }

  // Fix user status when they have paid subscriptions but free status
  async fixUserStatus(user, subscriptions) {
    try {
      const paidSubs = subscriptions.filter(sub => sub.paymentStatus === 'paid');
      const activeSubs = subscriptions.filter(sub => sub.status === 'active');

      const subToUse = activeSubs[0] || paidSubs[0];

      if (subToUse) {
        console.log(`🔧 Fixing user status: ${user.firstName} ${user.lastName} (@${user.username})`);

        const endDate = subToUse.endDate ? new Date(subToUse.endDate) : new Date();
        if (!subToUse.endDate) {
          const duration = subToUse.activePlan?.duration || 6;
          endDate.setMonth(endDate.getMonth() + duration);
        }

        await User.findByIdAndUpdate(user._id, {
          subscriptionStatus: 'active',
          paymentRequired: false,
          subscriptionEndDate: endDate
        });

        console.log(`✅ Fixed ${user.username} - status updated to active`);
        return true;
      }

      return false;

    } catch (error) {
      console.error(`❌ Error fixing user status for ${user.username}:`, error.message);
      return false;
    }
  }

  // SECURITY FIX: Log pending verifications without auto-activation
  async logPendingVerification(subscription, orderId) {
    try {
      console.log(`📋 PENDING VERIFICATION: Order ${orderId} for user ${subscription.user?.username}`);
      console.log(`   Subscription ID: ${subscription._id}`);
      console.log(`   Created: ${subscription.createdAt}`);
      console.log(`   Plan: ${subscription.activePlan?.title}`);
      console.log(`   ⚠️ REQUIRES MANUAL VERIFICATION BEFORE ACTIVATION`);
    } catch (error) {
      console.error(`❌ Error logging pending verification:`, error.message);
    }
  }

  // SECURITY FIX: Verify payment without auto-activation
  async verifySubscriptionPaymentOnly(subscription) {
    try {
      if (!subscription.paymentHistory || subscription.paymentHistory.length === 0) {
        console.log(`⚠️ No payment history for subscription ${subscription._id}`);
        return false;
      }

      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      const orderId = latestPayment.orderId;

      console.log(`🔍 Verifying payment for order: ${orderId} (verification only)`);

      // Check payment status with ZenoPay API
      const statusUrl = `https://api.zenoapi.com/api/v1/payments/${orderId}`;

      try {
        const response = await axios.get(statusUrl, {
          headers: {
            "x-api-key": process.env.ZENOPAY_API_KEY,
          },
          timeout: 15000
        });

        console.log(`📊 Payment status response for ${orderId}:`, response.data);

        // Check if payment is completed
        if (response.data && response.data.data && response.data.data.length > 0) {
          const paymentData = response.data.data[0];

          if (paymentData.payment_status === 'COMPLETED') {
            console.log(`✅ Payment ${orderId} is COMPLETED! But auto-activation is disabled.`);
            console.log(`🔒 MANUAL ACTIVATION REQUIRED for user: ${subscription.user?.username}`);
            return true; // Payment verified but not activated
          } else {
            console.log(`⏳ Payment ${orderId} status: ${paymentData.payment_status}`);
          }
        } else {
          console.log(`⏳ No payment data found for ${orderId}`);
        }

      } catch (apiError) {
        console.log(`❌ Error checking payment status for ${orderId}:`, apiError.message);
      }

      return false;

    } catch (error) {
      console.error(`❌ Error verifying subscription ${subscription._id}:`, error.message);
      return false;
    }
  }
}

// Export singleton instance
const paymentVerificationService = new PaymentVerificationService();
module.exports = paymentVerificationService;
