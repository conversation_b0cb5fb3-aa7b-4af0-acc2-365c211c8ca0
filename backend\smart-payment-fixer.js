const mongoose = require('mongoose');
const axios = require('axios');
require('dotenv').config();

// Import models
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const Plan = require('./models/planModel');

class SmartPaymentFixer {
  constructor() {
    this.fixedUsers = [];
    this.failedUsers = [];
  }

  async connectDB() {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
  }

  async findAllUsersWithPaymentIssues() {
    console.log('🔍 Finding ALL users with payment issues...');
    
    const allUsers = await User.find({});
    const usersWithIssues = [];
    
    for (const user of allUsers) {
      const subscriptions = await Subscription.find({ user: user._id })
        .populate('activePlan', 'title duration discountedPrice')
        .sort({ createdAt: -1 });
      
      if (subscriptions.length > 0) {
        const paidSubs = subscriptions.filter(sub => sub.paymentStatus === 'paid');
        const activeSubs = subscriptions.filter(sub => sub.status === 'active');
        const pendingSubs = subscriptions.filter(sub => sub.paymentStatus === 'pending');
        
        // Issue 1: User has paid subscriptions but status is free
        if (paidSubs.length > 0 && user.subscriptionStatus === 'free') {
          usersWithIssues.push({ user, subscriptions, issue: 'PAID_BUT_FREE' });
        }
        
        // Issue 2: User has active subscriptions but status is free
        else if (activeSubs.length > 0 && user.subscriptionStatus === 'free') {
          usersWithIssues.push({ user, subscriptions, issue: 'ACTIVE_BUT_FREE' });
        }
        
        // Issue 3: User has recent pending payments (likely legitimate)
        else if (pendingSubs.length > 0 && user.subscriptionStatus === 'free') {
          const recentPending = pendingSubs.filter(sub => {
            const timeSinceCreated = Date.now() - new Date(sub.createdAt).getTime();
            const hoursAgo = timeSinceCreated / (1000 * 60 * 60);
            return hoursAgo < 24; // Created within last 24 hours
          });
          
          if (recentPending.length > 0) {
            usersWithIssues.push({ user, subscriptions, issue: 'RECENT_PENDING' });
          }
        }
      }
    }
    
    console.log(`🚨 Found ${usersWithIssues.length} users with payment issues`);
    return usersWithIssues;
  }

  async fixUserSubscription(userData) {
    const { user, subscriptions, issue } = userData;
    
    try {
      console.log(`\n🔧 Fixing ${user.firstName} ${user.lastName} (@${user.username}) - Issue: ${issue}`);
      
      let subscriptionToActivate = null;
      
      if (issue === 'PAID_BUT_FREE' || issue === 'ACTIVE_BUT_FREE') {
        // Find the paid or active subscription
        subscriptionToActivate = subscriptions.find(sub => 
          sub.paymentStatus === 'paid' || sub.status === 'active'
        );
      } else if (issue === 'RECENT_PENDING') {
        // For recent pending payments, activate the most recent one
        const recentPending = subscriptions.filter(sub => {
          const timeSinceCreated = Date.now() - new Date(sub.createdAt).getTime();
          const hoursAgo = timeSinceCreated / (1000 * 60 * 60);
          return hoursAgo < 24 && sub.paymentStatus === 'pending';
        });
        
        if (recentPending.length > 0) {
          subscriptionToActivate = recentPending[0]; // Most recent
          
          // For pending payments, we need to activate them
          await this.activateSubscription(subscriptionToActivate);
        }
      }
      
      if (subscriptionToActivate) {
        // Update user status
        const planDuration = subscriptionToActivate.activePlan?.duration || 6;
        const endDate = new Date();
        endDate.setMonth(endDate.getMonth() + planDuration);
        
        await User.findByIdAndUpdate(user._id, {
          subscriptionStatus: 'active',
          paymentRequired: false,
          subscriptionEndDate: endDate
        });
        
        console.log(`✅ Fixed ${user.username} - ${subscriptionToActivate.activePlan?.title}`);
        console.log(`   Status: free → active`);
        console.log(`   End Date: ${endDate.toISOString().split('T')[0]}`);
        
        this.fixedUsers.push({
          username: user.username,
          name: `${user.firstName} ${user.lastName}`,
          plan: subscriptionToActivate.activePlan?.title,
          issue: issue
        });
        
        return true;
      } else {
        console.log(`❌ Could not find subscription to activate for ${user.username}`);
        this.failedUsers.push({
          username: user.username,
          name: `${user.firstName} ${user.lastName}`,
          issue: issue,
          reason: 'No valid subscription found'
        });
        return false;
      }
      
    } catch (error) {
      console.error(`❌ Error fixing ${user.username}:`, error.message);
      this.failedUsers.push({
        username: user.username,
        name: `${user.firstName} ${user.lastName}`,
        issue: issue,
        reason: error.message
      });
      return false;
    }
  }

  async activateSubscription(subscription) {
    try {
      // Calculate subscription dates
      const startDate = new Date();
      const endDate = new Date();
      const planDuration = subscription.activePlan?.duration || 6;
      endDate.setMonth(endDate.getMonth() + planDuration);
      
      const formattedStartDate = startDate.toISOString().split('T')[0];
      const formattedEndDate = endDate.toISOString().split('T')[0];
      
      // Update subscription
      subscription.paymentStatus = 'paid';
      subscription.status = 'active';
      subscription.startDate = formattedStartDate;
      subscription.endDate = formattedEndDate;
      
      // Update payment history
      if (subscription.paymentHistory.length > 0) {
        const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
        latestPayment.paymentStatus = 'paid';
        latestPayment.referenceId = `SMART_FIX_${Date.now()}`;
      }
      
      await subscription.save();
      console.log(`   📅 Subscription activated: ${formattedStartDate} to ${formattedEndDate}`);
      
    } catch (error) {
      console.error('❌ Error activating subscription:', error.message);
      throw error;
    }
  }

  async fixAllUsers() {
    try {
      await this.connectDB();
      
      console.log('🚀 Starting SMART payment fixer for ALL users...\n');
      
      const usersWithIssues = await this.findAllUsersWithPaymentIssues();
      
      if (usersWithIssues.length === 0) {
        console.log('🎉 No payment issues found! All users have correct subscription status.');
        return;
      }
      
      console.log(`\n🔧 Fixing ${usersWithIssues.length} users with payment issues...\n`);
      
      for (const userData of usersWithIssues) {
        await this.fixUserSubscription(userData);
        // Small delay to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      // Summary
      console.log('\n📊 SMART PAYMENT FIXER RESULTS:');
      console.log('=' .repeat(50));
      
      console.log(`\n✅ SUCCESSFULLY FIXED (${this.fixedUsers.length} users):`);
      this.fixedUsers.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.name} (@${user.username}) - ${user.plan} - Issue: ${user.issue}`);
      });
      
      if (this.failedUsers.length > 0) {
        console.log(`\n❌ FAILED TO FIX (${this.failedUsers.length} users):`);
        this.failedUsers.forEach((user, index) => {
          console.log(`   ${index + 1}. ${user.name} (@${user.username}) - Issue: ${user.issue} - Reason: ${user.reason}`);
        });
      }
      
      console.log(`\n🎯 SUMMARY:`);
      console.log(`   Total Issues Found: ${usersWithIssues.length}`);
      console.log(`   Successfully Fixed: ${this.fixedUsers.length}`);
      console.log(`   Failed to Fix: ${this.failedUsers.length}`);
      console.log(`   Success Rate: ${Math.round((this.fixedUsers.length / usersWithIssues.length) * 100)}%`);
      
      if (this.fixedUsers.length > 0) {
        console.log('\n🎉 All fixed users should now see ACTIVE subscription status!');
        console.log('📱 Users can refresh their subscription page to see the changes.');
      }
      
    } catch (error) {
      console.error('❌ Smart payment fixer failed:', error.message);
    } finally {
      mongoose.disconnect();
      console.log('\n🔌 Disconnected from MongoDB');
    }
  }
}

// Run the smart payment fixer
const fixer = new SmartPaymentFixer();
fixer.fixAllUsers();
