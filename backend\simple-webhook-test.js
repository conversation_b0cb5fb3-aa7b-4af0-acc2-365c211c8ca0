const axios = require('axios');
require('dotenv').config();

async function testWebhookSystem() {
  try {
    console.log('🧪 Testing Webhook System...\n');
    
    // Test 1: Check if server is running
    console.log('1️⃣ Testing server health...');
    try {
      const healthResponse = await axios.get('http://localhost:5000/api/health');
      console.log('✅ Server is running:', healthResponse.data);
    } catch (error) {
      console.log('❌ Server not responding:', error.message);
      return;
    }
    
    // Test 2: Check verification service status
    console.log('\n2️⃣ Testing verification service...');
    try {
      const verificationResponse = await axios.get('http://localhost:5000/api/payment/verification-status');
      console.log('✅ Verification service:', verificationResponse.data);
    } catch (error) {
      console.log('❌ Verification service error:', error.message);
    }
    
    // Test 3: Test webhook endpoint
    console.log('\n3️⃣ Testing webhook endpoint...');
    
    const testWebhookPayload = {
      order_id: 'TEST_ORDER_' + Date.now(),
      payment_status: 'COMPLETED',
      reference: 'TEST_REF_' + Date.now(),
      amount: '13000',
      currency: 'TZS',
      buyer_name: 'Test User',
      timestamp: new Date().toISOString()
    };
    
    console.log('📤 Sending test webhook:', JSON.stringify(testWebhookPayload, null, 2));
    
    try {
      const webhookResponse = await axios.post('http://localhost:5000/api/payment/webhook', testWebhookPayload, {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.ZENOPAY_API_KEY
        }
      });
      
      console.log('✅ Webhook test successful:', webhookResponse.data);
    } catch (error) {
      console.log('❌ Webhook test failed:', error.response?.data || error.message);
    }
    
    // Test 4: Test webhook without API key (should still work with new flexible auth)
    console.log('\n4️⃣ Testing webhook without API key...');
    
    try {
      const webhookResponse2 = await axios.post('http://localhost:5000/api/payment/webhook', testWebhookPayload, {
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'ZenoPay-Webhook/1.0'
        }
      });
      
      console.log('✅ Webhook without API key successful:', webhookResponse2.data);
    } catch (error) {
      console.log('❌ Webhook without API key failed:', error.response?.data || error.message);
    }
    
    // Test 5: Test manual verification endpoint
    console.log('\n5️⃣ Testing manual verification...');
    
    try {
      const manualVerifyResponse = await axios.post('http://localhost:5000/api/payment/verify-payment', {
        orderId: 'TEST_ORDER_123'
      });
      
      console.log('✅ Manual verification response:', manualVerifyResponse.data);
    } catch (error) {
      console.log('⚠️ Manual verification (expected to fail for test order):', error.response?.data || error.message);
    }
    
    console.log('\n📋 Summary:');
    console.log('✅ Enhanced webhook system is now active with:');
    console.log('   🔧 Flexible authentication (API key, User-Agent, or demo mode)');
    console.log('   🔍 Automatic payment verification service (checks every 30 seconds)');
    console.log('   🛠️ Manual verification endpoint for troubleshooting');
    console.log('   📊 Better logging and error handling');
    
    console.log('\n💡 How it works now:');
    console.log('1️⃣ User makes payment → ZenoPay processes');
    console.log('2️⃣ ZenoPay sends webhook → Enhanced webhook handler processes');
    console.log('3️⃣ If webhook fails → Verification service checks payment status every 30s');
    console.log('4️⃣ When payment confirmed → Subscription automatically activated');
    console.log('5️⃣ User gets immediate access → No more "free" status for paid users');
    
    console.log('\n🎉 Webhook system test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testWebhookSystem();
