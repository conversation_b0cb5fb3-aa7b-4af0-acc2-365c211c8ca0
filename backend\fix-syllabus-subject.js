const mongoose = require('mongoose');
require('dotenv').config();

async function fixSyllabusSubject() {
  console.log('🔧 Fixing syllabus subject names to match predefined subjects...\n');

  try {
    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    const Syllabus = require('./models/syllabusModel');

    // Check current syllabuses
    console.log('\n1️⃣ Current syllabuses:');
    const allSyllabuses = await Syllabus.find({});
    allSyllabuses.forEach((s, i) => {
      console.log(`${i + 1}. "${s.title}"`);
      console.log(`   Level: ${s.level}`);
      console.log(`   Subject: "${s.subject}"`);
      console.log(`   Classes: ${s.classes.join(', ')}`);
      console.log('');
    });

    // Subject mapping to fix case issues
    const subjectMapping = {
      'science and technology': 'Science and Technology',
      'mathematics': 'Mathematics',
      'english': 'English',
      'kiswahili': 'Kiswahili',
      'geography': 'Geography',
      'physics': 'Physics',
      'chemistry': 'Chemistry',
      'biology': 'Biology',
      'history': 'History',
      'civics': 'Civics',
      'computer': 'Computer',
      'commerce': 'Commerce',
      'economics': 'Economics',
      'agriculture': 'Agriculture',
      'accountancy': 'Accountancy'
    };

    console.log('2️⃣ Fixing subject names...');
    let updatedCount = 0;

    for (const syllabus of allSyllabuses) {
      const currentSubject = syllabus.subject.toLowerCase();
      const correctSubject = subjectMapping[currentSubject];
      
      if (correctSubject && correctSubject !== syllabus.subject) {
        console.log(`📝 Updating "${syllabus.title}"`);
        console.log(`   From: "${syllabus.subject}"`);
        console.log(`   To: "${correctSubject}"`);
        
        await Syllabus.findByIdAndUpdate(syllabus._id, {
          subject: correctSubject
        });
        
        updatedCount++;
        console.log('   ✅ Updated successfully\n');
      } else if (correctSubject) {
        console.log(`✅ "${syllabus.title}" already has correct subject: "${syllabus.subject}"`);
      } else {
        console.log(`⚠️ "${syllabus.title}" has unknown subject: "${syllabus.subject}"`);
        console.log('   Please add this subject to the predefined subjects list');
      }
    }

    console.log(`\n3️⃣ Summary:`);
    console.log(`✅ Total syllabuses: ${allSyllabuses.length}`);
    console.log(`✅ Updated: ${updatedCount}`);
    console.log(`✅ Already correct: ${allSyllabuses.length - updatedCount}`);

    // Verify the changes
    console.log('\n4️⃣ Verification - Updated syllabuses:');
    const updatedSyllabuses = await Syllabus.find({});
    updatedSyllabuses.forEach((s, i) => {
      console.log(`${i + 1}. "${s.title}"`);
      console.log(`   Level: ${s.level}`);
      console.log(`   Subject: "${s.subject}"`);
      console.log(`   Classes: ${s.classes.join(', ')}`);
      console.log('');
    });

    // Test subject availability
    console.log('5️⃣ Testing subject availability:');
    const primarySubjects = await Syllabus.getAvailableSubjects('primary');
    console.log(`Primary subjects: [${primarySubjects.join(', ')}]`);

    const secondarySubjects = await Syllabus.getAvailableSubjects('secondary');
    console.log(`Secondary subjects: [${secondarySubjects.join(', ')}]`);

    const advanceSubjects = await Syllabus.getAvailableSubjects('advance');
    console.log(`Advance subjects: [${advanceSubjects.join(', ')}]`);

    console.log('\n🎯 Next steps:');
    console.log('1. Test the syllabus upload form with subject dropdown');
    console.log('2. Test AI question generation with corrected subject names');
    console.log('3. Verify subject matching works correctly');

  } catch (error) {
    console.error('❌ Error fixing syllabus subjects:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from database');
  }
}

// Run fix if this file is executed directly
if (require.main === module) {
  fixSyllabusSubject().catch(console.error);
}

module.exports = { fixSyllabusSubject };
