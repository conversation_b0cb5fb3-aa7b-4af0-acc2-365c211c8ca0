const mongoose = require("mongoose");
require("dotenv").config();

async function checkAIExamsVisibility() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log("✅ Connected to MongoDB");

    const Exam = require("./models/examModel");
    const Question = require("./models/questionModel");
    const User = require("./models/userModel");

    // Find a regular user (non-admin) to simulate the quiz listing
    const regularUser = await User.findOne({ isAdmin: { $ne: true } });
    if (!regularUser) {
      console.log("❌ No regular user found");
      return;
    }

    console.log(`👤 Testing with user: ${regularUser.name} (Level: ${regularUser.level}, Class: ${regularUser.class})`);

    // Simulate the quiz listing logic from examsRoute.js
    const userLevel = regularUser.level || 'Primary';
    
    let exams;
    if (userLevel === "Secondary") {
      exams = await Exam.find({ level: "Secondary" }).populate('questions');
    } else if (userLevel === "Advance") {
      exams = await Exam.find({ level: "Advance" }).populate('questions');
    } else {
      exams = await Exam.find({ level: "Primary" }).populate('questions');
    }

    console.log(`📊 Found ${exams.length} exams for ${userLevel} level before filtering`);

    // Filter out exams with no questions (as done in the actual route)
    const beforeFilterCount = exams.length;
    exams = exams.filter(exam => exam.questions && exam.questions.length > 0);
    const afterFilterCount = exams.length;

    console.log(`📊 After filtering: ${afterFilterCount} exams (${beforeFilterCount - afterFilterCount} filtered out for having no questions)`);

    // Check for AI-generated exams specifically
    const aiGeneratedExams = exams.filter(exam => {
      // Check if exam name matches AI-generated pattern
      return exam.name.match(/^[PSA]\d+[A-Z]{1,2}-[A-Z0-9]{2}$/);
    });

    console.log(`🤖 AI-generated exams with questions: ${aiGeneratedExams.length}`);

    if (aiGeneratedExams.length > 0) {
      console.log("\n📋 AI-generated exams that should appear in quiz listing:");
      for (const exam of aiGeneratedExams) {
        const aiQuestions = await Question.find({ 
          _id: { $in: exam.questions },
          isAIGenerated: true 
        });
        
        console.log(`- ${exam.name} (${exam.level} Class ${exam.class})`);
        console.log(`  Subject: ${exam.subject}`);
        console.log(`  Total Questions: ${exam.questions.length}`);
        console.log(`  AI Questions: ${aiQuestions.length}`);
        console.log(`  Created: ${exam.createdAt}`);
        console.log("");
      }
    } else {
      console.log("❌ No AI-generated exams with questions found!");
    }

    // Check for recent exams that might be missing questions
    const recentExams = await Exam.find({ level: userLevel })
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('questions');

    console.log("\n📋 Recent exams (last 10) and their question counts:");
    recentExams.forEach(exam => {
      const isAI = exam.name.match(/^[PSA]\d+[A-Z]{1,2}-[A-Z0-9]{2}$/);
      console.log(`- ${exam.name}: ${exam.questions.length} questions ${isAI ? '🤖' : '👤'}`);
    });

    await mongoose.disconnect();
    console.log("\n✅ Check completed");

  } catch (error) {
    console.error("❌ Error:", error);
    await mongoose.disconnect();
  }
}

checkAIExamsVisibility();
