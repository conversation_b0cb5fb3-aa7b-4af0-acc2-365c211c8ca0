// routes/chatgpt.js
const express = require("express");
const axios = require("axios");
const router = express.Router();
const AWS = require("aws-sdk");
const multer = require("multer");

const storage = multer.memoryStorage();
const upload = multer({ storage });

const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION,
});

router.post("/image/upload", upload.single("image"), async (req, res) => {
  try {
    console.log('Image upload request received');
    const file = req.file;
    if (!file) {
      console.log('No file in request');
      return res.status(400).json({ success: false, message: "No file uploaded." });
    }

    console.log('File details:', { name: file.originalname, size: file.size, type: file.mimetype });

    const params = {
      Bucket: process.env.AWS_S3_BUCKET_NAME, // Your S3 bucket name
      Key: `${Date.now()}-${file.originalname}`, // Unique file name
      Body: file.buffer,
      ContentType: file.mimetype,
      ACL: "public-read", // File should be publicly readable
    };

    const result = await s3.upload(params).promise();
    console.log("File uploaded successfully:", result);

    res.status(200).json({
      success: true,
      data: { url: result.Location },
      url: result.Location // Fallback for compatibility
    });
  } catch (error) {
    console.error("Error uploading file:", error);
    res.status(500).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
});

router.post("/chat", async (req, res) => {
  console.log('Data: ', req.body);
  try {
    const start = performance.now();
    const { messages, language, systemPrompt } = req.body;

    // Prepare messages with language-specific system prompt for Kiswahili users
    let finalMessages = [...messages];

    if (language === 'kiswahili' && systemPrompt) {
      // Add or update system message for Kiswahili users
      const systemMessage = {
        role: "system",
        content: systemPrompt
      };

      // Check if there's already a system message
      const hasSystemMessage = finalMessages.some(msg => msg.role === 'system');
      if (hasSystemMessage) {
        // Replace existing system message
        finalMessages = finalMessages.map(msg =>
          msg.role === 'system' ? systemMessage : msg
        );
      } else {
        // Add system message at the beginning
        finalMessages = [systemMessage, ...finalMessages];
      }
    }

    const response = await axios.post(
      "https://api.openai.com/v1/chat/completions",
      {
        model: "gpt-4o",
        messages: finalMessages,
        // max_tokens: 100,
        temperature: 0.5,
      },
      {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
        },
      }
    );

    const end = performance.now();
    console.log('Time taken by openai api (s): ', (end - start) / 1000);
    res.status(200).json({ success: true, data: response.data.choices[0].message.content });
  } catch (error) {
    console.error("Error fetching GPT-4 response:", error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// ✔️ NEW – AI ­answer-checker
router.post("/check-answer", async (req, res) => {
  try {
    const questions = req.body;

    if (!Array.isArray(questions) || questions.length === 0) {
      return res.status(400).json({
        success: false,
        error: "Request body must be a non-empty array of questions",
      });
    }

    const results = [];

    for (const item of questions) {
      const { question, expectedAnswer, userAnswer } = item;

      if (!question || !expectedAnswer || !userAnswer) {
        results.push({
          question,
          success: false,
          error: "question, expectedAnswer and userAnswer are required",
        });
        continue;
      }

      const messages = [
        {
          role: "system",
          content:
            'You are an examiner. Compare the student\'s answer with the expected answer. Ignore the format just validate if answer is correct or not. ' +
            'Reply ONLY with valid JSON: {"isCorrect": true/false}.',
        },
        {
          role: "user",
          content: `QUESTION: ${question}\nEXPECTED ANSWER: ${expectedAnswer}\nSTUDENT ANSWER: ${userAnswer}`,
        },
      ];

      try {
        const { data } = await axios.post(
          "https://api.openai.com/v1/chat/completions",
          {
            model: "gpt-4o",
            messages,
            temperature: 0,
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
            },
          }
        );

        const aiJson = JSON.parse(data.choices[0].message.content.trim());

        results.push({
          question,
          expectedAnswer,
          userAnswer,
          result: aiJson,
          success: true,
        });
      } catch (innerError) {
        console.error("OpenAI error:", innerError.message);
        results.push({
          question,
          expectedAnswer,
          userAnswer,
          success: false,
          error: "AI response failed or returned invalid JSON",
        });
      }
    }

    res.status(200).json({ success: true, data: results });
  } catch (error) {
    console.error("Answer-check error:", error.message);
    res.status(500).json({ success: false, error: error.message });
  }
});

router.post("/explain-answer", async (req, res) => {
  try {
    const { question, expectedAnswer, userAnswer, imageUrl, language } = req.body;

    // console.log("Image Url: ", imageUrl);

    // Enhanced validation with better error messages
    if (!question || question.trim() === '' || question === 'Question text not available') {
      return res.status(400).json({
        success: false,
        error: "Valid question text is required for explanation",
      });
    }

    // Allow empty or placeholder answers but log them for debugging
    const hasValidExpectedAnswer = expectedAnswer && expectedAnswer.trim() !== '' && expectedAnswer !== 'Answer not available';
    const hasValidUserAnswer = userAnswer && userAnswer.trim() !== '' && userAnswer !== 'No answer provided';

    console.log('🔍 Explanation request validation:', {
      question: question.substring(0, 100) + '...',
      hasValidExpectedAnswer,
      hasValidUserAnswer,
      expectedAnswer: expectedAnswer || 'EMPTY',
      userAnswer: userAnswer || 'EMPTY'
    });

    // Detect if this is a math question
    const isMathQuestion = /[\d\+\-\×\÷\*\/\=\(\)\^\%]|math|calculate|solve|equation|formula|area|volume|perimeter|algebra|geometry|trigonometry|statistics|probability/i.test(question + ' ' + (expectedAnswer || ''));

    // Create math-focused explanation prompt with clear calculations
    const explanationPrompt = `
QUESTION: ${question}
CORRECT ANSWER: ${expectedAnswer}
STUDENT'S ANSWER: ${userAnswer}

FOR MATHEMATICS - SHOW CLEAR CALCULATIONS:
**Solution Steps:**
Step 1: [First calculation step]
Step 2: [Second calculation step]
Step 3: [Final result]

FOR OTHER SUBJECTS - USE STRUCTURED FORMAT:
**Definition:** Brief explanation of "${expectedAnswer}"
**Why Correct:** Reason this is right
**Key Points:** Important facts (bullet points)

FORMATTING RULES:
- Use LaTeX \\(...\\) for ALL math expressions
- Show calculations step-by-step with minimal words
- For math: Focus on STEPS, not explanations
- For other subjects: Keep brief with highlights
- **Bold** headings, bullet points for facts`;

    // Simple, direct prompt focusing on educational explanation
    let userContent = explanationPrompt;
      ``,
      imageUrl ?
        `SCIENCE EDUCATIONAL DIAGRAM: A science educational diagram/image is provided with this question for Class 6 Tanzania curriculum. You MUST analyze this science image thoroughly as it is ESSENTIAL for providing educational explanation. This science diagram may show:

        - BIOLOGY: Plant parts (roots, stems, leaves, flowers), animal body parts, human anatomy, cell structures, organs, life cycles
        - CHEMISTRY: Molecular structures, chemical reactions, laboratory equipment, periodic table elements
        - PHYSICS: Force diagrams, simple machines, electrical circuits, light/sound waves, magnets
        - EARTH SCIENCE: Rock formations, weather patterns, solar system, water cycle
        - GENERAL SCIENCE: Scientific instruments, measurement tools, experimental setups

        MANDATORY INSTRUCTION: You are a science teacher and you MUST examine this science diagram carefully and provide detailed scientific analysis and explanation. This is for educational learning purposes in Tanzania schools. NEVER refuse to analyze science educational images.

        ANALYZE THE SCIENCE DIAGRAM AND EXPLAIN WHAT YOU SEE IN DETAIL.` :
        'No science diagram provided with this question.',
      ``,
      isMathQuestion ?
        `This is a MATHEMATICS question. Please provide:

        **1. Evaluation:**
        - Explain why the student's answer is incorrect (if it is)
        - Compare it with the correct answer provided by the admin

        **2. Complete Solution:**
        Step 1: [Clear mathematical step with equations in LaTeX format]
        Step 2: [Next step with calculations in LaTeX format]
        Step 3: [Continue with formulas and work in LaTeX format]

        **3. Mathematical Work:**
        Show all calculations using LaTeX notation:
        - Use \\( \\) for inline math: \\(2 + 3 = 5\\)
        - Use \\[ \\] for block equations: \\[Area = length \\times width\\]
        - Use proper LaTeX symbols: \\times, \\div, \\leq, \\geq, \\neq, \\frac{a}{b}, x^2, \\sqrt{x}

        **4. Final Answer:** \\[Final = numerical \\text{ answer with units}\\]

        IMPORTANT:
        - Wrap ALL mathematical expressions in LaTeX notation
        - Use \\( \\) for inline math and \\[ \\] for display math
        - Show step-by-step calculations with proper mathematical formatting
        - Use LaTeX commands for fractions, exponents, roots, etc.
        - Reference the image/diagram if provided

        Example format:
        **Step 1:** Find the area
        \\[Area = length \\times width = 5 \\times 3 = 15\\]

        **Step 2:** Calculate perimeter
        \\[Perimeter = 2(l + w) = 2(5 + 3) = 16\\]` :
        `You are a SCIENCE TEACHER and you MUST provide comprehensive educational explanation with science diagram analysis:

        **1. MANDATORY SCIENCE DIAGRAM ANALYSIS (if science image provided):**
        - You MUST carefully examine and describe what you see in the science educational diagram
        - IDENTIFY and LABEL all relevant parts, structures, organisms, or scientific features
        - EXPLAIN the scientific context and educational purpose of the diagram
        - DESCRIBE the scientific processes, structures, or concepts shown
        - This is REQUIRED for Class 6 Tanzania science education

        **2. STUDENT ANSWER EVALUATION:**
        - Compare the student's answer with the admin's correct answer
        - Explain why the student's answer is incorrect (if it is)
        - Reference specific parts of the science diagram that support the correct answer
        - Use scientific terminology appropriate for Class 6 level

        **3. CORRECT SCIENTIFIC ANSWER EXPLANATION:**
        - Explain the correct answer provided by the admin using scientific knowledge
        - Show the proper scientific method/reasoning to arrive at this answer
        - Use the science diagram to illustrate and support your scientific explanation
        - Point out specific visual elements in the diagram that lead to the correct answer

        **4. SCIENCE EDUCATIONAL LEARNING POINTS:**
        - Key scientific concepts the student should understand from this question and diagram
        - How to properly analyze similar science diagrams or images
        - Common mistakes to avoid when interpreting science educational images
        - Tips for answering similar science visual/diagram-based questions

        **ULTRA-CRITICAL INSTRUCTIONS FOR SCIENCE DIAGRAMS:**
        - You are a SCIENCE TEACHER and you MUST analyze any science educational images provided
        - This is ESSENTIAL for science learning in Tanzania schools
        - ALWAYS reference specific parts of science diagrams in your explanation
        - Provide detailed scientific visual analysis for educational purposes
        - Help students understand how to interpret science educational images correctly
        - This is for Class 6 science education - science diagram analysis is REQUIRED and EXPECTED
        - NEVER refuse to analyze science educational diagrams - this is your primary job as a science teacher`;

    // Convert userContent array to string
    const userContentString = Array.isArray(userContent) ? userContent.join('\n') : userContent;

    // Handle image content for vision models
    let messageContent;
    if (imageUrl) {
      messageContent = [
        {
          type: "text",
          text: userContentString
        },
        {
          type: "image_url",
          image_url: {
            url: imageUrl,
          },
        }
      ];
    } else {
      messageContent = userContentString;
    }

    // Check if admin answer is empty, placeholder, or problematic
    const isEmptyAnswer = !expectedAnswer || expectedAnswer.toString().trim() === "";
    const isPlaceholderAnswer = expectedAnswer && /answer not available|not available|no answer|placeholder/i.test(expectedAnswer);
    const needsAIEvaluation = isEmptyAnswer || isPlaceholderAnswer;

    console.log(`🔍 AI Explanation Analysis:`);
    console.log(`   Expected Answer: "${expectedAnswer}"`);
    console.log(`   Is Empty: ${isEmptyAnswer}`);
    console.log(`   Is Placeholder: ${isPlaceholderAnswer}`);
    console.log(`   Needs AI Evaluation: ${needsAIEvaluation}`);

    // Math-focused system message with clear calculations
    const systemContent = language === 'kiswahili'
      ? `Wewe ni mwalimu wa Tanzania. Kwa hesabu, onyesha hatua za hesabu tu.

KWA HESABU:
**Hatua za Suluhisho:**
Hatua 1: [Hesabu ya kwanza]
Hatua 2: [Hesabu ya pili]
Hatua 3: [Jibu la mwisho]

KWA MASOMO MENGINE:
**Ufafanuzi:** Maana ya jibu sahihi
**Kwa nini ni sahihi:** Sababu
**Mambo muhimu:** Nukta muhimu

KANUNI:
- Tumia LaTeX \\(...\\) kwa hesabu ZOTE
- Kwa hesabu: Onyesha HATUA tu, si maelezo mengi
- Epuka maneno mengi katika hesabu`
      : `You are a teacher from Tanzania. For mathematics, show clear calculation steps only.

FOR MATHEMATICS:
**Solution Steps:**
Step 1: [First calculation step]
Step 2: [Second calculation step]
Step 3: [Final result]

FOR OTHER SUBJECTS:
**Definition:** Brief explanation of correct answer
**Why Correct:** Reason why it's right
**Key Points:** Important facts (bullet points)

RULES:
- Use LaTeX \\(...\\) for ALL mathematical expressions
- For math: Show STEPS only, avoid lengthy explanations
- Focus on calculations, not words
- Keep non-math subjects brief with highlights`;

    const messages = [
      {
        role: "system",
        content: systemContent,
      },
      {
        role: "user",
        content: messageContent
      },
    ];


    // Validate OpenAI API key
    if (!process.env.OPENAI_API_KEY) {
      throw new Error("OpenAI API key not configured");
    }

    console.log("🚀 Sending explanation request:");
    console.log("QUESTION:", question);
    console.log("CORRECT ANSWER:", expectedAnswer);
    console.log("USER ANSWER:", userAnswer);
    console.log("SYSTEM PROMPT:", systemContent);
    console.log("USER PROMPT:", userContentString);

    // GPT-4o request with retry mechanism
    let data;
    let lastError;

    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        console.log(`Attempt ${attempt}/3 for OpenAI API call`);

        const response = await axios.post(
          "https://api.openai.com/v1/chat/completions",
          {
            model: attempt === 1 ? "gpt-4o" : "gpt-3.5-turbo", // Fallback to cheaper model on retry
            messages,
            temperature: 0.7,
            max_tokens: 1000,
          },
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
            },
            timeout: 30000
          }
        );

        data = response.data;
        break; // Success, exit retry loop

      } catch (apiError) {
        lastError = apiError;
        console.error(`❌ Attempt ${attempt} failed:`, {
          message: apiError.message,
          status: apiError.response?.status,
          statusText: apiError.response?.statusText,
          data: apiError.response?.data,
          code: apiError.code
        });

        // If it's a rate limit error, wait before retrying
        if (apiError.response?.status === 429 && attempt < 3) {
          console.log(`⏳ Rate limited, waiting ${attempt * 2} seconds before retry...`);
          await new Promise(resolve => setTimeout(resolve, attempt * 2000));
          continue;
        }

        // If it's the last attempt or a non-retryable error, throw
        if (attempt === 3) {
          console.error(`💥 All attempts failed. Final error:`, {
            message: lastError.message,
            status: lastError.response?.status,
            data: lastError.response?.data
          });
          throw lastError;
        }
      }
    }

    // Validate response
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error("Invalid response from OpenAI API");
    }

    const explanation = data.choices[0].message.content.trim();

    if (!explanation) {
      throw new Error("Empty explanation received from OpenAI API");
    }

    console.log("Explanation generated successfully:", {
      explanationLength: explanation.length,
      questionType: isMathQuestion ? 'math' : 'general'
    });

    res.status(200).json({
      success: true,
      explanation,
    });
  } catch (error) {
    console.error("Explain-answer error:", error.message);
    console.error("Error details:", {
      question: req.body.question?.substring(0, 100),
      expectedAnswer: req.body.expectedAnswer,
      userAnswer: req.body.userAnswer,
      hasImage: !!req.body.imageUrl,
      language: req.body.language,
      errorType: error.response?.status || 'unknown',
      errorData: error.response?.data || 'no data'
    });

    // Try to provide a better fallback explanation
    let fallbackExplanation = "";
    const { question, expectedAnswer, userAnswer, language } = req.body;
    const isKiswahili = language === 'kiswahili';

    if (expectedAnswer && userAnswer && expectedAnswer.toLowerCase() !== userAnswer.toLowerCase()) {
      // Create a more detailed fallback explanation
      if (isKiswahili) {
        fallbackExplanation = `📚 Maelezo ya Swali:\n\n` +
          `❌ Jibu lako: "${userAnswer}"\n` +
          `✅ Jibu sahihi: "${expectedAnswer}"\n\n` +
          `💡 Kumbuka: Angalia kwa makini maelezo ya swali na ujaribu kutafakari jibu sahihi. ` +
          `Jibu sahihi ni "${expectedAnswer}" kwa sababu ni jibu la kweli la swali hili.`;
      } else {
        fallbackExplanation = `📚 Question Explanation:\n\n` +
          `❌ Your answer: "${userAnswer}"\n` +
          `✅ Correct answer: "${expectedAnswer}"\n\n` +
          `💡 Remember: The correct answer is "${expectedAnswer}" because it accurately responds to what the question is asking. ` +
          `Review the question carefully and consider why this is the most appropriate answer.`;
      }
    } else if (expectedAnswer && userAnswer && expectedAnswer.toLowerCase() === userAnswer.toLowerCase()) {
      // User got it right, but explanation was requested
      if (isKiswahili) {
        fallbackExplanation = `🎉 Hongera! Umejibu kwa usahihi.\n\n✅ Jibu lako: "${userAnswer}"\n\nUmefanya vizuri!`;
      } else {
        fallbackExplanation = `🎉 Congratulations! You answered correctly.\n\n✅ Your answer: "${userAnswer}"\n\nWell done!`;
      }
    } else {
      // Generic fallback
      if (isKiswahili) {
        fallbackExplanation = "⚠️ Samahani, hatukuweza kutengeneza maelezo ya kina kwa wakati huu. Tafadhali jaribu tena baadaye au uliza mwalimu wako.";
      } else {
        fallbackExplanation = "⚠️ Sorry, we could not generate a detailed explanation at this time. Please try again later or ask your teacher for help.";
      }
    }

    console.log("🔄 Providing fallback explanation due to API failure");

    // Return fallback explanation instead of error
    res.status(200).json({
      success: true,
      explanation: fallbackExplanation,
      isFallback: true,
      note: "This is a basic explanation due to AI service unavailability"
    });
  }
});



module.exports = router;
