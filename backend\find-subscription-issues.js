const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const Plan = require('./models/planModel');

async function findSubscriptionIssues() {
  try {
    await mongoose.connect('mongodb://hvmgeeks:<EMAIL>:27017,ac-4ojvv6y-shard-00-01.cdg8fdn.mongodb.net:27017,ac-4ojvv6y-shard-00-02.cdg8fdn.mongodb.net:27017/stjoseph?ssl=true&replicaSet=atlas-fsgg6f-shard-0&authSource=admin&retryWrites=true&w=majority&appName=Cluster0');
    
    console.log('🔍 Finding all subscription issues...\n');
    
    // Find all users with subscription issues
    const usersWithIssues = [];
    
    // Get all subscriptions
    const allSubscriptions = await Subscription.find({})
      .populate('user', 'firstName lastName username email subscriptionStatus paymentRequired')
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    console.log(`📊 Total subscriptions in database: ${allSubscriptions.length}\n`);
    
    // Group by user
    const userSubscriptions = {};
    allSubscriptions.forEach(sub => {
      if (sub.user) {
        const userId = sub.user._id.toString();
        if (!userSubscriptions[userId]) {
          userSubscriptions[userId] = {
            user: sub.user,
            subscriptions: []
          };
        }
        userSubscriptions[userId].subscriptions.push(sub);
      }
    });
    
    console.log(`👥 Users with subscriptions: ${Object.keys(userSubscriptions).length}\n`);
    
    // Analyze each user
    Object.values(userSubscriptions).forEach(userData => {
      const user = userData.user;
      const subscriptions = userData.subscriptions;
      
      const paidSubscriptions = subscriptions.filter(sub => sub.paymentStatus === 'paid');
      const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
      const pendingSubscriptions = subscriptions.filter(sub => sub.paymentStatus === 'pending');
      
      // Check for issues
      const issues = [];
      
      // Issue 1: User has paid subscriptions but status is free
      if (paidSubscriptions.length > 0 && user.subscriptionStatus === 'free') {
        issues.push('PAID_BUT_FREE_STATUS');
      }
      
      // Issue 2: User has active subscriptions but status is free
      if (activeSubscriptions.length > 0 && user.subscriptionStatus === 'free') {
        issues.push('ACTIVE_BUT_FREE_STATUS');
      }
      
      // Issue 3: User has paid subscriptions but none are active
      if (paidSubscriptions.length > 0 && activeSubscriptions.length === 0) {
        issues.push('PAID_BUT_NOT_ACTIVE');
      }
      
      // Issue 4: User has many pending subscriptions (possible payment failures)
      if (pendingSubscriptions.length > 1) {
        issues.push('MULTIPLE_PENDING');
      }
      
      // Issue 5: User status is active but no active subscriptions
      if (user.subscriptionStatus === 'active' && activeSubscriptions.length === 0) {
        issues.push('STATUS_ACTIVE_NO_SUBSCRIPTION');
      }
      
      if (issues.length > 0) {
        usersWithIssues.push({
          user,
          subscriptions,
          issues,
          paidCount: paidSubscriptions.length,
          activeCount: activeSubscriptions.length,
          pendingCount: pendingSubscriptions.length
        });
      }
    });
    
    console.log(`🚨 Users with issues found: ${usersWithIssues.length}\n`);
    
    // Display issues
    usersWithIssues.forEach((userData, index) => {
      const user = userData.user;
      console.log(`${index + 1}. 👤 ${user.firstName} ${user.lastName} (@${user.username})`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Current Status: ${user.subscriptionStatus}`);
      console.log(`   Payment Required: ${user.paymentRequired}`);
      console.log(`   Issues: ${userData.issues.join(', ')}`);
      console.log(`   Subscriptions: ${userData.subscriptions.length} total (${userData.paidCount} paid, ${userData.activeCount} active, ${userData.pendingCount} pending)`);
      
      // Show latest subscription details
      if (userData.subscriptions.length > 0) {
        const latest = userData.subscriptions[0];
        console.log(`   Latest: ${latest.activePlan?.title} - ${latest.paymentStatus}/${latest.status} (${latest.createdAt.toISOString().split('T')[0]})`);
      }
      console.log('');
    });
    
    // Summary by issue type
    console.log('📋 Issue Summary:');
    const issueCounts = {};
    usersWithIssues.forEach(userData => {
      userData.issues.forEach(issue => {
        issueCounts[issue] = (issueCounts[issue] || 0) + 1;
      });
    });
    
    Object.entries(issueCounts).forEach(([issue, count]) => {
      console.log(`   ${issue}: ${count} users`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    mongoose.disconnect();
  }
}

findSubscriptionIssues();
