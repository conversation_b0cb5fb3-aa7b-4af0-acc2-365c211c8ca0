const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const axios = require('axios');
require('dotenv').config();

// Import models
const User = require('./models/userModel');

async function testAPIResponse() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to MongoDB');

        // Find a test user
        const testUser = await User.findOne({ isAdmin: { $ne: true } });
        if (!testUser) {
            console.log('❌ No test user found');
            return;
        }

        console.log('👤 Test user:', testUser.name, testUser.email);

        // Generate a valid token
        const token = jwt.sign({ userId: testUser._id }, process.env.JWT_SECRET, { expiresIn: '1d' });

        // Test the XP leaderboard API and log the full response
        console.log('\n🎯 Testing XP leaderboard API response structure...');
        
        try {
            const response = await axios.get('http://localhost:5000/api/quiz/xp-leaderboard?limit=5', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ XP leaderboard API success');
            console.log('📊 Response status:', response.status);
            console.log('📊 Response structure:');
            console.log('  - success:', response.data.success);
            console.log('  - data type:', typeof response.data.data);
            console.log('  - data length:', response.data.data?.length || 0);
            console.log('  - metadata:', !!response.data.metadata);

            if (response.data.data && response.data.data.length > 0) {
                console.log('\n📋 Sample user data structure:');
                const sampleUser = response.data.data[0];
                console.log('Sample user keys:', Object.keys(sampleUser));
                console.log('Sample user data:', JSON.stringify(sampleUser, null, 2));
            }

            console.log('\n📋 Full response data:');
            console.log(JSON.stringify(response.data, null, 2));

        } catch (apiError) {
            console.log('❌ XP leaderboard API failed:', apiError.response?.status, apiError.response?.data?.message || apiError.message);
        }

    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.disconnect();
        console.log('✅ Disconnected from MongoDB');
    }
}

testAPIResponse();
