const mongoose = require('mongoose');
require('dotenv').config();

async function quickTest() {
  try {
    console.log('🔌 Testing MongoDB connection...');
    console.log('MongoDB URL:', process.env.MONGO_URL ? 'Found' : 'Missing');
    
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ MongoDB connected successfully');
    
    // Test syllabus collection
    const Syllabus = require('./models/syllabusModel');
    
    console.log('📚 Testing syllabus queries...');
    
    // Query 1: All syllabuses
    const allSyllabuses = await Syllabus.find({});
    console.log(`Total syllabuses: ${allSyllabuses.length}`);
    
    // Query 2: Active syllabuses
    const activeSyllabuses = await Syllabus.find({ isActive: true });
    console.log(`Active syllabuses: ${activeSyllabuses.length}`);
    
    // Query 3: Completed syllabuses
    const completedSyllabuses = await Syllabus.find({ processingStatus: 'completed' });
    console.log(`Completed syllabuses: ${completedSyllabuses.length}`);
    
    // Query 4: Active AND completed
    const activeCompleted = await Syllabus.find({ 
      isActive: true, 
      processingStatus: 'completed' 
    });
    console.log(`Active AND completed: ${activeCompleted.length}`);
    
    if (allSyllabuses.length > 0) {
      console.log('\n📋 Syllabus details:');
      allSyllabuses.forEach((s, i) => {
        console.log(`${i + 1}. "${s.title}"`);
        console.log(`   Level: ${s.level}`);
        console.log(`   Classes: ${s.classes ? s.classes.join(', ') : 'N/A'}`);
        console.log(`   Subject: ${s.subject}`);
        console.log(`   Status: ${s.processingStatus}`);
        console.log(`   Active: ${s.isActive}`);
        console.log(`   Created: ${s.createdAt}`);
        console.log('');
      });
    }
    
    // Test subjects query
    console.log('📖 Testing subjects query...');
    const primarySubjects = await Syllabus.getAvailableSubjects('primary');
    console.log(`Primary subjects: [${primarySubjects.join(', ')}]`);
    
    console.log('\n🎯 Summary:');
    console.log(`✅ Database connection: Working`);
    console.log(`✅ Syllabus model: Working`);
    console.log(`✅ Data exists: ${allSyllabuses.length > 0 ? 'Yes' : 'No'}`);
    console.log(`✅ Active data: ${activeSyllabuses.length > 0 ? 'Yes' : 'No'}`);
    
    if (activeSyllabuses.length === 0 && allSyllabuses.length > 0) {
      console.log('\n⚠️  ISSUE FOUND: Syllabuses exist but none are active!');
      console.log('🔧 Fix: Set isActive = true for existing syllabuses');
      
      // Fix the issue
      const result = await Syllabus.updateMany(
        { processingStatus: 'completed' },
        { isActive: true }
      );
      console.log(`✅ Fixed: Updated ${result.modifiedCount} syllabuses to active`);
      
      // Re-test
      const newActiveSyllabuses = await Syllabus.find({ isActive: true });
      console.log(`✅ Now active syllabuses: ${newActiveSyllabuses.length}`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from database');
  }
}

quickTest();
