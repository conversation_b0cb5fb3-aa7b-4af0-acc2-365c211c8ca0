const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');

async function fixExpiredSubscriptions() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    console.log('🔍 Checking for expired subscriptions...\n');
    
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];
    
    console.log(`📅 Today's Date: ${todayString}\n`);
    
    // Find all users with active subscription status
    const activeUsers = await User.find({ subscriptionStatus: 'active' });
    
    console.log(`👥 Found ${activeUsers.length} users with active subscription status\n`);
    
    const expiredUsers = [];
    const validUsers = [];
    const noEndDateUsers = [];
    const updatedUsers = [];
    
    for (const user of activeUsers) {
      try {
        console.log(`👤 Checking ${user.firstName || 'Unknown'} ${user.lastName || 'User'} (@${user.username})`);
        
        // Check user's subscription end date
        let endDate = null;
        
        if (user.subscriptionEndDate) {
          endDate = new Date(user.subscriptionEndDate);
          console.log(`   📅 User End Date: ${endDate.toISOString().split('T')[0]}`);
        } else {
          // Try to get end date from subscription
          const subscriptions = await Subscription.find({ user: user._id }).sort({ createdAt: -1 });
          
          if (subscriptions.length > 0) {
            const latestSub = subscriptions[0];
            if (latestSub.endDate) {
              endDate = new Date(latestSub.endDate);
              console.log(`   📅 Subscription End Date: ${endDate.toISOString().split('T')[0]}`);
            }
          }
        }
        
        if (!endDate) {
          console.log(`   ⚠️ No end date found - needs manual review`);
          noEndDateUsers.push(user);
          continue;
        }
        
        // Check if subscription has expired
        const timeDiff = today.getTime() - endDate.getTime();
        const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        
        if (daysDiff > 0) {
          console.log(`   ❌ EXPIRED ${daysDiff} days ago - UPDATING STATUS`);
          
          // Update user status to expired/free
          await User.findByIdAndUpdate(user._id, {
            subscriptionStatus: 'free',
            paymentRequired: true
          });
          
          // Update subscription status to expired
          await Subscription.updateMany(
            { user: user._id, status: 'active' },
            { status: 'expired' }
          );
          
          expiredUsers.push({
            user,
            endDate,
            daysExpired: daysDiff
          });
          
          updatedUsers.push({
            username: user.username,
            name: `${user.firstName || 'Unknown'} ${user.lastName || 'User'}`,
            daysExpired: daysDiff,
            endDate: endDate.toISOString().split('T')[0]
          });
          
          console.log(`   ✅ Updated to FREE status`);
          
        } else {
          const daysRemaining = Math.abs(daysDiff);
          console.log(`   ✅ Valid - expires in ${daysRemaining} days`);
          validUsers.push({
            user,
            endDate,
            daysRemaining
          });
        }
        
      } catch (userError) {
        console.error(`   ❌ Error processing ${user.username}:`, userError.message);
      }
    }
    
    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 SUBSCRIPTION EXPIRY UPDATE RESULTS');
    console.log('='.repeat(60));
    
    console.log(`\n✅ Valid Subscriptions: ${validUsers.length}`);
    console.log(`❌ Expired & Updated: ${expiredUsers.length}`);
    console.log(`⚠️ No End Date: ${noEndDateUsers.length}`);
    
    if (updatedUsers.length > 0) {
      console.log(`\n🔧 UPDATED USERS (${updatedUsers.length} users changed from ACTIVE to FREE):`);
      updatedUsers.forEach((userData, index) => {
        console.log(`   ${index + 1}. ${userData.name} (@${userData.username}) - Expired ${userData.daysExpired} days ago (${userData.endDate})`);
      });
    }
    
    if (noEndDateUsers.length > 0) {
      console.log(`\n⚠️ USERS WITH NO END DATE (${noEndDateUsers.length} users need manual review):`);
      noEndDateUsers.slice(0, 10).forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.firstName || 'Unknown'} ${user.lastName || 'User'} (@${user.username})`);
      });
      if (noEndDateUsers.length > 10) {
        console.log(`   ... and ${noEndDateUsers.length - 10} more users`);
      }
    }
    
    // Handle users with no end date
    if (noEndDateUsers.length > 0) {
      console.log('\n🔧 Fixing users with no end date...');
      
      for (const user of noEndDateUsers) {
        try {
          // Get their subscription to calculate end date
          const subscriptions = await Subscription.find({ user: user._id }).sort({ createdAt: -1 });
          
          if (subscriptions.length > 0) {
            const latestSub = subscriptions[0];
            
            // Calculate end date based on creation date + 6 months (standard duration)
            const createdDate = new Date(latestSub.createdAt);
            const calculatedEndDate = new Date(createdDate);
            calculatedEndDate.setMonth(calculatedEndDate.getMonth() + 6);
            
            const timeDiff = today.getTime() - calculatedEndDate.getTime();
            const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
            
            console.log(`   👤 ${user.username}: Created ${createdDate.toISOString().split('T')[0]}, Calculated end: ${calculatedEndDate.toISOString().split('T')[0]}`);
            
            if (daysDiff > 0) {
              console.log(`   ❌ Calculated as EXPIRED ${daysDiff} days ago - UPDATING`);
              
              await User.findByIdAndUpdate(user._id, {
                subscriptionStatus: 'free',
                paymentRequired: true,
                subscriptionEndDate: calculatedEndDate
              });
              
              await Subscription.findByIdAndUpdate(latestSub._id, {
                endDate: calculatedEndDate.toISOString().split('T')[0],
                status: 'expired'
              });
              
              updatedUsers.push({
                username: user.username,
                name: `${user.firstName || 'Unknown'} ${user.lastName || 'User'}`,
                daysExpired: daysDiff,
                endDate: calculatedEndDate.toISOString().split('T')[0],
                calculated: true
              });
              
            } else {
              console.log(`   ✅ Calculated as VALID - setting end date`);
              
              await User.findByIdAndUpdate(user._id, {
                subscriptionEndDate: calculatedEndDate
              });
              
              await Subscription.findByIdAndUpdate(latestSub._id, {
                endDate: calculatedEndDate.toISOString().split('T')[0]
              });
            }
          }
          
        } catch (calcError) {
          console.error(`   ❌ Error calculating end date for ${user.username}:`, calcError.message);
        }
      }
    }
    
    // Final summary
    console.log('\n🎯 FINAL RESULTS:');
    console.log(`   📊 Total Active Users Checked: ${activeUsers.length}`);
    console.log(`   ✅ Valid Subscriptions: ${validUsers.length}`);
    console.log(`   🔧 Updated to FREE (Expired): ${updatedUsers.length}`);
    console.log(`   ⚠️ Manual Review Needed: ${noEndDateUsers.filter(user => !updatedUsers.find(u => u.username === user.username)).length}`);
    
    if (updatedUsers.length > 0) {
      console.log('\n🎉 SUCCESS! Expired subscriptions have been updated.');
      console.log('📱 Users with expired subscriptions will now see subscription page instead of hub.');
    } else {
      console.log('\n🎉 All active users have valid subscriptions!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the fix
fixExpiredSubscriptions();
