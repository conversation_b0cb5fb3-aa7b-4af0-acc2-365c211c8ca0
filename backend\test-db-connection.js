const mongoose = require("mongoose");
require("dotenv").config();

async function testDatabaseConnection() {
  try {
    console.log("🔍 Testing database connection...");
    console.log(`📡 MongoDB URL: ${process.env.MONGO_URL.substring(0, 50)}...`);
    
    // Test connection
    await mongoose.connect(process.env.MONGO_URL);
    console.log("✅ Database connected successfully!");
    
    // Test basic operations
    const User = require("./models/userModel");
    const Exam = require("./models/examModel");
    
    // Count documents
    const userCount = await User.countDocuments();
    const examCount = await Exam.countDocuments();
    
    console.log(`📊 Database Statistics:`);
    console.log(`   - Users: ${userCount}`);
    console.log(`   - Exams: ${examCount}`);
    
    // Test AI-generated exams specifically
    const aiExams = await Exam.find({
      name: { $regex: /^[PSA]\d+[A-Z]{1,2}-[A-Z0-9]{2}$/ }
    });
    
    console.log(`   - AI-generated exams: ${aiExams.length}`);
    
    if (aiExams.length > 0) {
      console.log(`🤖 Recent AI-generated exams:`);
      aiExams.slice(0, 5).forEach(exam => {
        console.log(`   - ${exam.name} (${exam.level} Class ${exam.class}) - ${exam.questions.length} questions`);
      });
    }
    
    await mongoose.disconnect();
    console.log("✅ Database test completed successfully!");
    
  } catch (error) {
    console.error("❌ Database connection failed:", error.message);
    
    if (error.message.includes("ENOTFOUND")) {
      console.log("🔧 DNS resolution issue detected. Trying fallback connection...");
      
      try {
        await mongoose.connect(process.env.MONGO_URL_FALLBACK);
        console.log("✅ Fallback database connected successfully!");
        await mongoose.disconnect();
      } catch (fallbackError) {
        console.error("❌ Fallback connection also failed:", fallbackError.message);
      }
    }
  }
}

testDatabaseConnection();
