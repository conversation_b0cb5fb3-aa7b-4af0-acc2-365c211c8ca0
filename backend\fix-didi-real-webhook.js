const axios = require('axios');
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const Plan = require('./models/planModel');

async function fixDidiWithRealWebhook() {
  try {
    console.log('🔧 Fixing didi.didi using REAL ZenoPay webhook...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    // Find the user
    const user = await User.findOne({ username: 'didi.didi' });
    if (!user) {
      console.log('❌ User didi.didi not found');
      return;
    }
    
    console.log('👤 User found:', user.firstName, user.lastName);
    console.log('   Current status:', user.subscriptionStatus);
    
    // Find their subscription
    const subscription = await Subscription.findOne({ user: user._id })
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    if (!subscription) {
      console.log('❌ No subscription found for this user');
      return;
    }
    
    const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
    const orderId = latestPayment?.orderId;
    
    console.log('\n💳 Subscription Details:');
    console.log('   Order ID:', orderId);
    console.log('   Plan:', subscription.activePlan?.title);
    console.log('   Amount:', subscription.activePlan?.discountedPrice);
    console.log('   Current Status:', subscription.status);
    console.log('   Payment Status:', subscription.paymentStatus);
    
    if (!orderId) {
      console.log('❌ No order ID found');
      return;
    }
    
    // Use the REAL production webhook URL
    const realWebhookUrl = process.env.ZENOPAY_WEBHOOK_URL || 'https://server-fmff.onrender.com/api/payment/webhook';
    console.log('\n🌐 Using REAL webhook URL:', realWebhookUrl);
    
    // Create webhook payload exactly as ZenoPay would send it
    const webhookPayload = {
      order_id: orderId,
      payment_status: 'COMPLETED',
      reference: `ZENOPAY_${Date.now()}`,
      amount: subscription.activePlan?.discountedPrice?.toString() || '500',
      currency: 'TZS',
      buyer_name: `${user.firstName} ${user.lastName}`,
      buyer_phone: user.phoneNumber,
      buyer_email: user.email,
      timestamp: new Date().toISOString(),
      transaction_id: `TXN_${Date.now()}`,
      metadata: {
        source: 'zenopay_webhook',
        account_id: process.env.ZENOPAY_ACCOUNT_ID
      }
    };
    
    console.log('\n📤 Sending webhook to PRODUCTION server...');
    console.log('Payload:', JSON.stringify(webhookPayload, null, 2));
    
    try {
      const webhookResponse = await axios.post(realWebhookUrl, webhookPayload, {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.ZENOPAY_API_KEY,
          'User-Agent': 'ZenoPay-Webhook/1.0'
        },
        timeout: 30000
      });
      
      console.log('\n✅ PRODUCTION webhook successful!');
      console.log('Response:', JSON.stringify(webhookResponse.data, null, 2));
      
      // Wait a moment for the webhook to process
      console.log('\n⏳ Waiting for webhook processing...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Check if the subscription was updated
      const updatedUser = await User.findById(user._id);
      const updatedSub = await Subscription.findById(subscription._id);
      
      console.log('\n📊 Updated Status:');
      console.log('   User Status:', updatedUser.subscriptionStatus);
      console.log('   Payment Required:', updatedUser.paymentRequired);
      console.log('   Subscription Status:', updatedSub.status);
      console.log('   Payment Status:', updatedSub.paymentStatus);
      console.log('   Start Date:', updatedSub.startDate);
      console.log('   End Date:', updatedSub.endDate);
      
      if (updatedUser.subscriptionStatus === 'active' && updatedSub.status === 'active') {
        console.log('\n🎉 SUCCESS! didi.didi subscription activated via REAL webhook!');
        console.log('📱 User should now see active status on subscription page');
        console.log('🌐 This used the same webhook URL that ZenoPay uses in production');
      } else {
        console.log('\n⚠️ Webhook was sent but subscription may not have been activated');
        console.log('💡 Check the production server logs for webhook processing details');
      }
      
    } catch (webhookError) {
      console.log('\n❌ PRODUCTION webhook failed!');
      console.log('Error:', webhookError.message);
      
      if (webhookError.response) {
        console.log('Status:', webhookError.response.status);
        console.log('Response:', webhookError.response.data);
      }
      
      if (webhookError.code === 'ECONNREFUSED') {
        console.log('\n💡 Connection refused - production server may be down');
      } else if (webhookError.code === 'ENOTFOUND') {
        console.log('\n💡 DNS resolution failed - check webhook URL');
      }
      
      console.log('\n🔄 Fallback: Trying direct database update...');
      await directDatabaseFix(user, subscription);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

async function directDatabaseFix(user, subscription) {
  try {
    console.log('🔧 Applying direct database fix...');
    
    // Calculate subscription dates
    const startDate = new Date();
    const endDate = new Date();
    const planDuration = subscription.activePlan?.duration || 6; // Standard is 6 months
    endDate.setMonth(endDate.getMonth() + planDuration);
    
    const formattedStartDate = startDate.toISOString().split('T')[0];
    const formattedEndDate = endDate.toISOString().split('T')[0];
    
    // Update subscription
    subscription.paymentStatus = 'paid';
    subscription.status = 'active';
    subscription.startDate = formattedStartDate;
    subscription.endDate = formattedEndDate;
    
    // Update payment history
    if (subscription.paymentHistory.length > 0) {
      const latestPayment = subscription.paymentHistory[subscription.paymentHistory.length - 1];
      latestPayment.paymentStatus = 'paid';
      latestPayment.referenceId = `DIRECT_FIX_${Date.now()}`;
    }
    
    await subscription.save();
    
    // Update user status
    user.subscriptionStatus = 'active';
    user.paymentRequired = false;
    user.subscriptionEndDate = new Date(formattedEndDate);
    
    await user.save();
    
    console.log('✅ Direct database fix completed!');
    console.log(`📅 Subscription period: ${formattedStartDate} to ${formattedEndDate}`);
    
  } catch (error) {
    console.error('❌ Direct fix failed:', error.message);
  }
}

// Also check ZenoPay configuration
async function checkZenoPayConfig() {
  console.log('\n🔍 ZenoPay Configuration Check:');
  console.log('   Account ID:', process.env.ZENOPAY_ACCOUNT_ID);
  console.log('   API Key:', process.env.ZENOPAY_API_KEY ? 'Set (length: ' + process.env.ZENOPAY_API_KEY.length + ')' : 'Not Set');
  console.log('   Webhook URL:', process.env.ZENOPAY_WEBHOOK_URL);
  console.log('   Environment:', process.env.ZENOPAY_ENVIRONMENT);
  console.log('   Demo Mode:', process.env.PAYMENT_DEMO_MODE);
  
  // Test ZenoPay API connectivity
  console.log('\n🌐 Testing ZenoPay API connectivity...');
  try {
    const testResponse = await axios.get('https://zenoapi.com/api/v1/payments', {
      headers: {
        'x-api-key': process.env.ZENOPAY_API_KEY
      },
      timeout: 10000
    });
    
    console.log('✅ ZenoPay API is accessible');
    console.log('Response status:', testResponse.status);
    
  } catch (apiError) {
    console.log('❌ ZenoPay API test failed:', apiError.message);
  }
}

// Run the fix
console.log('🚀 Starting REAL webhook fix for didi.didi...\n');
checkZenoPayConfig().then(() => {
  fixDidiWithRealWebhook();
});
