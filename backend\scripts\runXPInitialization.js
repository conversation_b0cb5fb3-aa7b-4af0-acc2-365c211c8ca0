const mongoose = require("mongoose");
const { initializeXPSystem } = require("./initializeXPSystem");

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || "mongodb://localhost:27017/brainwave", {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log("✅ Connected to MongoDB");
  } catch (error) {
    console.error("❌ MongoDB connection error:", error);
    process.exit(1);
  }
};

// Main initialization function
const runInitialization = async () => {
  try {
    console.log("🚀 Starting XP System Initialization...");
    
    // Connect to database
    await connectDB();
    
    // Initialize XP system
    await initializeXPSystem();
    
    console.log("✅ XP System initialization completed successfully!");
    console.log("\n📋 Next steps:");
    console.log("1. Restart your server to load the new XP system");
    console.log("2. Test quiz completion to see XP being awarded");
    console.log("3. Check the new ranking page with XP-based leaderboards");
    console.log("4. Users will start at Level 1 with 0 XP");
    console.log("5. Existing points will be preserved as legacy data");
    
  } catch (error) {
    console.error("❌ Initialization failed:", error);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log("🔌 Database connection closed");
    process.exit(0);
  }
};

// Run the initialization
runInitialization();
