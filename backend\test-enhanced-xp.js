const mongoose = require('mongoose');
const User = require('./models/userModel');
const Report = require('./models/reportModel');
const enhancedXPService = require('./services/enhancedXPService');
const streakTrackingService = require('./services/streakTrackingService');
const xpRankingService = require('./services/xpRankingService');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/brainwave', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

class XPSystemTester {
  constructor() {
    this.testResults = {
      totalTests: 0,
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  /**
   * Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting Enhanced XP System Tests...\n');

    try {
      await this.testXPCalculation();
      await this.testStreakTracking();
      await this.testRankingSystem();
      await this.testUserMigration();
      
      this.printTestResults();
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    }
  }

  /**
   * Test XP calculation with sample data
   */
  async testXPCalculation() {
    console.log('📊 Testing XP Calculation...');
    
    try {
      // Create a test user
      const testUser = {
        _id: new mongoose.Types.ObjectId(),
        totalQuizzesTaken: 10,
        averageScore: 85,
        currentStreak: 5,
        activityTracking: {
          dailyLoginStreak: 7,
          quizCompletionStreak: 5,
          perfectScoreStreak: 2,
          subjectPerformance: [{
            subject: 'Mathematics',
            quizzesTaken: 5,
            averageScore: 90,
            quizzesPassed: 4
          }]
        }
      };

      // Test quiz result
      const quizResult = {
        score: 90,
        verdict: 'Pass',
        correctAnswers: [1, 2, 3, 4, 5, 6, 7, 8, 9],
        wrongAnswers: [10]
      };

      const examData = {
        _id: new mongoose.Types.ObjectId(),
        subject: 'Mathematics',
        difficulty: 'medium',
        duration: 30,
        name: 'Test Quiz'
      };

      // Calculate XP
      const xpResult = await enhancedXPService.calculateQuizXP({
        userId: testUser._id,
        examData: examData,
        result: quizResult,
        timeSpent: 1200, // 20 minutes
        isFirstAttempt: true,
        previousScore: null
      });

      this.assert(xpResult.xpAwarded > 0, 'XP should be awarded for quiz completion');
      this.assert(xpResult.breakdown.baseCompletion > 0, 'Base completion XP should be awarded');
      this.assert(xpResult.breakdown.correctAnswers > 0, 'Correct answers XP should be awarded');
      this.assert(xpResult.breakdown.streakBonus >= 0, 'Streak bonus should be calculated');

      console.log(`  ✅ XP Calculation: ${xpResult.xpAwarded} XP awarded`);
      console.log(`  📋 Breakdown:`, xpResult.breakdown);

    } catch (error) {
      this.recordError('XP Calculation', error);
    }
  }

  /**
   * Test streak tracking functionality
   */
  async testStreakTracking() {
    console.log('\n🔥 Testing Streak Tracking...');
    
    try {
      // Test login streak update
      const testUserId = new mongoose.Types.ObjectId();
      
      // Create a minimal user for testing
      const testUser = new User({
        _id: testUserId,
        name: 'Test User',
        email: '<EMAIL>',
        phoneNumber: '1234567890',
        school: 'Test School',
        class: '5',
        level: 'primary',
        password: 'testpassword'
      });

      // Don't save to database, just test the logic
      const streakResult = await streakTrackingService.updateQuizStreaks(testUserId, {
        score: 85,
        verdict: 'Pass',
        correctAnswers: [1, 2, 3, 4, 5, 6, 7, 8],
        wrongAnswers: [9, 10]
      }, {
        subject: 'Mathematics',
        difficulty: 'medium'
      });

      this.assert(typeof streakResult.quizCompletionStreak === 'number', 'Quiz completion streak should be a number');
      this.assert(typeof streakResult.quizPassStreak === 'number', 'Quiz pass streak should be a number');

      console.log('  ✅ Streak tracking logic validated');

    } catch (error) {
      this.recordError('Streak Tracking', error);
    }
  }

  /**
   * Test ranking system calculations
   */
  async testRankingSystem() {
    console.log('\n🏆 Testing Ranking System...');
    
    try {
      const testUser = {
        _id: new mongoose.Types.ObjectId(),
        totalXP: 2500,
        lifetimeXP: 3000,
        seasonXP: 2500,
        currentLevel: 5,
        averageScore: 85,
        bestStreak: 15,
        achievements: [
          { id: 'first_quiz', name: 'First Quiz' },
          { id: 'streak_5', name: '5 Quiz Streak' }
        ],
        activityTracking: {
          dailyLoginStreak: 10,
          quizCompletionStreak: 20,
          perfectScoreStreak: 3,
          subjectPerformance: [
            {
              subject: 'Mathematics',
              quizzesTaken: 10,
              averageScore: 90,
              quizzesPassed: 9
            }
          ]
        },
        subscriptionStatus: 'premium',
        subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
      };

      const rankingResult = await xpRankingService.calculateRankingScore(testUser);

      this.assert(rankingResult.success === true, 'Ranking calculation should succeed');
      this.assert(rankingResult.rankingScore > 0, 'Ranking score should be positive');
      this.assert(rankingResult.breakdown, 'Ranking breakdown should be provided');

      console.log(`  ✅ Ranking Score: ${rankingResult.rankingScore}`);
      console.log(`  📊 Breakdown:`, rankingResult.breakdown);

    } catch (error) {
      this.recordError('Ranking System', error);
    }
  }

  /**
   * Test user migration logic
   */
  async testUserMigration() {
    console.log('\n🔄 Testing User Migration Logic...');
    
    try {
      // Test with a user that has legacy points but no reports
      const legacyUser = {
        _id: new mongoose.Types.ObjectId(),
        name: 'Legacy User',
        totalPointsEarned: 1500,
        totalQuizzesTaken: 0,
        averageScore: 0,
        currentStreak: 0,
        bestStreak: 0
      };

      // Simulate migration calculations
      const estimatedQuizzes = Math.max(1, Math.floor(legacyUser.totalPointsEarned / 100));
      const estimatedAverageScore = Math.min(95, Math.max(60, 60 + (legacyUser.totalPointsEarned / estimatedQuizzes / 10)));
      const estimatedXP = Math.round(estimatedQuizzes * 50 * (estimatedAverageScore / 70));

      this.assert(estimatedQuizzes > 0, 'Should estimate positive number of quizzes');
      this.assert(estimatedAverageScore >= 60 && estimatedAverageScore <= 95, 'Estimated average should be reasonable');
      this.assert(estimatedXP > 0, 'Should estimate positive XP');

      console.log(`  ✅ Migration estimates: ${estimatedQuizzes} quizzes, ${estimatedAverageScore}% avg, ${estimatedXP} XP`);

    } catch (error) {
      this.recordError('User Migration', error);
    }
  }

  /**
   * Assert function for tests
   */
  assert(condition, message) {
    this.testResults.totalTests++;
    if (condition) {
      this.testResults.passed++;
    } else {
      this.testResults.failed++;
      this.testResults.errors.push(message);
      console.log(`  ❌ FAILED: ${message}`);
    }
  }

  /**
   * Record error
   */
  recordError(testName, error) {
    this.testResults.failed++;
    this.testResults.errors.push(`${testName}: ${error.message}`);
    console.log(`  ❌ ERROR in ${testName}:`, error.message);
  }

  /**
   * Print test results
   */
  printTestResults() {
    console.log('\n' + '='.repeat(60));
    console.log('🧪 TEST RESULTS');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${this.testResults.totalTests}`);
    console.log(`Passed: ${this.testResults.passed}`);
    console.log(`Failed: ${this.testResults.failed}`);
    
    if (this.testResults.errors.length > 0) {
      console.log('\n❌ ERRORS:');
      this.testResults.errors.forEach(error => {
        console.log(`  - ${error}`);
      });
    }

    const successRate = this.testResults.totalTests > 0 ? 
      Math.round((this.testResults.passed / this.testResults.totalTests) * 100) : 0;
    
    console.log(`\n📊 Success Rate: ${successRate}%`);
    
    if (successRate >= 80) {
      console.log('✅ Enhanced XP System tests PASSED!');
    } else {
      console.log('❌ Enhanced XP System tests FAILED!');
    }
    
    console.log('='.repeat(60));
  }
}

// Run tests if called directly
if (require.main === module) {
  const runTests = async () => {
    try {
      await connectDB();
      const tester = new XPSystemTester();
      await tester.runAllTests();
      process.exit(0);
    } catch (error) {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    }
  };

  runTests();
}

module.exports = XPSystemTester;
