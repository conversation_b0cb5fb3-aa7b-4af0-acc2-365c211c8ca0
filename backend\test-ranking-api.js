const mongoose = require('mongoose');
const jwt = require('jsonwebtoken');
const axios = require('axios');
require('dotenv').config();

// Import models
const User = require('./models/userModel');

async function testRankingAPI() {
    try {
        // Connect to MongoDB
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to MongoDB');

        // Find a test user
        const testUser = await User.findOne({ isAdmin: { $ne: true } });
        if (!testUser) {
            console.log('❌ No test user found');
            return;
        }

        console.log('👤 Test user:', testUser.name, testUser.email);

        // Generate a valid token
        const token = jwt.sign({ userId: testUser._id }, process.env.JWT_SECRET, { expiresIn: '1d' });
        console.log('🔑 Generated token:', token.substring(0, 50) + '...');

        // Test token verification
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        console.log('✅ Token verified, userId:', decoded.userId);

        // Test the XP leaderboard API
        console.log('\n🎯 Testing XP leaderboard API...');
        
        try {
            const response = await axios.get('http://localhost:5000/api/quiz/xp-leaderboard?limit=10', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ XP leaderboard API success');
            console.log('📊 Response status:', response.status);
            console.log('📊 Response data:', response.data.success ? 'Success' : 'Failed');
            console.log('📊 Users returned:', response.data.data?.length || 0);

            if (response.data.data && response.data.data.length > 0) {
                console.log('🥇 Top user:', response.data.data[0].name, 'XP:', response.data.data[0].totalXP);
            }

        } catch (apiError) {
            console.log('❌ XP leaderboard API failed:', apiError.response?.status, apiError.response?.data?.message || apiError.message);
        }

        // Test enhanced leaderboard API
        console.log('\n🎯 Testing enhanced leaderboard API...');
        
        try {
            const response = await axios.get('http://localhost:5000/api/quiz/enhanced-leaderboard?limit=10', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            console.log('✅ Enhanced leaderboard API success');
            console.log('📊 Response status:', response.status);
            console.log('📊 Response data:', response.data.success ? 'Success' : 'Failed');
            console.log('📊 Users returned:', response.data.data?.length || 0);

        } catch (apiError) {
            console.log('❌ Enhanced leaderboard API failed:', apiError.response?.status, apiError.response?.data?.message || apiError.message);
        }

    } catch (error) {
        console.error('❌ Error:', error);
    } finally {
        await mongoose.disconnect();
        console.log('✅ Disconnected from MongoDB');
    }
}

testRankingAPI();
