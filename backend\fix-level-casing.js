const mongoose = require('mongoose');
const User = require('./models/userModel');
const Exam = require('./models/examModel');
require('dotenv').config();

async function fixLevelCasing() {
  try {
    console.log('🔧 Fixing Level Casing Issues...');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');

    // Fix user levels - convert all to lowercase
    console.log('\n📊 Fixing User Levels...');
    const users = await User.find({});
    let userUpdates = 0;
    
    for (const user of users) {
      if (user.level && user.level !== user.level.toLowerCase()) {
        const oldLevel = user.level;
        user.level = user.level.toLowerCase();
        await user.save();
        console.log(`- Updated ${user.name}: ${oldLevel} → ${user.level}`);
        userUpdates++;
      }
    }
    console.log(`✅ Updated ${userUpdates} user levels`);

    // Fix exam levels - convert all to lowercase
    console.log('\n📚 Fixing Exam Levels...');
    const exams = await Exam.find({});
    let examUpdates = 0;
    
    for (const exam of exams) {
      if (exam.level && exam.level !== exam.level.toLowerCase()) {
        const oldLevel = exam.level;
        exam.level = exam.level.toLowerCase();
        await exam.save();
        console.log(`- Updated ${exam.name}: ${oldLevel} → ${exam.level}`);
        examUpdates++;
      }
    }
    console.log(`✅ Updated ${examUpdates} exam levels`);

    // Verify the fix
    console.log('\n🔍 Verification:');
    const allUsers = await User.find({}, 'name level').limit(5);
    const allExams = await Exam.find({}, 'name level').limit(5);
    
    console.log('Sample Users:');
    allUsers.forEach(user => {
      console.log(`- ${user.name}: ${user.level}`);
    });
    
    console.log('Sample Exams:');
    allExams.forEach(exam => {
      console.log(`- ${exam.name}: ${exam.level}`);
    });

    console.log('\n✅ Level casing fix completed!');

  } catch (error) {
    console.error('❌ Fix error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

fixLevelCasing();
