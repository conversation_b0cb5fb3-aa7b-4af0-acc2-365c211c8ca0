/* User Skills Page Styles */

.skills-container {
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* Header */
.skills-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  max-width: 600px;
  margin: 0 auto;
}

.skills-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin: 0 0 10px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.skills-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

/* Featured Section */
.featured-section {
  margin-bottom: 40px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.8rem;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-icon {
  color: #ffd700;
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.featured-skill-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.featured-skill-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

/* Controls */
.skills-controls {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-section {
  margin-bottom: 20px;
}

.search-input-container {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  font-size: 16px;
}

.search-input {
  width: 100%;
  padding: 15px 15px 15px 45px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.filters-section {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: center;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 150px;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: white;
  font-size: 14px;
}

.filter-select {
  padding: 10px 15px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  transition: all 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

/* Skills Grid */
.skills-content {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 25px;
}

.skill-card {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.skill-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.skill-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.skill-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.skill-card:hover .skill-thumbnail img {
  transform: scale(1.05);
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.skill-card:hover .play-overlay {
  opacity: 1;
}

.play-icon {
  color: white;
  font-size: 20px;
  margin-left: 3px;
}

.skill-level-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  padding: 5px 10px;
  border-radius: 15px;
  color: white;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.difficulty-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 8px;
  border-radius: 10px;
  font-size: 12px;
}

.skill-content {
  padding: 20px;
}

.skill-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.skill-description {
  color: #718096;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 15px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.skill-tags {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.skill-category {
  background: #e2e8f0;
  color: #4a5568;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.estimated-time {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #718096;
  font-size: 12px;
}

.skill-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skill-stats {
  display: flex;
  gap: 15px;
}

.skill-views,
.skill-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #718096;
  font-size: 12px;
}

.complete-btn {
  background: #48bb78;
  color: white;
  border: none;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.complete-btn:hover {
  background: #38a169;
  transform: scale(1.1);
}

/* Video Modal */
.video-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.video-modal-overlay.expanded {
  padding: 0;
}

.video-modal {
  background: white;
  border-radius: 15px;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.video-modal.expanded {
  max-width: 100vw;
  max-height: 100vh;
  border-radius: 0;
  width: 100vw;
  height: 100vh;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e2e8f0;
  background: #f7fafc;
}

.video-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.video-controls {
  display: flex;
  gap: 10px;
}

.expand-btn,
.close-btn {
  background: #e2e8f0;
  border: none;
  border-radius: 8px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.expand-btn:hover,
.close-btn:hover {
  background: #cbd5e0;
}

.video-container {
  position: relative;
  background: #000;
}

.video-container video {
  width: 100%;
  height: auto;
  display: block;
}

.video-error {
  padding: 60px 20px;
  text-align: center;
  color: white;
}

.video-info {
  padding: 25px;
}

.skill-details {
  margin-bottom: 20px;
}

.skill-meta-info {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.skill-level {
  padding: 5px 12px;
  border-radius: 15px;
  color: white;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.learning-outcomes {
  margin-top: 20px;
}

.learning-outcomes h4 {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 10px;
}

.learning-outcomes ul {
  list-style: none;
  padding: 0;
}

.learning-outcomes li {
  padding: 5px 0;
  color: #4a5568;
  position: relative;
  padding-left: 20px;
}

.learning-outcomes li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #48bb78;
  font-weight: bold;
}

.video-actions {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.complete-skill-btn {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.complete-skill-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(72, 187, 120, 0.4);
}

/* Loading and Error States */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state {
  text-align: center;
  padding: 60px 20px;
  color: white;
}

.error-state h3 {
  color: white;
  margin-bottom: 10px;
}

.retry-btn {
  background: white;
  color: #667eea;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.retry-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(255, 255, 255, 0.3);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: white;
}

.empty-icon {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 20px;
}

.empty-state h3 {
  color: white;
  margin-bottom: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .skills-container {
    padding: 15px;
  }

  .skills-title {
    font-size: 2rem;
  }

  .featured-grid {
    grid-template-columns: 1fr;
  }

  .skills-grid {
    grid-template-columns: 1fr;
  }

  .filters-section {
    flex-direction: column;
    gap: 15px;
  }

  .filter-group {
    min-width: auto;
  }

  .video-modal {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }

  .video-header {
    padding: 15px;
  }

  .video-title {
    font-size: 1.1rem;
  }

  .video-info {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .skills-header {
    padding: 30px 15px;
  }

  .skills-title {
    font-size: 1.8rem;
  }

  .skills-subtitle {
    font-size: 1rem;
  }

  .skills-controls {
    padding: 20px;
  }

  .search-input {
    padding: 12px 12px 12px 40px;
  }

  .skill-content {
    padding: 15px;
  }

  .skill-title {
    font-size: 1.1rem;
  }
}
