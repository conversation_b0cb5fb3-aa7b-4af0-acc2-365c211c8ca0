const mongoose = require('mongoose');
require('dotenv').config();

// Test multiple classes functionality
async function testMultipleClasses() {
  console.log('🧪 Testing Multiple Classes Functionality...\n');

  try {
    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    const Syllabus = require('./models/syllabusModel');

    // Test 1: Create a syllabus with multiple classes
    console.log('\n1️⃣ Testing syllabus creation with multiple classes...');
    
    const testSyllabus = new Syllabus({
      title: 'Mathematics Primary Classes 5-7 Syllabus 2024',
      description: 'Comprehensive mathematics syllabus for upper primary classes',
      level: 'primary',
      classes: ['5', '6', '7'], // Multiple classes
      subject: 'Mathematics',
      fileName: 'test-math-syllabus.pdf',
      originalFileName: 'Mathematics_Primary_5-7_2024.pdf',
      filePath: '/uploads/test/math-syllabus.pdf',
      fileSize: 1024000,
      mimeType: 'application/pdf',
      processingStatus: 'completed',
      extractedText: 'Sam<PERSON> extracted text from mathematics syllabus...',
      extractedTopics: [
        {
          topicName: 'Numbers and Operations',
          subtopics: [
            { name: 'Fractions', description: 'Understanding fractions' },
            { name: 'Decimals', description: 'Working with decimal numbers' }
          ],
          keyTerms: ['fraction', 'decimal', 'numerator', 'denominator'],
          difficulty: 'medium'
        }
      ],
      learningObjectives: [
        {
          objective: 'Students will understand basic fraction operations',
          topic: 'Numbers and Operations'
        }
      ],
      academicYear: '2024',
      tags: ['mathematics', 'primary', 'multi-class'],
      qualityScore: 85
    });

    await testSyllabus.save();
    console.log('✅ Successfully created syllabus with multiple classes:', testSyllabus.classes);

    // Test 2: Test findForAIGeneration with different classes
    console.log('\n2️⃣ Testing AI generation lookup for different classes...');
    
    const testCases = [
      { level: 'primary', class: '5', subject: 'Mathematics' },
      { level: 'primary', class: '6', subject: 'Mathematics' },
      { level: 'primary', class: '7', subject: 'Mathematics' },
      { level: 'primary', class: '4', subject: 'Mathematics' }, // Should not find
    ];

    for (const testCase of testCases) {
      const result = await Syllabus.findForAIGeneration(
        testCase.level, 
        testCase.class, 
        testCase.subject
      );
      
      if (result) {
        console.log(`✅ Found syllabus for ${testCase.level} Class ${testCase.class} ${testCase.subject}`);
        console.log(`   Covers classes: ${result.classes.join(', ')}`);
      } else {
        console.log(`❌ No syllabus found for ${testCase.level} Class ${testCase.class} ${testCase.subject}`);
      }
    }

    // Test 3: Test getAvailableSubjects with class filtering
    console.log('\n3️⃣ Testing available subjects lookup...');
    
    const subjects5 = await Syllabus.getAvailableSubjects('primary', '5');
    const subjects6 = await Syllabus.getAvailableSubjects('primary', '6');
    const subjects4 = await Syllabus.getAvailableSubjects('primary', '4');
    
    console.log('Available subjects for Primary Class 5:', subjects5);
    console.log('Available subjects for Primary Class 6:', subjects6);
    console.log('Available subjects for Primary Class 4:', subjects4);

    // Test 4: Test instance methods
    console.log('\n4️⃣ Testing instance methods...');
    
    const topics = testSyllabus.getTopicsForAI();
    console.log('✅ Topics for AI generation:', Object.keys(topics));
    
    await testSyllabus.updateUsageStats('Numbers and Operations');
    console.log('✅ Updated usage statistics');

    // Test 5: Test query with classes array
    console.log('\n5️⃣ Testing database queries with classes array...');
    
    const syllabusesForClass5 = await Syllabus.find({
      level: 'primary',
      classes: { $in: ['5'] },
      subject: 'Mathematics'
    });
    
    console.log(`Found ${syllabusesForClass5.length} syllabuses covering Class 5`);

    // Cleanup
    await Syllabus.deleteOne({ _id: testSyllabus._id });
    console.log('✅ Cleaned up test data');

    console.log('\n🎉 All multiple classes tests passed!');
    
    console.log('\n📋 Test Summary:');
    console.log('✅ Syllabus creation with multiple classes works');
    console.log('✅ AI generation lookup works for all covered classes');
    console.log('✅ Subject filtering works with classes array');
    console.log('✅ Instance methods work correctly');
    console.log('✅ Database queries work with classes array');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from database');
  }
}

// Test validation fixes
async function testValidationFixes() {
  console.log('\n🔧 Testing Validation Fixes...\n');

  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    const Syllabus = require('./models/syllabusModel');

    // Test 1: Create syllabus without extractedText (should work now)
    console.log('1️⃣ Testing syllabus creation without extractedText...');
    
    const testSyllabus = new Syllabus({
      title: 'Test Syllabus Without Extracted Text',
      level: 'primary',
      classes: ['5'],
      subject: 'Science',
      fileName: 'test-science.pdf',
      originalFileName: 'science.pdf',
      filePath: '/uploads/test/science.pdf',
      fileSize: 500000,
      mimeType: 'application/pdf',
      processingStatus: 'pending',
      // extractedText not provided - should default to empty string
      // uploadedBy not provided - should be optional now
    });

    await testSyllabus.save();
    console.log('✅ Successfully created syllabus without extractedText');
    console.log('   extractedText value:', `"${testSyllabus.extractedText}"`);

    // Test 2: Update with extractedText later
    console.log('\n2️⃣ Testing extractedText update...');
    
    testSyllabus.extractedText = 'This is the extracted text from the PDF processing...';
    testSyllabus.processingStatus = 'completed';
    await testSyllabus.save();
    
    console.log('✅ Successfully updated extractedText');
    console.log('   New extractedText length:', testSyllabus.extractedText.length);

    // Cleanup
    await Syllabus.deleteOne({ _id: testSyllabus._id });
    console.log('✅ Cleaned up test data');

    console.log('\n🎉 Validation fixes work correctly!');

  } catch (error) {
    console.error('❌ Validation test failed:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from database');
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Multiple Classes and Validation Tests\n');
  console.log('=' .repeat(60));
  
  await testValidationFixes();
  await testMultipleClasses();
  
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 All tests completed!');
  console.log('\n💡 The syllabus system now supports:');
  console.log('   📚 Multiple classes per syllabus (e.g., Classes 5, 6, 7)');
  console.log('   ✅ Fixed validation errors');
  console.log('   🔍 Smart AI lookup across all covered classes');
  console.log('   📊 Proper subject filtering');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = {
  testMultipleClasses,
  testValidationFixes,
  runAllTests
};
