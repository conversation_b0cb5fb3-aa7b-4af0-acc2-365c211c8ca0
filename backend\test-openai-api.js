const axios = require('axios');
require('dotenv').config();

async function testOpenAIAPI() {
  console.log('🧪 Testing OpenAI API Connection...\n');

  // Check if API key exists
  if (!process.env.OPENAI_API_KEY) {
    console.log('❌ OPENAI_API_KEY not found in environment variables');
    return;
  }

  console.log('✅ API Key found:', process.env.OPENAI_API_KEY.substring(0, 10) + '...');

  try {
    // Test simple API call
    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'user',
            content: 'Say "Hello, API test successful!" in exactly those words.'
          }
        ],
        max_tokens: 50,
        temperature: 0
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
        },
        timeout: 30000
      }
    );

    console.log('✅ OpenAI API Test Successful!');
    console.log('Response:', response.data.choices[0].message.content);
    console.log('Model used:', response.data.model);
    console.log('Usage:', response.data.usage);

    // Test explanation-style request
    console.log('\n🔍 Testing explanation request...');
    const explanationResponse = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are a helpful teacher. Explain why the student\'s answer is wrong and provide the correct answer.'
          },
          {
            role: 'user',
            content: 'Question: What is 2+2? Student answered: 5. Correct answer: 4. Please explain.'
          }
        ],
        max_tokens: 200,
        temperature: 0.7
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
        },
        timeout: 30000
      }
    );

    console.log('✅ Explanation Test Successful!');
    console.log('Explanation:', explanationResponse.data.choices[0].message.content);

  } catch (error) {
    console.log('❌ OpenAI API Test Failed!');
    console.log('Error:', error.message);
    
    if (error.response) {
      console.log('Status:', error.response.status);
      console.log('Status Text:', error.response.statusText);
      console.log('Response Data:', JSON.stringify(error.response.data, null, 2));
    }
    
    if (error.code) {
      console.log('Error Code:', error.code);
    }
  }
}

testOpenAIAPI();
