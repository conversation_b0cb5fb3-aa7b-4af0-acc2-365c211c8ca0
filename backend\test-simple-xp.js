const mongoose = require('mongoose');
const User = require('./models/userModel');
const enhancedXPService = require('./services/enhancedXPService');
require('dotenv').config();

async function testSimpleXP() {
  try {
    console.log('🔍 Testing Simple XP Award...\n');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');

    // Find a test user (non-admin)
    const testUser = await User.findOne({ isAdmin: { $ne: true } }).limit(1);
    if (!testUser) {
      console.log('❌ No test user found');
      return;
    }

    console.log(`👤 Test User: ${testUser.name} (${testUser.email})`);
    console.log(`📊 Current XP: ${testUser.totalXP || 0}`);
    console.log(`🎯 Current Level: ${testUser.currentLevel || 1}\n`);

    // Test direct XP awarding
    console.log('🏆 Awarding 50 XP directly...');
    const xpAwardResult = await enhancedXPService.awardXP({
      userId: testUser._id,
      xpAmount: 50,
      transactionType: 'quiz_completion',
      sourceId: null,
      sourceModel: 'exams',
      breakdown: {
        baseCompletion: 10,
        correctAnswers: 40
      },
      quizData: {
        examId: null,
        subject: 'Test',
        difficulty: 'medium',
        questionsTotal: 4,
        questionsCorrect: 4,
        timeSpent: 300,
        score: 100,
        isFirstAttempt: true,
      },
      metadata: {
        testRun: true
      }
    });

    console.log(`   Success: ${xpAwardResult.success}`);
    console.log(`   XP Awarded: ${xpAwardResult.xpAwarded}`);
    console.log(`   New Total XP: ${xpAwardResult.newTotalXP}`);
    console.log(`   Level Up: ${xpAwardResult.levelUp}`);
    console.log(`   New Level: ${xpAwardResult.newLevel}\n`);

    // Verify user was updated
    const updatedUser = await User.findById(testUser._id);
    console.log('✅ User Updated:');
    console.log(`   Total XP: ${updatedUser.totalXP}`);
    console.log(`   Current Level: ${updatedUser.currentLevel}`);
    console.log(`   Lifetime XP: ${updatedUser.lifetimeXP}`);
    console.log(`   Season XP: ${updatedUser.seasonXP}\n`);

    console.log('🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testSimpleXP();
