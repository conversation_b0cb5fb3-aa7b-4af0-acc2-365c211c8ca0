const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');

async function checkRealSubscriptionDates() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');
    
    console.log('🔍 Checking REAL subscription start/end dates from original data...\n');
    
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];
    
    console.log(`📅 Today's Date: ${todayString}\n`);
    
    // Find all users with active subscription status
    const activeUsers = await User.find({ subscriptionStatus: 'active' });
    
    console.log(`👥 Found ${activeUsers.length} users with active subscription status\n`);
    
    const expiredUsers = [];
    const validUsers = [];
    const suspiciousUsers = [];
    
    for (const user of activeUsers) {
      try {
        console.log(`👤 Checking ${user.firstName || 'Unknown'} ${user.lastName || 'User'} (@${user.username})`);
        
        // Get ALL subscriptions for this user
        const subscriptions = await Subscription.find({ user: user._id }).sort({ createdAt: -1 });
        
        if (subscriptions.length === 0) {
          console.log(`   ❌ No subscriptions found - should be FREE`);
          expiredUsers.push({
            user,
            reason: 'NO_SUBSCRIPTION',
            action: 'SET_TO_FREE'
          });
          continue;
        }
        
        console.log(`   📋 Found ${subscriptions.length} subscription(s)`);
        
        // Check each subscription for real dates
        let hasValidSubscription = false;
        let latestValidEnd = null;
        
        for (let i = 0; i < subscriptions.length; i++) {
          const sub = subscriptions[i];
          console.log(`\n   📋 Subscription ${i + 1}:`);
          console.log(`      Created: ${sub.createdAt.toISOString().split('T')[0]}`);
          console.log(`      Status: ${sub.status}`);
          console.log(`      Payment Status: ${sub.paymentStatus}`);
          console.log(`      Start Date: ${sub.startDate || 'Not Set'}`);
          console.log(`      End Date: ${sub.endDate || 'Not Set'}`);
          
          // Check if this subscription is valid
          if (sub.paymentStatus === 'paid' && sub.status === 'active') {
            if (sub.endDate) {
              const endDate = new Date(sub.endDate);
              const timeDiff = today.getTime() - endDate.getTime();
              const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
              
              console.log(`      📅 End Date Check: ${endDate.toISOString().split('T')[0]}`);
              
              if (daysDiff <= 0) {
                console.log(`      ✅ VALID - ${Math.abs(daysDiff)} days remaining`);
                hasValidSubscription = true;
                if (!latestValidEnd || endDate > latestValidEnd) {
                  latestValidEnd = endDate;
                }
              } else {
                console.log(`      ❌ EXPIRED - ${daysDiff} days ago`);
              }
            } else {
              // No end date set, calculate from creation date
              const createdDate = new Date(sub.createdAt);
              const calculatedEnd = new Date(createdDate);
              calculatedEnd.setMonth(calculatedEnd.getMonth() + 6); // Assume 6 months
              
              const timeDiff = today.getTime() - calculatedEnd.getTime();
              const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
              
              console.log(`      📅 Calculated End: ${calculatedEnd.toISOString().split('T')[0]} (from creation + 6 months)`);
              
              if (daysDiff <= 0) {
                console.log(`      ✅ VALID (calculated) - ${Math.abs(daysDiff)} days remaining`);
                hasValidSubscription = true;
                if (!latestValidEnd || calculatedEnd > latestValidEnd) {
                  latestValidEnd = calculatedEnd;
                }
              } else {
                console.log(`      ❌ EXPIRED (calculated) - ${daysDiff} days ago`);
              }
            }
          } else {
            console.log(`      ⏳ Not active/paid - Status: ${sub.status}, Payment: ${sub.paymentStatus}`);
          }
        }
        
        // Determine user status
        if (hasValidSubscription) {
          console.log(`\n   ✅ USER HAS VALID SUBSCRIPTION - expires ${latestValidEnd.toISOString().split('T')[0]}`);
          validUsers.push({
            user,
            endDate: latestValidEnd,
            daysRemaining: Math.floor((latestValidEnd.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
          });
        } else {
          console.log(`\n   ❌ USER HAS NO VALID SUBSCRIPTION - should be FREE`);
          
          // Check if this is suspicious (recent subscription but expired)
          const recentSub = subscriptions.find(sub => {
            const timeSinceCreated = Date.now() - new Date(sub.createdAt).getTime();
            const hoursAgo = timeSinceCreated / (1000 * 60 * 60);
            return hoursAgo < 48; // Created within last 48 hours
          });
          
          if (recentSub) {
            console.log(`   ⚠️ SUSPICIOUS - Recent subscription but expired/invalid`);
            suspiciousUsers.push({
              user,
              subscription: recentSub,
              reason: 'RECENT_BUT_INVALID'
            });
          } else {
            expiredUsers.push({
              user,
              reason: 'ALL_SUBSCRIPTIONS_EXPIRED',
              action: 'SET_TO_FREE'
            });
          }
        }
        
        console.log(''); // Empty line for readability
        
      } catch (userError) {
        console.error(`   ❌ Error processing ${user.username}:`, userError.message);
      }
    }
    
    // Summary
    console.log('\n' + '='.repeat(70));
    console.log('📊 REAL SUBSCRIPTION DATE ANALYSIS');
    console.log('='.repeat(70));
    
    console.log(`\n✅ Users with Valid Subscriptions: ${validUsers.length}`);
    console.log(`❌ Users with Expired Subscriptions: ${expiredUsers.length}`);
    console.log(`⚠️ Suspicious Cases: ${suspiciousUsers.length}`);
    
    if (expiredUsers.length > 0) {
      console.log(`\n❌ USERS TO UPDATE TO FREE STATUS (${expiredUsers.length}):`);
      expiredUsers.forEach((userData, index) => {
        const user = userData.user;
        console.log(`   ${index + 1}. ${user.firstName || 'Unknown'} ${user.lastName || 'User'} (@${user.username}) - ${userData.reason}`);
      });
      
      console.log('\n🔧 UPDATING EXPIRED USERS TO FREE STATUS...');
      
      for (const userData of expiredUsers) {
        try {
          await User.findByIdAndUpdate(userData.user._id, {
            subscriptionStatus: 'free',
            paymentRequired: true
          });
          
          // Update their subscriptions to expired
          await Subscription.updateMany(
            { user: userData.user._id, status: 'active' },
            { status: 'expired' }
          );
          
          console.log(`   ✅ Updated ${userData.user.username} to FREE status`);
          
        } catch (updateError) {
          console.error(`   ❌ Failed to update ${userData.user.username}:`, updateError.message);
        }
      }
    }
    
    if (suspiciousUsers.length > 0) {
      console.log(`\n⚠️ SUSPICIOUS CASES NEED MANUAL REVIEW (${suspiciousUsers.length}):`);
      suspiciousUsers.forEach((userData, index) => {
        const user = userData.user;
        const sub = userData.subscription;
        console.log(`   ${index + 1}. ${user.firstName || 'Unknown'} ${user.lastName || 'User'} (@${user.username})`);
        console.log(`      Recent subscription: ${sub.createdAt.toISOString().split('T')[0]} - ${sub.status}/${sub.paymentStatus}`);
      });
    }
    
    if (validUsers.length > 0) {
      console.log(`\n✅ USERS WITH VALID SUBSCRIPTIONS (${validUsers.length}):`);
      // Show first 10
      const displayUsers = validUsers.slice(0, 10);
      displayUsers.forEach((userData, index) => {
        const user = userData.user;
        console.log(`   ${index + 1}. ${user.firstName || 'Unknown'} ${user.lastName || 'User'} (@${user.username}) - ${userData.daysRemaining} days remaining`);
      });
      if (validUsers.length > 10) {
        console.log(`   ... and ${validUsers.length - 10} more valid subscriptions`);
      }
    }
    
    // Final summary
    console.log('\n🎯 FINAL RESULTS:');
    console.log(`   📊 Total Active Users Checked: ${activeUsers.length}`);
    console.log(`   ✅ Truly Valid: ${validUsers.length}`);
    console.log(`   🔧 Updated to FREE: ${expiredUsers.length}`);
    console.log(`   ⚠️ Need Manual Review: ${suspiciousUsers.length}`);
    
    if (expiredUsers.length > 0) {
      console.log('\n🎉 SUCCESS! Expired users have been updated to FREE status.');
      console.log('📱 These users will now see the subscription page instead of hub.');
    } else {
      console.log('\n🎉 All active users have genuinely valid subscriptions!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

// Run the real date check
checkRealSubscriptionDates();
