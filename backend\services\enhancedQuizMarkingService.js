const User = require('../models/userModel');

class EnhancedQuizMarkingService {
  constructor() {
    this.difficultyMultipliers = {
      easy: 1.0,
      medium: 1.5,
      hard: 2.0
    };
    
    this.questionTypeMultipliers = {
      mcq: 1.0,
      fill: 1.2,
      image: 1.3,
      text_based: 1.1
    };
  }

  /**
   * Calculate enhanced score for a quiz attempt
   * @param {Object} params - Quiz marking parameters
   * @param {Array} params.questions - Array of questions with answers
   * @param {Object} params.selectedOptions - User's selected answers
   * @param {Object} params.gptMap - AI evaluation results for free text questions
   * @param {number} params.timeSpent - Time spent on quiz in seconds
   * @param {number} params.totalTimeAllowed - Total time allowed for quiz
   * @param {Object} params.user - User object
   * @param {Object} params.examData - Exam configuration
   * @returns {Object} Enhanced quiz result
   */
  async calculateEnhancedScore(params) {
    const {
      questions,
      selectedOptions,
      gptMap = {},
      timeSpent,
      totalTimeAllowed,
      user,
      examData
    } = params;

    const results = {
      correctAnswers: [],
      wrongAnswers: [],
      partialAnswers: [],
      totalQuestions: questions.length,
      baseScore: 0,
      bonusPoints: 0,
      penaltyPoints: 0,
      finalScore: 0,
      finalPoints: 0,
      verdict: 'Fail',
      breakdown: {
        difficultyBonus: 0,
        timeBonus: 0,
        streakBonus: 0,
        consistencyBonus: 0,
        improvementBonus: 0
      },
      achievements: []
    };

    // Process each question
    let consecutiveCorrect = 0;
    let maxStreak = 0;
    let totalDifficultyPoints = 0;
    let totalPossiblePoints = 0;

    for (let idx = 0; idx < questions.length; idx++) {
      const question = questions[idx];
      const userAnswer = selectedOptions[idx] || "";
      const questionResult = this.evaluateQuestion(question, userAnswer, gptMap);
      
      // Calculate base points for this question
      const difficultyMultiplier = this.difficultyMultipliers[question.difficultyLevel] || 1.0;
      const typeMultiplier = this.questionTypeMultipliers[question.questionType] || 1.0;
      const baseQuestionPoints = 10 * difficultyMultiplier * typeMultiplier;
      
      totalPossiblePoints += baseQuestionPoints;

      if (questionResult.isCorrect) {
        consecutiveCorrect++;
        maxStreak = Math.max(maxStreak, consecutiveCorrect);
        totalDifficultyPoints += baseQuestionPoints;
        results.correctAnswers.push({
          ...question,
          userAnswer,
          points: baseQuestionPoints,
          ...questionResult
        });
      } else if (questionResult.isPartial) {
        consecutiveCorrect = 0;
        const partialPoints = baseQuestionPoints * questionResult.partialScore;
        totalDifficultyPoints += partialPoints;
        results.partialAnswers.push({
          ...question,
          userAnswer,
          points: partialPoints,
          ...questionResult
        });
      } else {
        consecutiveCorrect = 0;
        results.wrongAnswers.push({
          ...question,
          userAnswer,
          points: 0,
          ...questionResult
        });
      }
    }

    // Calculate base score
    results.baseScore = Math.round((totalDifficultyPoints / totalPossiblePoints) * 100);

    // Calculate bonuses
    results.breakdown.difficultyBonus = this.calculateDifficultyBonus(questions, results.correctAnswers);
    results.breakdown.timeBonus = this.calculateTimeBonus(timeSpent, totalTimeAllowed);
    results.breakdown.streakBonus = this.calculateStreakBonus(maxStreak);
    results.breakdown.consistencyBonus = await this.calculateConsistencyBonus(user._id);
    results.breakdown.improvementBonus = await this.calculateImprovementBonus(user._id, examData.subject);

    // Calculate total bonus points
    results.bonusPoints = Object.values(results.breakdown).reduce((sum, bonus) => sum + bonus, 0);

    // Calculate final score and points
    results.finalScore = Math.min(100, results.baseScore + results.bonusPoints);
    results.finalPoints = Math.round(totalDifficultyPoints + (results.bonusPoints * 2)); // Bonus points worth 2x in point calculation

    // Determine pass/fail - handle both passingMarks and passingPercentage
    const passingPercentage = examData.passingPercentage || examData.passingMarks || 70;
    results.verdict = results.finalScore >= passingPercentage ? "Pass" : "Fail";

    // Check for achievements
    results.achievements = await this.checkAchievements(user, results, maxStreak, examData);

    return results;
  }

  /**
   * Evaluate a single question with comprehensive error handling
   */
  evaluateQuestion(question, userAnswer, gptMap) {
    const result = {
      isCorrect: false,
      isPartial: false,
      partialScore: 0,
      reason: "",
      confidence: 1.0
    };

    // Enhanced question type detection
    const questionType = this.detectQuestionType(question);
    console.log(`🔍 Question evaluation for "${question.name || 'Unnamed Question'}":`);
    console.log(`   Detected Type: ${questionType}`);
    console.log(`   Original Type: ${question.type}`);
    console.log(`   Answer Type: ${question.answerType}`);
    console.log(`   User Answer: "${userAnswer}"`);

    if (questionType === 'mcq') {
      return this.evaluateMCQQuestion(question, userAnswer);
    } else if (questionType === 'fill') {
      return this.evaluateFillQuestion(question, userAnswer, gptMap);
    } else {
      console.log(`   ⚠️ Unknown question type, defaulting to fill evaluation`);
      return this.evaluateFillQuestion(question, userAnswer, gptMap);
    }
  }

  /**
   * Detect question type with multiple fallbacks
   */
  detectQuestionType(question) {
    // Check explicit type fields
    if (question.type === 'mcq' || question.answerType === 'Options') {
      return 'mcq';
    }
    if (question.type === 'fill' || question.answerType === 'Free Text' || question.answerType === 'Fill in the Blank') {
      return 'fill';
    }

    // Check if question has options (likely MCQ)
    if (question.options && typeof question.options === 'object' && Object.keys(question.options).length > 0) {
      return 'mcq';
    }

    // Check if question has correctOption (likely MCQ)
    if (question.correctOption) {
      return 'mcq';
    }

    // Default to fill if unclear
    return 'fill';
  }

  /**
   * Evaluate MCQ question with comprehensive answer checking
   */
  evaluateMCQQuestion(question, userAnswer) {
    const result = {
      isCorrect: false,
      isPartial: false,
      partialScore: 0,
      reason: "",
      confidence: 1.0
    };

    // Get correct answer with multiple fallbacks
    const correctAnswer = question.correctOption || question.correctAnswer || null;

    console.log(`   📝 MCQ Evaluation:`);
    console.log(`   Correct Answer: ${correctAnswer || 'MISSING'}`);
    console.log(`   User Answer: ${userAnswer || 'EMPTY'}`);
    console.log(`   Options:`, question.options || 'MISSING');

    // Check if correct answer is missing
    if (!correctAnswer) {
      console.log(`   ❌ CRITICAL: No correct answer specified for MCQ`);
      result.isCorrect = false;
      result.needsManualReview = true;
      result.confidence = 0.1;
      result.reason = "No correct answer specified for this MCQ question - needs admin review";
      return result;
    }

    // Check if options are missing
    if (!question.options || Object.keys(question.options).length === 0) {
      console.log(`   ❌ CRITICAL: No options provided for MCQ`);
      result.isCorrect = false;
      result.needsManualReview = true;
      result.confidence = 0.1;
      result.reason = "No options provided for this MCQ question - needs admin review";
      return result;
    }

    // Check if correct answer exists in options
    if (!question.options[correctAnswer]) {
      console.log(`   ❌ CRITICAL: Correct answer "${correctAnswer}" not found in options`);
      result.isCorrect = false;
      result.needsManualReview = true;
      result.confidence = 0.1;
      result.reason = `Correct answer "${correctAnswer}" not found in available options - needs admin review`;
      return result;
    }

    // Perform the actual comparison
    const isCorrect = String(correctAnswer) === String(userAnswer);
    result.isCorrect = isCorrect;
    result.confidence = 1.0;
    result.reason = isCorrect ? "Correct option selected" : `Incorrect option selected. Correct answer is ${correctAnswer}`;

    console.log(`   ${isCorrect ? '✅ CORRECT' : '❌ WRONG'} - ${result.reason}`);

    return result;
  }

  /**
   * Evaluate fill-in-the-blank question with enhanced logic
   */
  evaluateFillQuestion(question, userAnswer, gptMap) {

    const result = {
      isCorrect: false,
      isPartial: false,
      partialScore: 0,
      reason: "",
      confidence: 1.0
    };

    // Get correct answer with multiple fallbacks
    const correctAnswer = question.correctAnswer || question.answer || null;
    const userAnswerClean = (userAnswer || "").toString().toLowerCase().trim();

    console.log(`   📝 Fill Question Evaluation:`);
    console.log(`   User answer: "${userAnswerClean}"`);
    console.log(`   Admin correct answer: ${correctAnswer ? `"${correctAnswer}"` : 'EMPTY/NULL'}`);

    // Check if admin answer is empty or missing
    if (!correctAnswer || correctAnswer.toString().trim() === "") {
      console.log(`   ⚠️ EMPTY ADMIN ANSWER - No correct answer provided by admin`);
      console.log(`   🤖 Routing to AI evaluation for scientific/academic assessment`);

      // When admin answer is empty, use AI evaluation
      const gptResult = gptMap[question.name] || {};

      if (gptResult.isCorrect) {
        result.isCorrect = true;
        result.confidence = gptResult.confidence || 0.8;
        result.reason = "AI evaluation: Correct answer (admin answer was empty)";
        console.log(`   ✅ AI EVALUATION - CORRECT`);
      } else if (gptResult.partialScore && gptResult.partialScore > 0) {
        result.isPartial = true;
        result.partialScore = gptResult.partialScore;
        result.confidence = gptResult.confidence || 0.6;
        result.reason = "AI evaluation: Partially correct (admin answer was empty)";
        console.log(`   🟡 AI EVALUATION - PARTIAL (${Math.round(gptResult.partialScore * 100)}%)`);
      } else {
        // If no AI result available, mark for manual review
        result.isCorrect = false;
        result.needsManualReview = true;
        result.confidence = 0.1;
        result.reason = "No admin answer provided and AI evaluation unavailable - needs manual review";
        console.log(`   ❓ NEEDS MANUAL REVIEW - No admin answer and no AI evaluation`);
      }

      return result;
    }

    // Admin answer exists, proceed with normal comparison
    const correctAnswerClean = correctAnswer.toString().toLowerCase().trim();
    console.log(`   📋 Proceeding with admin answer comparison: "${correctAnswerClean}"`);

    // Check for placeholder answers first
    if (correctAnswerClean === "answer not available" ||
        correctAnswerClean === "not available" ||
        correctAnswerClean === "no answer" ||
        correctAnswerClean === "placeholder") {

      console.log(`   ⚠️ PLACEHOLDER DETECTED - Admin answer appears to be a placeholder: "${correctAnswerClean}"`);
      console.log(`   🤖 Will rely on AI evaluation for this question`);

      // Use AI evaluation for placeholder answers
      const gptResult = gptMap[question.name] || {};

      if (gptResult.isCorrect) {
        result.isCorrect = true;
        result.confidence = gptResult.confidence || 0.7;
        result.reason = "AI evaluation: Correct answer (admin answer was placeholder)";
        console.log(`   ✅ AI EVALUATION - CORRECT`);
      } else if (gptResult.partialScore && gptResult.partialScore > 0) {
        result.isPartial = true;
        result.partialScore = gptResult.partialScore;
        result.confidence = gptResult.confidence || 0.5;
        result.reason = "AI evaluation: Partially correct (admin answer was placeholder)";
        console.log(`   🟡 AI EVALUATION - PARTIAL`);
      } else {
        result.isCorrect = false;
        result.needsAIReview = true;
        result.reason = "Admin answer appears to be placeholder - needs review";
        result.confidence = 0.3;
        console.log(`   ❓ NEEDS AI REVIEW - Placeholder answer`);
      }

      return result;
    }

    // Direct comparison with admin's answer
    if (userAnswerClean === correctAnswerClean) {
      result.isCorrect = true;
      result.confidence = 1.0;
      result.reason = "Exact match with admin's correct answer";
      console.log(`   ✅ CORRECT - Exact match with admin answer`);
    }
    // Check for partial matches (contains, similar words)
    else if (correctAnswerClean && userAnswerClean &&
             (userAnswerClean.includes(correctAnswerClean) || correctAnswerClean.includes(userAnswerClean))) {
      const similarity = Math.min(userAnswerClean.length, correctAnswerClean.length) /
                        Math.max(userAnswerClean.length, correctAnswerClean.length);
      if (similarity >= 0.7) {
        result.isPartial = true;
        result.partialScore = similarity;
        result.confidence = 0.8;
        result.reason = "Partial match with admin's correct answer";
        console.log(`   🟡 PARTIAL (${Math.round(similarity * 100)}%) - Similar to admin answer`);
      } else {
        result.isCorrect = false;
        result.reason = "Does not match admin's correct answer";
        console.log(`   ❌ WRONG - Does not match admin answer`);
      }
    }
    // Check for word-level similarity (split by spaces and compare)
    else if (correctAnswerClean && userAnswerClean) {
      const correctWords = correctAnswerClean.split(/\s+/).filter(w => w.length > 2);
      const userWords = userAnswerClean.split(/\s+/).filter(w => w.length > 2);
      const commonWords = correctWords.filter(word =>
        userWords.some(userWord => userWord.includes(word) || word.includes(userWord))
      );

      if (commonWords.length > 0 && commonWords.length >= correctWords.length * 0.6) {
        result.isPartial = true;
        result.partialScore = commonWords.length / correctWords.length;
        result.confidence = 0.7;
        result.reason = "Contains key words from admin's correct answer";
        console.log(`   🟡 PARTIAL - Contains ${commonWords.length}/${correctWords.length} key words`);
      } else {
        result.isCorrect = false;
        result.reason = "Does not match admin's correct answer";
        console.log(`   ❌ WRONG - No significant match with admin answer`);
      }
    } else {
      result.isCorrect = false;
      result.reason = "Does not match admin's correct answer";
      console.log(`   ❌ WRONG - No match with admin answer`);
    }

    return result;
  }

  /**
   * Calculate difficulty bonus based on question difficulty distribution
   */
  calculateDifficultyBonus(questions, correctAnswers) {
    const hardQuestions = correctAnswers.filter(q => q.difficultyLevel === 'hard').length;
    const mediumQuestions = correctAnswers.filter(q => q.difficultyLevel === 'medium').length;
    
    return (hardQuestions * 3) + (mediumQuestions * 1); // 3 bonus points per hard, 1 per medium
  }

  /**
   * Calculate time bonus/penalty
   */
  calculateTimeBonus(timeSpent, totalTimeAllowed) {
    if (!timeSpent || !totalTimeAllowed) return 0;
    
    const timeRatio = timeSpent / totalTimeAllowed;
    
    if (timeRatio <= 0.5) {
      return 10; // Completed in half the time
    } else if (timeRatio <= 0.75) {
      return 5; // Completed in 3/4 the time
    } else if (timeRatio <= 1.0) {
      return 0; // Completed within time limit
    } else {
      return -5; // Overtime penalty
    }
  }

  /**
   * Calculate streak bonus
   */
  calculateStreakBonus(maxStreak) {
    if (maxStreak >= 10) return 15;
    if (maxStreak >= 7) return 10;
    if (maxStreak >= 5) return 5;
    if (maxStreak >= 3) return 2;
    return 0;
  }

  /**
   * Calculate consistency bonus based on recent performance
   */
  async calculateConsistencyBonus(userId) {
    try {
      const Report = require('../models/reportModel');
      
      // Get last 5 quiz results
      const recentReports = await Report.find({ user: userId })
        .sort({ createdAt: -1 })
        .limit(5)
        .populate('exam');

      if (recentReports.length < 3) return 0;

      const scores = recentReports.map(r => r.result.score || 0);
      const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
      const variance = scores.reduce((sum, score) => sum + Math.pow(score - average, 2), 0) / scores.length;
      const standardDeviation = Math.sqrt(variance);

      // Lower standard deviation = more consistent = higher bonus
      if (standardDeviation <= 5) return 8;
      if (standardDeviation <= 10) return 5;
      if (standardDeviation <= 15) return 2;
      return 0;
    } catch (error) {
      console.error('Error calculating consistency bonus:', error);
      return 0;
    }
  }

  /**
   * Calculate improvement bonus based on subject performance trend
   */
  async calculateImprovementBonus(userId, subject) {
    try {
      const Report = require('../models/reportModel');
      
      // Get last 3 attempts in this subject
      const subjectReports = await Report.find({ user: userId })
        .populate({
          path: 'exam',
          match: { subject: subject }
        })
        .sort({ createdAt: -1 })
        .limit(3);

      const validReports = subjectReports.filter(r => r.exam);
      
      if (validReports.length < 2) return 0;

      const scores = validReports.map(r => r.result.score || 0).reverse(); // Oldest first
      
      // Check for improvement trend
      let improvements = 0;
      for (let i = 1; i < scores.length; i++) {
        if (scores[i] > scores[i-1]) improvements++;
      }

      if (improvements === scores.length - 1) return 10; // Consistent improvement
      if (improvements >= Math.ceil((scores.length - 1) / 2)) return 5; // Mostly improving
      return 0;
    } catch (error) {
      console.error('Error calculating improvement bonus:', error);
      return 0;
    }
  }

  /**
   * Check for new achievements
   */
  async checkAchievements(user, results, maxStreak, examData) {
    const achievements = [];

    // Perfect score achievement
    if (results.finalScore === 100) {
      achievements.push({
        type: 'perfect_score',
        subject: examData.subject,
        metadata: { score: results.finalScore, exam: examData.name }
      });
    }

    // Streak achievements
    if (maxStreak >= 20) {
      achievements.push({ type: 'streak_20', metadata: { streak: maxStreak } });
    } else if (maxStreak >= 10) {
      achievements.push({ type: 'streak_10', metadata: { streak: maxStreak } });
    } else if (maxStreak >= 5) {
      achievements.push({ type: 'streak_5', metadata: { streak: maxStreak } });
    }

    // Speed achievement
    if (results.breakdown.timeBonus >= 10) {
      achievements.push({
        type: 'speed_demon',
        subject: examData.subject,
        metadata: { timeBonus: results.breakdown.timeBonus }
      });
    }

    // Consistency achievement
    if (results.breakdown.consistencyBonus >= 8) {
      achievements.push({
        type: 'consistent_learner',
        metadata: { consistencyBonus: results.breakdown.consistencyBonus }
      });
    }

    // Improvement achievement
    if (results.breakdown.improvementBonus >= 10) {
      achievements.push({
        type: 'improvement_star',
        subject: examData.subject,
        metadata: { improvementBonus: results.breakdown.improvementBonus }
      });
    }

    return achievements;
  }

  /**
   * Update user statistics after quiz completion
   */
  async updateUserStatistics(userId, results, examData) {
    try {
      const user = await User.findById(userId);
      if (!user) return;

      // Update basic stats
      user.totalQuizzesTaken = (user.totalQuizzesTaken || 0) + 1;
      user.totalPointsEarned = (user.totalPointsEarned || 0) + results.finalPoints;

      // Update average score
      const newAverage = ((user.averageScore || 0) * (user.totalQuizzesTaken - 1) + results.finalScore) / user.totalQuizzesTaken;
      user.averageScore = Math.round(newAverage * 100) / 100;

      // Update streak information
      if (results.verdict === 'Pass') {
        user.currentStreak = (user.currentStreak || 0) + 1;
        user.bestStreak = Math.max(user.bestStreak || 0, user.currentStreak);

        // Update passed exams count
        user.passedExamsCount = (user.passedExamsCount || 0) + 1;
      } else {
        user.currentStreak = 0;
      }

      // Update retry count if this is a retake
      if (results.isRetake) {
        user.retryCount = (user.retryCount || 0) + 1;
      }

      // Calculate pass rate
      const totalAttempts = user.totalQuizzesTaken;
      const passedAttempts = user.passedExamsCount || 0;
      user.passRate = totalAttempts > 0 ? Math.round((passedAttempts / totalAttempts) * 100) : 0;

      // Add new achievements
      for (const achievement of results.achievements) {
        const existingAchievement = user.achievements.find(a => 
          a.type === achievement.type && 
          a.subject === achievement.subject
        );
        
        if (!existingAchievement) {
          user.achievements.push(achievement);
        }
      }

      await user.save();

      // Update user's ranking position (async, don't wait for completion)
      this.updateUserRanking(userId).catch(error => {
        console.error('Error updating user ranking:', error);
      });

    } catch (error) {
      console.error('Error updating user statistics:', error);
    }
  }

  /**
   * Update user's ranking position after statistics change
   */
  async updateUserRanking(userId) {
    try {
      const xpRankingService = require('./xpRankingService');

      // Get updated ranking position
      const ranking = await xpRankingService.getUserRankingPosition(userId, 0);

      if (ranking.success) {
        // Update user's current rank in database for quick access
        await User.findByIdAndUpdate(userId, {
          currentRank: ranking.userRank,
          lastRankUpdate: new Date()
        });

        console.log(`Updated ranking for user ${userId}: Rank ${ranking.userRank}`);
      }
    } catch (error) {
      console.error('Error updating user ranking:', error);
    }
  }
}

module.exports = new EnhancedQuizMarkingService();
