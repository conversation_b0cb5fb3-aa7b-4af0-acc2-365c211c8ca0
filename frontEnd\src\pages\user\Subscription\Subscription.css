/* ===== RESPONSIVE SUBSCRIPTION PAGE STYLES ===== */
.subscription-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 1rem;
  position: relative;
  overflow-x: hidden;
}

/* Enhanced Responsive Padding - Mobile First Approach */
@media (max-width: 480px) {
  .subscription-page {
    padding: 0.75rem 0.5rem;
    min-height: 100vh;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .subscription-page {
    padding: 1rem 0.75rem;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .subscription-page {
    padding: 1.5rem 1rem;
  }
}

@media (min-width: 1025px) and (max-width: 1440px) {
  .subscription-page {
    padding: 2rem 1.5rem;
  }
}

@media (min-width: 1441px) {
  .subscription-page {
    padding: 2.5rem 2rem;
    max-width: 1600px;
    margin: 0 auto;
  }
}

/* Enhanced Subscription Container */
.subscription-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Enhanced Subscription Header */
.subscription-header {
  text-align: center;
  margin-bottom: 3rem;
  width: 100%;
  max-width: 900px;
  padding: 0 1rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.title-icon {
  color: #fbbf24;
  flex-shrink: 0;
}

.page-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Enhanced Responsive Header - Mobile First */
/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  .subscription-header {
    margin-bottom: 1.5rem;
    padding: 0 0.5rem;
  }

  .page-title {
    font-size: 1.5rem;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
  }

  .title-icon {
    font-size: 1.75rem;
  }

  .page-subtitle {
    font-size: 0.9rem;
    line-height: 1.5;
    padding: 0;
  }
}

/* Mobile (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .subscription-header {
    margin-bottom: 2rem;
  }

  .page-title {
    font-size: 1.75rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .title-icon {
    font-size: 2rem;
  }

  .page-subtitle {
    font-size: 1rem;
    padding: 0 0.5rem;
  }
}

/* Tablet (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .subscription-header {
    margin-bottom: 2.5rem;
  }

  .page-title {
    font-size: 2.25rem;
  }

  .title-icon {
    font-size: 2.25rem;
  }

  .page-subtitle {
    font-size: 1.05rem;
    max-width: 550px;
  }
}

/* Laptop (1025px - 1440px) */
@media (min-width: 1025px) and (max-width: 1440px) {
  .page-title {
    font-size: 2.75rem;
  }

  .title-icon {
    font-size: 2.5rem;
  }

  .page-subtitle {
    font-size: 1.15rem;
    max-width: 650px;
  }
}

/* Desktop (1441px+) */
@media (min-width: 1441px) {
  .subscription-header {
    margin-bottom: 3.5rem;
  }

  .page-title {
    font-size: 3rem;
  }

  .title-icon {
    font-size: 2.75rem;
  }

  .page-subtitle {
    font-size: 1.2rem;
    max-width: 700px;
  }
}

.section-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
  text-align: center;
}

.section-subtitle {
  font-size: 1rem;
  color: #6b7280;
  text-align: center;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Current Subscription */
.current-subscription {
  margin-bottom: 3rem;
}

.subscription-card {
  background: white;
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

/* Removed subscription-card.active styles - using inline styles for better control */

.subscription-card.expired {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2, #fef7f7);
}

.subscription-card.none {
  border-color: #6b7280;
  background: linear-gradient(135deg, #f9fafb, #f3f4f6);
}

.subscription-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.status-icon {
  font-size: 1.5rem;
}

.status-icon.active {
  color: #10b981;
}

.status-icon.expired {
  color: #ef4444;
}

.status-icon.none {
  color: #6b7280;
}

/* Removed status-text styles - using inline styles for active subscription */

.subscription-details {
  display: grid;
  gap: 1rem;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
  color: #374151;
}

.detail-icon {
  color: #6b7280;
  font-size: 1rem;
}

.renewal-message,
.upgrade-message {
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 0.5rem;
  color: #1e40af;
  font-weight: 500;
}

/* Enhanced Responsive Plans Grid */
.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  padding: 0 1rem;
}

/* Enhanced Responsive Grid - Mobile First */
/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  .plans-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-top: 1.5rem;
    padding: 0 0.5rem;
    max-width: 100%;
  }
}

/* Mobile (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .plans-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 1.5rem;
    padding: 0 1rem;
    max-width: 500px;
  }
}

/* Tablet (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .plans-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.75rem;
    max-width: 800px;
    padding: 0 1rem;
  }
}

/* Laptop (1025px - 1440px) */
@media (min-width: 1025px) and (max-width: 1440px) {
  .plans-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    max-width: 1200px;
    padding: 0 1.5rem;
  }
}

/* Desktop (1441px+) */
@media (min-width: 1441px) {
  .plans-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    max-width: 1400px;
    padding: 0 2rem;
  }
}

/* Enhanced Responsive Plan Card */
.plan-card {
  background: white;
  border-radius: 1.5rem;
  padding: 2.5rem 2rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  border: 2px solid #f1f5f9;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  max-width: 100%;
}

.plan-card:hover {
  border-color: #3b82f6;
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
}

/* Enhanced Plan Card Responsive Styles */
/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  .plan-card {
    padding: 1.25rem 1rem;
    border-radius: 1rem;
    margin: 0;
  }

  .plan-card:hover {
    transform: translateY(-4px);
  }
}

/* Mobile (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .plan-card {
    padding: 1.5rem 1.25rem;
    border-radius: 1.25rem;
    margin: 0;
  }

  .plan-card:hover {
    transform: translateY(-6px);
  }
}

/* Tablet (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .plan-card {
    padding: 2rem 1.5rem;
    border-radius: 1.5rem;
    margin: 0;
  }
}

/* Laptop (1025px - 1440px) */
@media (min-width: 1025px) and (max-width: 1440px) {
  .plan-card {
    padding: 2.5rem 2rem;
    border-radius: 1.5rem;
    margin: 0;
  }
}

/* Desktop (1441px+) */
@media (min-width: 1441px) {
  .plan-card {
    padding: 3rem 2.5rem;
    border-radius: 1.75rem;
    margin: 0;
  }
}

.plan-header {
  text-align: center;
  margin-bottom: 2rem;
  position: relative;
}

.plan-title {
  font-size: 1.75rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 0.75rem;
  letter-spacing: -0.025em;
}

.plan-badge {
  position: absolute;
  top: -1.5rem;
  right: -1.5rem;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 700;
  transform: rotate(12deg);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: rotate(12deg) scale(1); }
  50% { transform: rotate(12deg) scale(1.05); }
}

/* Responsive Plan Header Styles */
/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  .plan-header {
    margin-bottom: 1.25rem;
  }

  .plan-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .plan-badge {
    top: -1rem;
    right: -1rem;
    padding: 0.3rem 0.6rem;
    font-size: 0.7rem;
    border-radius: 1.5rem;
  }
}

/* Mobile (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .plan-header {
    margin-bottom: 1.5rem;
  }

  .plan-title {
    font-size: 1.5rem;
    margin-bottom: 0.6rem;
  }

  .plan-badge {
    top: -1.25rem;
    right: -1.25rem;
    padding: 0.4rem 0.8rem;
    font-size: 0.75rem;
  }
}

/* Tablet (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .plan-title {
    font-size: 1.625rem;
  }
}

/* Desktop (1441px+) */
@media (min-width: 1441px) {
  .plan-header {
    margin-bottom: 2.25rem;
  }

  .plan-title {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  .plan-badge {
    top: -1.75rem;
    right: -1.75rem;
    padding: 0.6rem 1.2rem;
    font-size: 1rem;
  }
}

.plan-pricing {
  text-align: center;
  margin-bottom: 2.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 1rem;
  border: 1px solid #e2e8f0;
}

.price-display {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.current-price {
  font-size: 2.5rem;
  font-weight: 900;
  color: #059669;
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.currency {
  font-size: 1.25rem;
  font-weight: 600;
  color: #6b7280;
}

.original-price {
  font-size: 1.5rem;
  color: #ef4444;
  text-decoration: line-through;
  position: relative;
  font-weight: 600;
}

.original-price::before {
  content: "❌";
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  font-size: 0.875rem;
  animation: bounce 1s infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

.discount-badge {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 700;
  margin-left: 0.5rem;
}

.plan-duration {
  color: #475569;
  font-weight: 600;
  font-size: 1.125rem;
  margin-top: 0.5rem;
}

.duration-highlight {
  color: #3b82f6;
  font-weight: 700;
}

/* Responsive Plan Pricing Styles */
/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  .plan-pricing {
    margin-bottom: 1.5rem;
    padding: 1rem;
  }

  .price-display {
    margin-bottom: 0.75rem;
    gap: 0.5rem;
    flex-direction: column;
    align-items: center;
  }

  .current-price {
    font-size: 1.75rem;
  }

  .currency {
    font-size: 1rem;
  }

  .original-price {
    font-size: 1.125rem;
  }

  .original-price::before {
    font-size: 0.75rem;
    top: -0.3rem;
    right: -0.3rem;
  }

  .discount-badge {
    font-size: 0.75rem;
    padding: 0.2rem 0.5rem;
    margin-left: 0.25rem;
  }

  .plan-duration {
    font-size: 0.9rem;
  }
}

/* Mobile (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .plan-pricing {
    margin-bottom: 2rem;
    padding: 1.25rem;
  }

  .price-display {
    gap: 0.75rem;
  }

  .current-price {
    font-size: 2rem;
  }

  .currency {
    font-size: 1.1rem;
  }

  .original-price {
    font-size: 1.25rem;
  }

  .discount-badge {
    font-size: 0.8rem;
    padding: 0.2rem 0.6rem;
  }

  .plan-duration {
    font-size: 1rem;
  }
}

/* Tablet (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .current-price {
    font-size: 2.25rem;
  }

  .original-price {
    font-size: 1.375rem;
  }
}

/* Desktop (1441px+) */
@media (min-width: 1441px) {
  .plan-pricing {
    margin-bottom: 3rem;
    padding: 2rem;
  }

  .price-display {
    margin-bottom: 1.25rem;
    gap: 1.25rem;
  }

  .current-price {
    font-size: 3rem;
  }

  .currency {
    font-size: 1.5rem;
  }

  .original-price {
    font-size: 1.75rem;
  }

  .original-price::before {
    font-size: 1rem;
    top: -0.6rem;
    right: -0.6rem;
  }

  .discount-badge {
    font-size: 1rem;
    padding: 0.3rem 0.9rem;
    margin-left: 0.75rem;
  }

  .plan-duration {
    font-size: 1.25rem;
    margin-top: 0.75rem;
  }
}

.plan-features {
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
  font-size: 0.95rem;
  color: #374151;
}

.feature-icon {
  color: #10b981;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.select-plan-btn {
  width: 100%;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.select-plan-btn:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-1px);
}

.select-plan-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Plan Features & Button Styles */
/* Small Mobile (320px - 480px) */
@media (max-width: 480px) {
  .plan-features {
    margin-bottom: 1.25rem;
  }

  .feature-item {
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
  }

  .feature-icon {
    font-size: 0.8rem;
  }

  .select-plan-btn {
    padding: 0.875rem 1.25rem;
    font-size: 0.9rem;
    border-radius: 0.625rem;
  }
}

/* Mobile (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .plan-features {
    margin-bottom: 1.5rem;
  }

  .feature-item {
    gap: 0.625rem;
    margin-bottom: 0.625rem;
    font-size: 0.9rem;
  }

  .feature-icon {
    font-size: 0.85rem;
  }

  .select-plan-btn {
    padding: 0.9375rem 1.375rem;
    font-size: 0.95rem;
    border-radius: 0.6875rem;
  }
}

/* Tablet (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .feature-item {
    font-size: 0.9375rem;
  }
}

/* Desktop (1441px+) */
@media (min-width: 1441px) {
  .plan-features {
    margin-bottom: 2.5rem;
  }

  .feature-item {
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 1.0625rem;
  }

  .feature-icon {
    font-size: 1rem;
  }

  .select-plan-btn {
    padding: 1.25rem 2rem;
    font-size: 1.125rem;
    border-radius: 1rem;
    gap: 0.75rem;
  }
}

.btn-icon {
  font-size: 0.9rem;
}

/* Phone Warning */
.phone-warning {
  margin-top: 2rem;
}

.warning-content {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 2px solid #f59e0b;
  border-radius: 1rem;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.warning-icon {
  color: #d97706;
  font-size: 1.5rem;
  flex-shrink: 0;
}

.warning-content h4 {
  margin: 0 0 0.5rem 0;
  color: #92400e;
  font-weight: 600;
}

.warning-content p {
  margin: 0 0 1rem 0;
  color: #a16207;
}

.update-phone-btn {
  background: #f59e0b;
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.update-phone-btn:hover {
  background: #d97706;
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

/* No Plans State */
.no-plans-state {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.no-plans-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.no-plans-state h3 {
  color: #374151;
  margin-bottom: 1rem;
}

.no-plans-state p {
  margin-bottom: 2rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.refresh-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.refresh-btn:hover {
  background: #2563eb;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Payment Modal Styles */
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(5px);
}

.payment-modal {
  background: white;
  border-radius: 20px;
  padding: 1.5rem;
  max-width: 500px;
  width: 90%;
  max-height: 95vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

.payment-modal.success-modal {
  max-width: 650px;
  max-height: 95vh;
  overflow: hidden;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.payment-modal-content {
  text-align: center;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  overflow: hidden;
}

/* Payment Modal Close Button */
.payment-modal-close {
  position: absolute;
  top: -10px;
  right: -10px;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e5e7eb;
  color: #666;
  font-size: 18px;
  font-weight: bold;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.payment-modal-close:hover {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border-color: #ff4d4f;
  transform: scale(1.1);
}

/* Try Again Section */
.payment-try-again {
  margin-top: 20px;
  padding: 15px;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.connection-issue {
  color: #d46b08;
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
}

.try-again-button {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.try-again-button:hover {
  background: linear-gradient(135deg, #096dd9 0%, #0050b3 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* Phone Instructions */
.phone-instruction {
  background: rgba(24, 144, 255, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.2);
  border-radius: 12px;
  padding: 15px;
  margin: 10px 0;
  text-align: center;
}

.phone-instruction h4 {
  color: #1890ff;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.phone-number {
  color: #1890ff;
  font-size: 16px;
  font-weight: 600;
  background: rgba(24, 144, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  display: inline-block;
  margin-bottom: 15px;
}

.instruction-text {
  color: #4a5568;
  font-size: 14px;
  line-height: 1.6;
  margin: 0;
  text-align: left;
}

.try-again-text {
  color: #8c4a00;
  font-size: 13px;
  margin-bottom: 10px;
  font-style: italic;
}

/* Professional Payment Modal Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.professional-payment-modal {
  animation: professionalSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.professional-success-modal {
  animation: successSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes professionalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes successSlideIn {
  from {
    opacity: 0;
    transform: translateY(-40px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enable background scrolling when modals are open for better UX */
body.modal-open {
  overflow: auto !important; /* Allow scrolling */
  position: static !important; /* Normal positioning */
  width: auto !important;
  height: auto !important;
}

/* Ensure subscription page remains scrollable behind modal */
.subscription-page {
  position: relative;
  z-index: 1; /* Below modal but above other content */
}

/* Best Design Modal Overlay - Perfect Centering */
.modal-overlay-best {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 10000 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-sizing: border-box !important;
}

/* Best Design Modal Container */
.modal-container-best {
  position: relative !important;
  margin: 0 !important;
  transform: translateZ(0) !important;
  animation: modalSlideInBest 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

@keyframes modalSlideInBest {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Best Design Responsive - Perfect Centering All Devices */

/* Mobile (320px - 768px) */
@media (max-width: 768px) {
  .modal-overlay-best {
    padding: 16px !important;
  }

  .modal-container-best {
    width: 100% !important;
    max-width: calc(100vw - 32px) !important;
    max-height: calc(100vh - 32px) !important;
    border-radius: 16px !important;
  }

  .modal-container-best > div:first-child {
    padding: 16px !important;
    border-radius: 16px 16px 0 0 !important;
  }

  .modal-container-best > div:last-child > div {
    padding: 16px !important;
    gap: 12px !important;
  }

  .try-again-button {
    width: 100% !important;
    max-width: none !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
  }
}

/* Tablet (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .modal-overlay-best {
    padding: 24px !important;
  }

  .modal-container-best {
    max-width: 500px !important;
    max-height: 90vh !important;
    border-radius: 20px !important;
  }

  .modal-container-best.success-modal-best {
    max-width: 600px !important;
  }

  .modal-container-best > div:first-child {
    padding: 20px !important;
    border-radius: 20px 20px 0 0 !important;
  }

  .modal-container-best > div:last-child > div {
    padding: 20px !important;
    gap: 16px !important;
  }

  .try-again-button {
    max-width: 200px !important;
    margin: 0 auto !important;
  }
}

/* Desktop (1025px+) */
@media (min-width: 1025px) {
  .modal-overlay-best {
    padding: 32px !important;
  }

  .modal-container-best {
    max-width: 480px !important;
    max-height: 85vh !important;
    border-radius: 24px !important;
  }

  .modal-container-best.success-modal-best {
    max-width: 600px !important;
    max-height: 90vh !important;
  }

  .modal-container-best > div:first-child {
    padding: 24px !important;
    border-radius: 24px 24px 0 0 !important;
  }

  .modal-container-best > div:last-child > div {
    padding: 24px !important;
    gap: 20px !important;
  }

  .try-again-button {
    max-width: 200px !important;
    margin: 0 auto !important;
  }
}

/* Responsive Design for Payment Modal */
@media (max-width: 768px) {
  /* Mobile Styles */
  .payment-modal {
    width: 95%;
    max-width: 400px;
    margin: 10px;
    max-height: 90vh;
    overflow-y: auto;
  }

  .payment-modal-content {
    padding: 20px 15px;
  }

  .payment-modal-close {
    top: -5px;
    right: -5px;
    width: 30px;
    height: 30px;
    font-size: 16px;
  }

  .phone-instruction {
    padding: 15px;
    margin: 10px 0;
  }

  .phone-instruction h4 {
    font-size: 16px;
  }

  .phone-number {
    font-size: 14px;
    padding: 6px 12px;
  }

  .instruction-text {
    font-size: 13px;
  }

  .payment-try-again {
    padding: 12px;
    margin-top: 15px;
  }

  .try-again-button {
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
  }

  .payment-processing-animation {
    transform: scale(0.8);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* Tablet Styles */
  .payment-modal {
    width: 80%;
    max-width: 500px;
  }

  .payment-modal-content {
    padding: 25px 20px;
  }

  .phone-instruction {
    padding: 18px;
  }

  .phone-instruction h4 {
    font-size: 17px;
  }

  .phone-number {
    font-size: 15px;
  }

  .try-again-button {
    padding: 11px 18px;
    font-size: 14px;
  }
}

@media (min-width: 1025px) {
  /* Desktop Styles */
  .payment-modal {
    width: 60%;
    max-width: 550px;
  }

  .payment-modal-content {
    padding: 30px 25px;
  }

  .phone-instruction {
    padding: 22px;
  }

  .phone-instruction h4 {
    font-size: 18px;
  }

  .phone-number {
    font-size: 16px;
  }

  .try-again-button {
    padding: 12px 20px;
    font-size: 15px;
  }
}

/* Enhanced Plan Card Responsive Styles */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
  .plans-grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
    padding: 0 0.5rem !important;
  }

  .plan-card {
    padding: 1.25rem 1rem !important;
    margin: 0 !important;
    border-radius: 1rem !important;
  }

  .plan-title {
    font-size: 1.4rem !important;
    margin-bottom: 0.5rem !important;
  }

  .plan-badge {
    top: -0.75rem !important;
    right: -0.75rem !important;
    padding: 0.25rem 0.5rem !important;
    font-size: 0.7rem !important;
  }

  .plan-pricing {
    padding: 0.75rem !important;
    margin-bottom: 1rem !important;
  }

  .current-price {
    font-size: 1.75rem !important;
  }

  .original-price {
    font-size: 1rem !important;
  }

  .plan-duration {
    font-size: 0.9rem !important;
  }

  .plan-features {
    margin-bottom: 1.25rem !important;
  }

  .feature-item {
    font-size: 0.8rem !important;
    padding: 0.4rem 0 !important;
    margin-bottom: 0.5rem !important;
  }

  .select-plan-btn {
    padding: 0.875rem 1rem !important;
    font-size: 0.9rem !important;
    border-radius: 10px !important;
  }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .plans-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1.5rem !important;
    padding: 0 1rem !important;
  }

  .plan-card {
    padding: 1.5rem 1.25rem !important;
    margin: 0 !important;
    border-radius: 1.25rem !important;
  }

  .plan-title {
    font-size: 1.5rem !important;
    margin-bottom: 0.75rem !important;
  }

  .plan-badge {
    top: -0.875rem !important;
    right: -0.875rem !important;
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  .plan-pricing {
    padding: 1rem !important;
    margin-bottom: 1.25rem !important;
  }

  .current-price {
    font-size: 2rem !important;
  }

  .original-price {
    font-size: 1.25rem !important;
  }

  .plan-duration {
    font-size: 1rem !important;
  }

  .plan-features {
    margin-bottom: 1.5rem !important;
  }

  .feature-item {
    font-size: 0.875rem !important;
    padding: 0.5rem 0 !important;
    margin-bottom: 0.6rem !important;
  }

  .select-plan-btn {
    padding: 1rem 1.25rem !important;
    font-size: 0.95rem !important;
    border-radius: 11px !important;
  }
}

/* Laptop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .plans-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 1.75rem !important;
    max-width: 900px !important;
    margin: 0 auto !important;
  }

  .plan-card {
    padding: 2rem 1.5rem !important;
    margin: 0 !important;
    border-radius: 1.5rem !important;
  }

  .plan-title {
    font-size: 1.6rem !important;
    margin-bottom: 0.75rem !important;
  }

  .plan-badge {
    top: -1rem !important;
    right: -1rem !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.8rem !important;
  }

  .plan-pricing {
    padding: 1.25rem !important;
    margin-bottom: 1.5rem !important;
  }

  .current-price {
    font-size: 2.25rem !important;
  }

  .original-price {
    font-size: 1.4rem !important;
  }

  .plan-duration {
    font-size: 1.1rem !important;
  }

  .plan-features {
    margin-bottom: 1.75rem !important;
  }

  .feature-item {
    font-size: 0.9rem !important;
    padding: 0.6rem 0 !important;
    margin-bottom: 0.7rem !important;
  }

  .select-plan-btn {
    padding: 1rem 1.5rem !important;
    font-size: 1rem !important;
    border-radius: 12px !important;
  }
}

/* Desktop Devices (1025px+) */
@media (min-width: 1025px) {
  .plans-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 2rem !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
  }

  .plan-card {
    padding: 2.5rem 2rem !important;
    margin: 0 !important;
    border-radius: 1.5rem !important;
  }

  .plan-title {
    font-size: 1.75rem !important;
    margin-bottom: 0.75rem !important;
  }

  .plan-badge {
    top: -1rem !important;
    right: -1rem !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.85rem !important;
  }

  .plan-pricing {
    padding: 1.5rem !important;
    margin-bottom: 2rem !important;
  }

  .current-price {
    font-size: 2.5rem !important;
  }

  .original-price {
    font-size: 1.5rem !important;
  }

  .plan-duration {
    font-size: 1.125rem !important;
  }

  .plan-features {
    margin-bottom: 2rem !important;
  }

  .feature-item {
    font-size: 0.95rem !important;
    padding: 0.75rem 0 !important;
    margin-bottom: 0.75rem !important;
  }

  .select-plan-btn {
    padding: 1rem 1.5rem !important;
    font-size: 1rem !important;
    border-radius: 12px !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .plan-card {
    padding: 2rem 1.5rem;
  }

  .plan-title {
    font-size: 1.625rem;
  }

  .current-price {
    font-size: 2.25rem;
  }

  .original-price {
    font-size: 1.375rem;
  }

  .plan-duration {
    font-size: 1.0625rem;
  }

  .feature-item {
    font-size: 0.9375rem;
  }
}

@media (min-width: 1025px) {
  .plan-card {
    padding: 2.5rem 2rem;
  }

  .plan-title {
    font-size: 1.75rem;
  }

  .current-price {
    font-size: 2.5rem;
  }

  .original-price {
    font-size: 1.5rem;
  }

  .plan-duration {
    font-size: 1.125rem;
  }

  .feature-item {
    font-size: 1rem;
  }
}

/* Payment Processing Animation */
.payment-processing-animation {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
}

.payment-spinner {
  width: 80px;
  height: 80px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 10px;
  left: 10px;
}

.payment-pulse {
  width: 100px;
  height: 100px;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
  position: absolute;
  top: 0;
  left: 0;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.5;
  }
  100% {
    transform: scale(0.8);
    opacity: 1;
  }
}

.payment-modal h3 {
  color: #1f2937;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.payment-status {
  color: #6b7280;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.payment-plan-info {
  background: #f8fafc;
  border-radius: 10px;
  padding: 0.75rem;
  margin: 0.5rem 0;
}

.payment-plan-info h4 {
  color: #1f2937;
  margin-bottom: 0.25rem;
  font-size: 16px;
}

.payment-plan-info p {
  color: #6b7280;
  margin: 0.125rem 0;
  font-size: 14px;
}

.payment-instructions {
  background: #e0f2fe;
  border-radius: 10px;
  padding: 1rem;
  margin-top: 1.5rem;
}

.payment-instructions p {
  margin: 0.5rem 0;
  color: #0c4a6e;
  font-size: 0.9rem;
}

/* Success Modal Styles */
.success-animation {
  position: relative;
  margin-bottom: 2rem;
}

.success-checkmark {
  font-size: 4rem;
  animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.success-confetti {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
  border-radius: 50%;
  animation: confetti 1s ease-out;
  opacity: 0;
}

@keyframes confetti {
  0% {
    transform: translateX(-50%) scale(0);
    opacity: 1;
  }
  50% {
    transform: translateX(-50%) scale(1.5);
    opacity: 0.8;
  }
  100% {
    transform: translateX(-50%) scale(0);
    opacity: 0;
  }
}

.success-message {
  color: #059669;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  font-weight: 500;
}

.success-plan-info {
  background: #ecfdf5;
  border: 1px solid #10b981;
  border-radius: 10px;
  padding: 1rem;
  margin: 1rem 0;
}

.success-plan-info h4 {
  color: #065f46;
  margin-bottom: 0.5rem;
}

.success-plan-info p {
  color: #047857;
  margin: 0.25rem 0;
}

.success-features {
  text-align: left;
  background: #f8fafc;
  border-radius: 10px;
  padding: 1.5rem;
  margin: 1.5rem 0;
}

.success-features h4 {
  color: #1f2937;
  margin-bottom: 1rem;
  text-align: center;
}

.success-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.success-features li {
  color: #374151;
  margin: 0.5rem 0;
  font-size: 0.95rem;
}

.success-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.success-btn {
  flex: 1;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 120px;
}

.success-btn.primary {
  background: #3b82f6;
  color: white;
}

.success-btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.success-btn.secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.success-btn.secondary:hover {
  background: #e5e7eb;
}

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
  .payment-modal {
    padding: 0.75rem;
    margin: 0.25rem;
    max-height: 98vh;
    width: 98%;
    border-radius: 16px;
  }

  .payment-modal.success-modal {
    max-height: 98vh;
    width: 98%;
  }

  .payment-modal-content {
    padding: 0.25rem 0;
    gap: 0.5rem;
  }

  .payment-modal-close {
    width: 28px;
    height: 28px;
    font-size: 14px;
    top: 8px;
    right: 8px;
  }

  .payment-processing-animation {
    width: 50px;
    height: 50px;
    margin: 0 auto 0.5rem;
  }

  .phone-instruction {
    padding: 8px;
    margin: 6px 0;
    border-radius: 8px;
  }

  .phone-instruction h4 {
    font-size: 13px;
    margin: 0 0 4px 0;
  }

  .phone-instruction p {
    font-size: 11px;
    margin: 2px 0;
  }

  .payment-plan-info {
    padding: 0.4rem;
    margin: 0.2rem 0;
    border-radius: 8px;
  }

  .payment-plan-info h4 {
    font-size: 13px;
    margin-bottom: 0.2rem;
  }

  .payment-plan-info p {
    font-size: 11px;
    margin: 0.1rem 0;
  }

  .success-actions {
    flex-direction: column;
    gap: 6px;
    margin-top: 0.75rem;
  }

  .success-btn {
    width: 100%;
    padding: 8px 12px;
    font-size: 12px;
    border-radius: 6px;
  }

  /* Success Modal Specific */
  .success-details {
    padding: 10px !important;
    margin-bottom: 10px !important;
  }

  .success-details h3 {
    font-size: 14px !important;
    margin-bottom: 6px !important;
  }

  .success-details p {
    font-size: 11px !important;
    margin: 1px 0 !important;
  }

  .success-features {
    padding: 8px !important;
    margin-bottom: 10px !important;
  }

  .success-features h3 {
    font-size: 12px !important;
    margin-bottom: 6px !important;
  }

  .success-features p {
    font-size: 10px !important;
    margin: 1px 0 !important;
  }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .payment-modal {
    padding: 1rem;
    margin: 0.5rem;
    max-height: 95vh;
    width: 95%;
    border-radius: 18px;
  }

  .payment-modal.success-modal {
    max-height: 95vh;
    width: 95%;
    max-width: 600px;
  }

  .payment-modal-content {
    padding: 0.5rem 0;
    gap: 0.75rem;
  }

  .payment-modal-close {
    width: 32px;
    height: 32px;
    font-size: 16px;
    top: 12px;
    right: 12px;
  }

  .payment-processing-animation {
    width: 70px;
    height: 70px;
    margin: 0 auto 0.75rem;
  }

  .phone-instruction {
    padding: 12px;
    margin: 8px 0;
    border-radius: 10px;
  }

  .phone-instruction h4 {
    font-size: 15px;
    margin: 0 0 6px 0;
  }

  .phone-instruction p {
    font-size: 13px;
    margin: 3px 0;
  }

  .payment-plan-info {
    padding: 0.6rem;
    margin: 0.3rem 0;
    border-radius: 10px;
  }

  .payment-plan-info h4 {
    font-size: 15px;
    margin-bottom: 0.3rem;
  }

  .payment-plan-info p {
    font-size: 13px;
    margin: 0.15rem 0;
  }

  .success-actions {
    flex-direction: row;
    gap: 10px;
    margin-top: 1rem;
    justify-content: center;
  }

  .success-btn {
    padding: 10px 18px;
    font-size: 13px;
    border-radius: 8px;
    min-width: 130px;
  }

  /* Success Modal Specific */
  .success-details {
    padding: 12px !important;
    margin-bottom: 12px !important;
  }

  .success-details h3 {
    font-size: 15px !important;
    margin-bottom: 8px !important;
  }

  .success-details p {
    font-size: 13px !important;
    margin: 2px 0 !important;
  }

  .success-features {
    padding: 10px !important;
    margin-bottom: 12px !important;
  }

  .success-features h3 {
    font-size: 13px !important;
    margin-bottom: 8px !important;
  }

  .success-features p {
    font-size: 11px !important;
    margin: 2px 0 !important;
  }
}

/* Laptop/Desktop Devices (769px+) */
@media (min-width: 769px) {
  .payment-modal {
    padding: 1.5rem;
    margin: 1rem;
    max-height: 90vh;
    width: 90%;
    max-width: 500px;
    border-radius: 20px;
  }

  .payment-modal.success-modal {
    max-height: 90vh;
    width: 90%;
    max-width: 650px;
  }

  .payment-modal-content {
    padding: 0.75rem 0;
    gap: 1rem;
  }

  .payment-modal-close {
    width: 36px;
    height: 36px;
    font-size: 18px;
    top: 16px;
    right: 16px;
  }

  .payment-processing-animation {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
  }

  .phone-instruction {
    padding: 15px;
    margin: 10px 0;
    border-radius: 12px;
  }

  .phone-instruction h4 {
    font-size: 16px;
    margin: 0 0 8px 0;
  }

  .phone-instruction p {
    font-size: 14px;
    margin: 4px 0;
  }

  .payment-plan-info {
    padding: 0.75rem;
    margin: 0.5rem 0;
    border-radius: 10px;
  }

  .payment-plan-info h4 {
    font-size: 16px;
    margin-bottom: 0.25rem;
  }

  .payment-plan-info p {
    font-size: 14px;
    margin: 0.125rem 0;
  }

  .success-actions {
    flex-direction: row;
    gap: 12px;
    margin-top: 1.5rem;
    justify-content: center;
  }

  .success-btn {
    padding: 10px 20px;
    font-size: 14px;
    border-radius: 8px;
    min-width: 140px;
  }

  /* Success Modal Specific */
  .success-details {
    padding: 15px !important;
    margin-bottom: 15px !important;
  }

  .success-details h3 {
    font-size: 16px !important;
    margin-bottom: 8px !important;
  }

  .success-details p {
    font-size: 14px !important;
    margin: 2px 0 !important;
  }

  .success-features {
    padding: 12px !important;
    margin-bottom: 16px !important;
  }

  .success-features h3 {
    font-size: 14px !important;
    margin-bottom: 8px !important;
  }

  .success-features p {
    font-size: 12px !important;
    margin: 2px 0 !important;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .subscription-page {
    padding: 1rem 0.5rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .plans-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .plan-card {
    padding: 1.5rem;
  }

  .warning-content {
    flex-direction: column;
    text-align: center;
  }
}

/* Enhanced Success Modal Animations */
@keyframes successPulse {
  0% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  70% {
    box-shadow: 0 0 0 20px rgba(82, 196, 26, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

.success-modal.immediate {
  animation: modalSlideIn 0.4s ease-out, successPulse 2s ease-out 0.4s;
}

/* Enhanced button hover effects */
.success-btn {
  position: relative;
  overflow: hidden;
}

.success-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.success-btn:hover::before {
  left: 100%;
}

/* ===== PROFESSIONAL PAYMENT MODALS ===== */

/* Enhanced Modal Overlay with Background Scrolling */
.payment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(15, 23, 42, 0.3); /* More transparent to show background */
  backdrop-filter: blur(4px); /* Less blur to keep background visible */
  -webkit-backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
  overflow: hidden; /* Prevent any scrolling issues */
}

/* Background scroll indicator */
.payment-modal-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none; /* Allow clicks to pass through */
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(59, 130, 246, 0.05) 20%,
    rgba(59, 130, 246, 0.05) 80%,
    transparent 100%
  );
  z-index: -1; /* Behind modal but above background */
}

/* Scroll hint for background */
.payment-modal-overlay::after {
  content: '↕ Background scrollable';
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(59, 130, 246, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  pointer-events: none;
  z-index: 10001;
  animation: scrollHintPulse 3s infinite;
  opacity: 0.8;
}

@keyframes scrollHintPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.4; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Optimal Modal Container for Best User Experience */
.payment-modal-container {
  background: #ffffff;
  border-radius: 24px;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  animation: slideUp 0.4s ease-out;
  border: 1px solid rgba(226, 232, 240, 0.8);
  margin: 0 auto;
}

.payment-modal-container.success {
  max-width: 600px;
}

/* Tablet Optimization */
@media (min-width: 769px) and (max-width: 1024px) {
  .payment-modal-container {
    max-width: 520px;
  }

  .payment-modal-container.success {
    max-width: 620px;
  }
}

/* Desktop Optimization */
@media (min-width: 1025px) {
  .payment-modal-container {
    max-width: 500px;
  }

  .payment-modal-container.success {
    max-width: 600px;
  }
}

/* Smooth, Professional Modal Animation */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Close Button */
.modal-close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 40px;
  height: 40px;
  border: none;
  background: rgba(15, 23, 42, 0.1);
  border-radius: 12px;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 10;
}

.modal-close-btn:hover {
  background: rgba(15, 23, 42, 0.15);
  color: #334155;
  transform: scale(1.05);
}

/* Modal Header */
.modal-header {
  padding: 32px 32px 24px;
  text-align: center;
  border-bottom: 1px solid #f1f5f9;
}

.modal-header.processing {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border-bottom: none;
}

.modal-header.success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border-bottom: none;
}

.modal-header h2 {
  margin: 16px 0 8px;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
}

.modal-header p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
  font-weight: 500;
}

/* Icons */
.processing-icon {
  position: relative;
  width: 64px;
  height: 64px;
  margin: 0 auto;
}

.spinner {
  position: absolute;
  width: 64px;
  height: 64px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.payment-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
}

.success-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto;
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}

/* Fixed Modal Content - No Internal Scrolling */
.modal-content {
  padding: 20px 24px 24px;
  max-height: none;
  overflow: visible;
  position: relative;
}

/* Highly Visible Scrollbar Styling */
.modal-content::-webkit-scrollbar {
  width: 10px; /* Wider scrollbar */
}

.modal-content::-webkit-scrollbar-track {
  background: #e2e8f0;
  border-radius: 5px;
  margin: 4px 0;
  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.1);
}

.modal-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%); /* Vibrant blue gradient */
  border-radius: 5px;
  border: 1px solid #bfdbfe;
  transition: all 0.2s ease;
  box-shadow: 0 0 3px rgba(59, 130, 246, 0.3);
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #2563eb 0%, #1e40af 100%);
  transform: scaleX(1.1);
}

.modal-content::-webkit-scrollbar-thumb:active {
  background: #1e40af;
}

/* Scroll Indicator Animation */
@keyframes scrollPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.4; }
}

/* Enhanced Scroll Indicators for High Visibility */
.modal-content::before {
  content: '';
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  height: 4px; /* Thicker indicator */
  background: linear-gradient(90deg, transparent 0%, #3b82f6 50%, transparent 100%); /* Blue gradient */
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-content.has-scroll::before {
  opacity: 1;
  animation: scrollPulse 2s infinite; /* Pulsing animation */
}

/* Prominent Scroll Fade Effect */
.modal-content::after {
  content: '';
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  height: 30px; /* Taller fade */
  background: linear-gradient(to top, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.7) 40%, transparent 100%);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-content.has-scroll::after {
  opacity: 1;
}

/* Explicit Scroll Indicator */
.modal-content.has-scroll::before {
  content: '⟱ Scroll for more ⟱';
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1e40af;
  font-size: 12px;
  font-weight: 600;
  height: 24px;
  background: linear-gradient(90deg, rgba(219, 234, 254, 0.7) 0%, rgba(191, 219, 254, 0.9) 50%, rgba(219, 234, 254, 0.7) 100%);
  border-bottom: 1px solid #bfdbfe;
  text-shadow: 0 1px 0 white;
  padding-top: 4px;
}

/* Status Card */
.status-card {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #3b82f6;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.processing {
  background: #3b82f6;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Removed duplicate status-text definition - using the one above with proper active subscription overrides */

/* Plan Info Card */
.plan-info-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
}

.plan-info-card h3 {
  margin: 0 0 16px;
  color: #1e293b;
  font-size: 18px;
  font-weight: 700;
}

.plan-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.detail-row span {
  color: #64748b;
  font-weight: 500;
}

.detail-row strong {
  color: #1e293b;
  font-weight: 700;
}

.detail-row.status .status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #dcfce7;
  color: #166534;
  padding: 6px 12px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
}

/* Instruction Card */
.instruction-card {
  background: linear-gradient(135deg, #fefce8 0%, #fef3c7 100%);
  border: 1px solid #f59e0b;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
}

.instruction-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  color: #92400e;
  font-weight: 700;
  font-size: 16px;
}

.phone-number {
  background: rgba(245, 158, 11, 0.2);
  color: #92400e;
  padding: 12px 16px;
  border-radius: 12px;
  text-align: center;
  font-weight: 700;
  font-size: 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.instruction-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.step {
  color: #78350f;
  font-size: 14px;
  font-weight: 500;
  padding-left: 8px;
  position: relative;
}

.step::before {
  content: '•';
  color: #f59e0b;
  font-weight: bold;
  position: absolute;
  left: -8px;
}

/* Try Again Card */
.try-again-card {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border: 1px solid #ef4444;
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  margin-bottom: 24px;
}

.try-again-card p {
  margin: 0 0 16px;
  color: #dc2626;
  font-weight: 600;
}

.try-again-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
  transition: all 0.2s ease;
}

.try-again-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Success Modal Specific */
.countdown-card {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #3b82f6;
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.countdown-icon {
  color: #3b82f6;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.countdown-card p {
  margin: 0;
  color: #1e40af;
  font-weight: 600;
}

.plan-summary-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
}

.plan-summary-card h3 {
  margin: 0 0 20px;
  color: #1e293b;
  font-size: 18px;
  font-weight: 700;
}

.features-card {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 1px solid #22c55e;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
}

.features-card h3 {
  margin: 0 0 20px;
  color: #166534;
  font-size: 18px;
  font-weight: 700;
  text-align: center;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.8);
  padding: 12px 16px;
  border-radius: 12px;
  color: #166534;
  font-weight: 600;
  font-size: 14px;
}

.feature-item svg {
  color: #22c55e;
  flex-shrink: 0;
}

/* Modal Actions */
.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
  margin-top: 8px;
}

.primary-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  padding: 16px 24px;
  border-radius: 12px;
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  min-width: 180px;
  justify-content: center;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.secondary-btn {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 16px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.secondary-btn:hover {
  background: #f1f5f9;
  color: #475569;
  transform: translateY(-1px);
}

/* Optimal Responsive Design for Best User Experience */
@media (max-width: 768px) {
  .payment-modal-overlay {
    padding: 16px;
    backdrop-filter: blur(3px); /* Less blur on mobile for better background visibility */
    -webkit-backdrop-filter: blur(3px);
  }

  .payment-modal-container {
    max-width: calc(100vw - 32px);
    margin: 16px;
    max-height: calc(100vh - 32px);
  }

  .modal-header {
    padding: 24px 20px 20px;
  }

  .modal-content {
    padding: 20px;
    max-height: calc(100vh - 160px) !important; /* Enhanced mobile height */
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scroll-behavior: smooth !important;
    -webkit-overflow-scrolling: touch !important; /* iOS smooth scrolling */
  }

  /* Enhanced Mobile Scrollbar - Highly Visible */
  .modal-content::-webkit-scrollbar {
    width: 8px !important; /* Wider for mobile */
  }

  .modal-content::-webkit-scrollbar-track {
    background: #e2e8f0 !important;
    border-radius: 4px !important;
  }

  .modal-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%) !important; /* Blue gradient */
    border-radius: 4px !important;
    border: 1px solid #bfdbfe !important;
  }

  .modal-header h2 {
    font-size: 20px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .modal-actions {
    flex-direction: column;
  }

  .primary-btn,
  .secondary-btn {
    width: 100%;
  }
}

/* Small Mobile Optimization */
@media (max-width: 480px) {
  .payment-modal-overlay {
    padding: 12px;
    backdrop-filter: blur(2px); /* Minimal blur on small screens */
    -webkit-backdrop-filter: blur(2px);
    background: rgba(15, 23, 42, 0.25); /* Even more transparent */
  }

  .payment-modal-container {
    max-width: calc(100vw - 24px);
    margin: 12px;
    max-height: calc(100vh - 24px);
    border-radius: 16px;
  }

  .modal-header {
    padding: 20px 16px 16px;
  }

  .modal-content {
    padding: 16px;
    max-height: calc(100vh - 140px) !important; /* Maximum height for small screens */
    overflow-y: auto !important;
    overflow-x: hidden !important;
    scroll-behavior: smooth !important;
    -webkit-overflow-scrolling: touch !important;
  }

  /* Small Mobile Scrollbar - Still Visible */
  .modal-content::-webkit-scrollbar {
    width: 6px !important; /* Wider for visibility */
  }

  .modal-content::-webkit-scrollbar-track {
    background: #e2e8f0 !important;
    border-radius: 3px !important;
  }

  .modal-content::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%) !important; /* Blue gradient */
    border-radius: 3px !important;
    border: 1px solid #bfdbfe !important;
  }

  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* Enhanced Subscription Status Styles */

/* Pulse animation for expired subscription */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 12px 35px rgba(239, 68, 68, 0.5);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
  }
}

/* Green status indicator styles */
.subscription-status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.subscription-status-indicator.active {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.subscription-status-indicator.expired {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  animation: pulse 2s infinite;
}
