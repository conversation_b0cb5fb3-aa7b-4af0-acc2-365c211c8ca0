const axios = require('axios');

async function testLocalRoute() {
  try {
    console.log('🧪 Testing Local Route...\n');
    
    // Test basic server health
    console.log('1️⃣ Testing server health...');
    try {
      const healthResponse = await axios.get('http://localhost:5000');
      console.log('✅ Server is running:', healthResponse.data);
    } catch (error) {
      console.log('❌ Server health check failed:', error.message);
      return;
    }
    
    // Test the specific route without auth (should fail with 401, not 404)
    console.log('\n2️⃣ Testing wipe-level-data route...');
    try {
      const routeResponse = await axios.post('http://localhost:5000/api/users/wipe-level-data', {
        userId: '687bf2b5a4a0362c2cdfa017',
        oldLevel: 'primary',
        newLevel: 'secondary'
      });
      console.log('❌ Should require authentication');
    } catch (error) {
      if (error.response) {
        if (error.response.status === 401) {
          console.log('✅ Route exists but requires authentication (correct)');
          console.log('   Status:', error.response.status);
          console.log('   Message:', error.response.data?.message || 'Unauthorized');
        } else if (error.response.status === 404) {
          console.log('❌ Route not found (404) - this is the problem');
          console.log('   Response:', error.response.data);
        } else {
          console.log('⚠️ Unexpected status:', error.response.status);
          console.log('   Response:', error.response.data);
        }
      } else {
        console.log('❌ Network error:', error.message);
      }
    }
    
    // Test other user routes to see if they work
    console.log('\n3️⃣ Testing other user routes...');
    try {
      const loginResponse = await axios.post('http://localhost:5000/api/users/login', {
        username: 'test',
        password: 'test'
      });
      console.log('⚠️ Login test (expected to fail)');
    } catch (error) {
      if (error.response && error.response.status === 400) {
        console.log('✅ Login route exists (returns 400 for invalid credentials)');
      } else if (error.response && error.response.status === 404) {
        console.log('❌ Login route not found - routes not loading properly');
      } else {
        console.log('✅ Login route exists, status:', error.response?.status);
      }
    }
    
    console.log('\n🔍 Diagnosis:');
    if (error.response?.status === 404) {
      console.log('❌ Routes are not being loaded properly');
      console.log('💡 Possible solutions:');
      console.log('   1. Check if usersRoute is properly exported');
      console.log('   2. Verify server.js is loading routes correctly');
      console.log('   3. Restart server with proper environment');
      console.log('   4. Check for syntax errors in route file');
    } else {
      console.log('✅ Routes are loading, authentication is working');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testLocalRoute();
