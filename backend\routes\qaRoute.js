const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const axios = require('axios');
const { extractTextFromPDF, extractQuestionFromText, getAllQuestionsFromText } = require('../utils/pdfExtractor');

// Simple AI chat function for QA
async function chatWithAI(messages, options = {}) {
  try {
    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-3.5-turbo',
        messages: messages,
        max_tokens: 1000,
        temperature: 0.7
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data.choices[0].message.content;
  } catch (error) {
    console.error('❌ OpenAI API Error:', error.message);
    throw new Error('Failed to generate AI response');
  }
}

/**
 * Get question by number from PDF
 * POST /api/qa/question/:number
 */
router.post('/question/:number', async (req, res) => {
  try {
    const { number } = req.params;
    const { pdfPath, pdfContent, language = 'english' } = req.body;

    console.log(`🔍 Processing question ${number} request:`, {
      pdfPath: pdfPath ? 'provided' : 'not provided',
      pdfContent: pdfContent ? `${pdfContent.length} characters` : 'not provided',
      language
    });

    let pdfText = '';

    // Extract text from PDF
    if (pdfContent) {
      // Use provided PDF content
      pdfText = pdfContent;
      console.log('📄 Using provided PDF content');
    } else if (pdfPath) {
      // Extract from file path
      const fullPath = path.resolve(pdfPath);
      pdfText = await extractTextFromPDF(fullPath);
      console.log('📄 Extracted text from PDF file');
    } else {
      return res.status(400).json({
        success: false,
        error: 'Either pdfPath or pdfContent must be provided'
      });
    }

    // Extract the specific question
    const questionData = extractQuestionFromText(pdfText, number);

    if (!questionData.found) {
      return res.status(404).json({
        success: false,
        error: questionData.error || `Question ${number} not found in the document`,
        questionNumber: number
      });
    }

    // Prepare AI prompt based on language
    const isKiswahili = language === 'kiswahili';
    const systemPrompt = isKiswahili
      ? `Wewe ni msaidizi wa masomo wa Tanzania. Jibu swali hili kwa kutumia maarifa ya kielimu. Toa jibu kamili na la kina kwa lugha ya Kiswahili rahisi.`
      : `You are an educational assistant for Tanzanian students. Answer this question using academic knowledge. Provide a complete and detailed answer.`;

    const userPrompt = isKiswahili
      ? `Jibu swali hili: ${questionData.questionText}`
      : `Answer this question: ${questionData.questionText}`;

    // Get AI response
    const aiResponse = await chatWithAI([
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ], { language: isKiswahili ? 'kiswahili' : 'english' });

    return res.json({
      success: true,
      questionNumber: number,
      question: questionData.questionText,
      answer: aiResponse,
      extractedAt: questionData.extractedAt,
      language: language
    });

  } catch (error) {
    console.error('❌ QA Route Error:', error);
    return res.status(500).json({
      success: false,
      error: 'Server error while processing question',
      details: error.message
    });
  }
});

/**
 * Get all questions from PDF
 * POST /api/qa/questions/all
 */
router.post('/questions/all', async (req, res) => {
  try {
    const { pdfPath, pdfContent } = req.body;

    console.log('📋 Getting all questions from PDF:', {
      pdfPath: pdfPath ? 'provided' : 'not provided',
      pdfContent: pdfContent ? `${pdfContent.length} characters` : 'not provided'
    });

    let pdfText = '';

    // Extract text from PDF
    if (pdfContent) {
      pdfText = pdfContent;
    } else if (pdfPath) {
      const fullPath = path.resolve(pdfPath);
      pdfText = await extractTextFromPDF(fullPath);
    } else {
      return res.status(400).json({
        success: false,
        error: 'Either pdfPath or pdfContent must be provided'
      });
    }

    // Get all questions
    const questions = getAllQuestionsFromText(pdfText);

    return res.json({
      success: true,
      questionsFound: questions.length,
      questions: questions,
      extractedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Get All Questions Error:', error);
    return res.status(500).json({
      success: false,
      error: 'Server error while extracting questions',
      details: error.message
    });
  }
});

/**
 * Extract text from PDF file
 * POST /api/qa/extract-text
 */
router.post('/extract-text', async (req, res) => {
  try {
    const { pdfPath } = req.body;

    if (!pdfPath) {
      return res.status(400).json({
        success: false,
        error: 'PDF path is required'
      });
    }

    console.log('📄 Extracting text from PDF:', pdfPath);

    const fullPath = path.resolve(pdfPath);
    const pdfText = await extractTextFromPDF(fullPath);

    return res.json({
      success: true,
      textLength: pdfText.length,
      text: pdfText,
      extractedAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Text Extraction Error:', error);
    return res.status(500).json({
      success: false,
      error: 'Server error while extracting text',
      details: error.message
    });
  }
});

/**
 * Health check for QA service
 * GET /api/qa/health
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    service: 'QA Service',
    status: 'operational',
    timestamp: new Date().toISOString(),
    features: [
      'PDF text extraction',
      'Question extraction by number',
      'AI-powered question answering',
      'Kiswahili language support'
    ]
  });
});

module.exports = router;
