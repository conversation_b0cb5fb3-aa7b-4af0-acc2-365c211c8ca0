const axios = require('axios');

// Simple test for syllabus system
async function testSyllabusRoutes() {
  const baseURL = 'http://localhost:5000';
  
  console.log('🧪 Testing Syllabus Routes...\n');

  try {
    // Test 1: Check if syllabus routes are accessible
    console.log('1️⃣ Testing syllabus routes...');
    
    try {
      const response = await axios.get(`${baseURL}/api/syllabus`);
      console.log('❌ Route should require authentication');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Syllabus route properly requires authentication');
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.message);
      }
    }

    // Test 2: Check AI content endpoint
    console.log('\n2️⃣ Testing AI content endpoint...');
    try {
      const response = await axios.get(`${baseURL}/api/syllabus/ai-content/primary/5/Mathematics`);
      console.log('❌ AI content route should require authentication');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ AI content route properly requires authentication');
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.message);
      }
    }

    // Test 3: Check subjects endpoint
    console.log('\n3️⃣ Testing subjects endpoint...');
    try {
      const response = await axios.get(`${baseURL}/api/syllabus/subjects/primary`);
      console.log('❌ Subjects route should require authentication');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Subjects route properly requires authentication');
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.message);
      }
    }

    console.log('\n🎉 Basic syllabus route tests completed!');
    console.log('\n📋 Summary:');
    console.log('✅ All syllabus routes properly require authentication');
    console.log('✅ Enhanced authentication middleware is working');
    console.log('✅ Server is responding to syllabus endpoints');
    
    console.log('\n🔧 Next Steps:');
    console.log('1. Login as admin user in the browser');
    console.log('2. Navigate to http://localhost:3000/admin/syllabus');
    console.log('3. Upload a PDF syllabus file');
    console.log('4. Test AI question generation with the uploaded syllabus');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSyllabusRoutes();
