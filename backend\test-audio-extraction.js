const ffmpeg = require("fluent-ffmpeg");
const ffmpegStatic = require("ffmpeg-static");
const axios = require("axios");
const fs = require("fs");
const path = require("path");
const os = require("os");
const { v4: uuidv4 } = require("uuid");

// Configure FFmpeg
ffmpeg.setFfmpegPath(ffmpegStatic);

async function testAudioExtraction() {
  console.log('🧪 Testing audio extraction...');
  
  try {
    // Test 1: Check FFmpeg capabilities
    console.log('1️⃣ Testing FFmpeg capabilities...');
    console.log('FFmpeg path:', ffmpegStatic);
    
    // Test 2: Download a small test video
    console.log('2️⃣ Downloading test video...');
    const testVideoUrl = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
    
    const response = await axios.get(testVideoUrl, { 
      responseType: 'arraybuffer',
      timeout: 30000,
      maxContentLength: 50 * 1024 * 1024 // 50MB limit for testing
    });
    
    const videoBuffer = Buffer.from(response.data);
    console.log('✅ Downloaded video, size:', videoBuffer.length, 'bytes');
    
    // Test 3: Extract audio using different codecs
    const tempDir = os.tmpdir();
    const tempVideoPath = path.join(tempDir, `test_video_${uuidv4()}.mp4`);
    const audioPathWav = path.join(tempDir, `test_audio_${uuidv4()}.wav`);
    const audioPathMp3 = path.join(tempDir, `test_audio_${uuidv4()}.mp3`);
    
    // Write video to temp file
    fs.writeFileSync(tempVideoPath, videoBuffer);
    console.log('✅ Saved video to temp file');
    
    // Test WAV extraction (PCM codec)
    console.log('3️⃣ Testing WAV extraction with PCM codec...');
    try {
      await new Promise((resolve, reject) => {
        ffmpeg(tempVideoPath)
          .audioCodec('pcm_s16le')
          .audioFrequency(16000)
          .audioChannels(1)
          .format('wav')
          .output(audioPathWav)
          .on('end', () => {
            console.log('✅ WAV extraction successful');
            const stats = fs.statSync(audioPathWav);
            console.log('   Audio file size:', stats.size, 'bytes');
            resolve();
          })
          .on('error', (error) => {
            console.error('❌ WAV extraction failed:', error.message);
            reject(error);
          })
          .run();
      });
    } catch (wavError) {
      console.error('❌ WAV extraction error:', wavError.message);
    }
    
    // Test MP3 extraction (if available)
    console.log('4️⃣ Testing MP3 extraction...');
    try {
      await new Promise((resolve, reject) => {
        ffmpeg(tempVideoPath)
          .audioCodec('mp3')
          .audioFrequency(16000)
          .audioChannels(1)
          .audioBitrate('64k')
          .format('mp3')
          .output(audioPathMp3)
          .on('end', () => {
            console.log('✅ MP3 extraction successful');
            const stats = fs.statSync(audioPathMp3);
            console.log('   Audio file size:', stats.size, 'bytes');
            resolve();
          })
          .on('error', (error) => {
            console.error('❌ MP3 extraction failed:', error.message);
            reject(error);
          })
          .run();
      });
    } catch (mp3Error) {
      console.error('❌ MP3 extraction error:', mp3Error.message);
    }
    
    // Test 5: List available codecs
    console.log('5️⃣ Checking available codecs...');
    try {
      await new Promise((resolve, reject) => {
        ffmpeg()
          .getAvailableCodecs((err, codecs) => {
            if (err) {
              reject(err);
              return;
            }
            
            console.log('Available audio codecs:');
            Object.keys(codecs).forEach(codec => {
              if (codecs[codec].type === 'audio') {
                console.log(`   - ${codec}: ${codecs[codec].description}`);
              }
            });
            resolve();
          });
      });
    } catch (codecError) {
      console.error('❌ Could not list codecs:', codecError.message);
    }
    
    // Cleanup
    console.log('🧹 Cleaning up...');
    try {
      fs.unlinkSync(tempVideoPath);
      if (fs.existsSync(audioPathWav)) fs.unlinkSync(audioPathWav);
      if (fs.existsSync(audioPathMp3)) fs.unlinkSync(audioPathMp3);
    } catch (cleanupError) {
      console.warn('⚠️ Cleanup warning:', cleanupError.message);
    }
    
    console.log('✅ Audio extraction test completed');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testAudioExtraction();
