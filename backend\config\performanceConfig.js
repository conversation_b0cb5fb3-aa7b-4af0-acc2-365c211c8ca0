// Performance Configuration for Production
const mongoose = require('mongoose');

// MongoDB Performance Optimization
const mongooseConfig = {
  // Connection pool settings
  maxPoolSize: process.env.NODE_ENV === 'production' ? 10 : 5,
  minPoolSize: process.env.NODE_ENV === 'production' ? 2 : 1,
  
  // Timeout settings
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 30000,
  
  // Buffer settings
  bufferMaxEntries: 0,
  bufferCommands: false,
  
  // Performance settings
  maxIdleTimeMS: 30000,
  heartbeatFrequencyMS: 10000,
  
  // Compression
  compressors: 'zlib',
  zlibCompressionLevel: 6,
  
  // Read preferences for better performance
  readPreference: 'secondaryPreferred',
  readConcern: { level: 'majority' },
  writeConcern: { w: 'majority', j: true, wtimeout: 10000 }
};

// Database indexing strategy
const ensureIndexes = async () => {
  try {
    console.log('🔍 Ensuring database indexes for performance...');
    
    // User model indexes
    const User = require('../models/userModel');
    await User.collection.createIndex({ email: 1 }, { unique: true, background: true });
    await User.collection.createIndex({ username: 1 }, { unique: true, background: true });
    await User.collection.createIndex({ phone: 1 }, { background: true });
    await User.collection.createIndex({ isAdmin: 1 }, { background: true });
    await User.collection.createIndex({ createdAt: -1 }, { background: true });
    
    // Exam model indexes (if exists)
    try {
      const Exam = require('../models/examModel');
      await Exam.collection.createIndex({ name: 1 }, { background: true });
      await Exam.collection.createIndex({ category: 1 }, { background: true });
      await Exam.collection.createIndex({ createdAt: -1 }, { background: true });
      await Exam.collection.createIndex({ 'questions.subject': 1 }, { background: true });
    } catch (err) {
      console.log('⚠️ Exam model not found, skipping exam indexes');
    }
    
    // Quiz model indexes (if exists)
    try {
      const Quiz = require('../models/quizModel');
      await Quiz.collection.createIndex({ userId: 1 }, { background: true });
      await Quiz.collection.createIndex({ examId: 1 }, { background: true });
      await Quiz.collection.createIndex({ createdAt: -1 }, { background: true });
      await Quiz.collection.createIndex({ score: -1 }, { background: true });
    } catch (err) {
      console.log('⚠️ Quiz model not found, skipping quiz indexes');
    }
    
    // Video model indexes (if exists)
    try {
      const Video = require('../models/videoModel');
      await Video.collection.createIndex({ title: 'text', description: 'text' }, { background: true });
      await Video.collection.createIndex({ category: 1 }, { background: true });
      await Video.collection.createIndex({ level: 1 }, { background: true });
      await Video.collection.createIndex({ createdAt: -1 }, { background: true });
    } catch (err) {
      console.log('⚠️ Video model not found, skipping video indexes');
    }
    
    console.log('✅ Database indexes ensured successfully');
  } catch (error) {
    console.error('❌ Error ensuring database indexes:', error.message);
  }
};

// Memory cache configuration
const cacheConfig = {
  // In-memory cache for frequently accessed data
  userCache: new Map(),
  examCache: new Map(),
  videoCache: new Map(),
  
  // Cache TTL (Time To Live) in milliseconds
  userCacheTTL: 5 * 60 * 1000, // 5 minutes
  examCacheTTL: 15 * 60 * 1000, // 15 minutes
  videoCacheTTL: 30 * 60 * 1000, // 30 minutes
  
  // Cache size limits
  maxCacheSize: 1000,
  
  // Cache cleanup interval
  cleanupInterval: 10 * 60 * 1000 // 10 minutes
};

// Simple cache implementation
class SimpleCache {
  constructor(ttl = 300000, maxSize = 1000) {
    this.cache = new Map();
    this.ttl = ttl;
    this.maxSize = maxSize;
    
    // Cleanup expired entries every 5 minutes
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }
  
  set(key, value) {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }
  
  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    // Check if item has expired
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }
  
  delete(key) {
    this.cache.delete(key);
  }
  
  clear() {
    this.cache.clear();
  }
  
  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > this.ttl) {
        this.cache.delete(key);
      }
    }
  }
  
  size() {
    return this.cache.size;
  }
}

// Create cache instances
const userCache = new SimpleCache(cacheConfig.userCacheTTL, cacheConfig.maxCacheSize);
const examCache = new SimpleCache(cacheConfig.examCacheTTL, cacheConfig.maxCacheSize);
const videoCache = new SimpleCache(cacheConfig.videoCacheTTL, cacheConfig.maxCacheSize);

// Performance monitoring
const performanceMonitor = {
  requestCount: 0,
  slowQueries: [],
  errorCount: 0,
  
  logSlowQuery: (query, duration) => {
    if (duration > 1000) { // Log queries slower than 1 second
      performanceMonitor.slowQueries.push({
        query,
        duration,
        timestamp: new Date().toISOString()
      });
      
      // Keep only last 100 slow queries
      if (performanceMonitor.slowQueries.length > 100) {
        performanceMonitor.slowQueries.shift();
      }
    }
  },
  
  incrementRequest: () => {
    performanceMonitor.requestCount++;
  },
  
  incrementError: () => {
    performanceMonitor.errorCount++;
  },
  
  getStats: () => ({
    requestCount: performanceMonitor.requestCount,
    slowQueriesCount: performanceMonitor.slowQueries.length,
    errorCount: performanceMonitor.errorCount,
    cacheStats: {
      userCacheSize: userCache.size(),
      examCacheSize: examCache.size(),
      videoCacheSize: videoCache.size()
    }
  })
};

// Mongoose query optimization middleware
const optimizeQueries = () => {
  // Add query performance monitoring
  mongoose.plugin((schema) => {
    schema.pre(/^find/, function() {
      this.start = Date.now();
    });
    
    schema.post(/^find/, function() {
      if (this.start) {
        const duration = Date.now() - this.start;
        performanceMonitor.logSlowQuery(this.getQuery(), duration);
      }
    });
  });
};

module.exports = {
  mongooseConfig,
  ensureIndexes,
  cacheConfig,
  SimpleCache,
  userCache,
  examCache,
  videoCache,
  performanceMonitor,
  optimizeQueries
};
