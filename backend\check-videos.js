const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
mongoose.connect(process.env.MONGO_URL, {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Video model (simplified)
const videoSchema = new mongoose.Schema({
  className: String,
  subject: String,
  title: String,
  level: String,
  videoID: String,
  videoUrl: String,
  thumbnail: String,
  additionalClasses: [String],
  subtitles: Array,
  hasSubtitles: Boolean,
  subtitleGenerationStatus: String
});

const Videos = mongoose.model('videos', videoSchema);

async function checkVideos() {
  try {
    console.log('🔍 Checking videos in database...');
    
    const totalVideos = await Videos.countDocuments();
    console.log(`📊 Total videos in database: ${totalVideos}`);
    
    if (totalVideos > 0) {
      console.log('\n📹 Sample videos:');
      const sampleVideos = await Videos.find().limit(5);
      sampleVideos.forEach((video, index) => {
        console.log(`${index + 1}. ${video.title}`);
        console.log(`   Level: ${video.level}`);
        console.log(`   Class: ${video.className}`);
        console.log(`   Subject: ${video.subject}`);
        console.log('');
      });
      
      console.log('\n📊 Videos by level:');
      const primaryVideos = await Videos.countDocuments({ level: 'primary' });
      const secondaryVideos = await Videos.countDocuments({ level: 'secondary' });
      const advanceVideos = await Videos.countDocuments({ level: 'advance' });
      
      console.log(`Primary: ${primaryVideos}`);
      console.log(`Secondary: ${secondaryVideos}`);
      console.log(`Advance: ${advanceVideos}`);
    } else {
      console.log('❌ No videos found in database');
    }
    
  } catch (error) {
    console.error('❌ Error checking videos:', error);
  } finally {
    mongoose.connection.close();
  }
}

checkVideos();
