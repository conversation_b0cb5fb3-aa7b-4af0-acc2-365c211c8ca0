const mongoose = require("mongoose");
require("dotenv").config();

async function testStandaloneApproval() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log("✅ Connected to MongoDB");

    const { AIQuestionGeneration } = require("./models/aiQuestionGenerationModel");
    const Exam = require("./models/examModel");
    const Question = require("./models/questionModel");

    // Find a standalone generation with questions
    const standaloneGeneration = await AIQuestionGeneration.findOne({
      examId: null,
      generationStatus: "completed",
      "generatedQuestions.0": { $exists: true }
    });

    if (!standaloneGeneration) {
      console.log("❌ No standalone generation with questions found");
      return;
    }

    console.log(`📋 Testing approval for generation: ${standaloneGeneration._id}`);
    console.log(`   - Questions: ${standaloneGeneration.generatedQuestions.length}`);
    console.log(`   - Level: ${standaloneGeneration.generationParams.level}`);
    console.log(`   - Class: ${standaloneGeneration.generationParams.class}`);
    console.log(`   - Subjects: ${standaloneGeneration.generationParams.subjects.join(", ")}`);

    // Count exams before
    const examsBefore = await Exam.countDocuments();
    console.log(`📊 Exams before approval: ${examsBefore}`);

    // Simulate the approval process by calling the createAutoExam method directly
    const aiService = require("./services/aiQuestionGenerationService");
    const aiServiceInstance = new aiService();

    console.log("🔧 Creating auto exam for standalone generation...");
    const newExamId = await aiServiceInstance.createAutoExam(
      standaloneGeneration.generationParams,
      standaloneGeneration.requestedBy
    );

    console.log(`✅ Created exam with ID: ${newExamId}`);

    // Update the generation record
    standaloneGeneration.examId = newExamId;
    await standaloneGeneration.save();
    console.log("✅ Updated generation record with new examId");

    // Count exams after
    const examsAfter = await Exam.countDocuments();
    console.log(`📊 Exams after approval: ${examsAfter}`);

    // Verify the new exam
    const newExam = await Exam.findById(newExamId);
    if (newExam) {
      console.log(`📝 New exam details:`);
      console.log(`   - Name: ${newExam.name}`);
      console.log(`   - Level: ${newExam.level}`);
      console.log(`   - Class: ${newExam.class}`);
      console.log(`   - Subject: ${newExam.subject}`);
      console.log(`   - Duration: ${newExam.duration} minutes`);
      console.log(`   - Total Marks: ${newExam.totalMarks}`);
      console.log(`   - Passing Marks: ${newExam.passingMarks}`);
      console.log(`   - Questions: ${newExam.questions.length}`);
    }

    // Now test the actual approval process
    console.log("\n🧪 Testing question approval process...");
    
    // Simulate approving the first question
    const questionToApprove = standaloneGeneration.generatedQuestions[0];
    if (questionToApprove && questionToApprove.generatedContent) {
      const questionData = {
        ...questionToApprove.generatedContent,
        exam: newExamId,
      };

      console.log("📝 Creating question from approved content...");
      const newQuestion = new Question(questionData);
      const savedQuestion = await newQuestion.save();
      
      // Add to exam
      newExam.questions.push(savedQuestion._id);
      await newExam.save();
      
      console.log(`✅ Question approved and added to exam: ${savedQuestion._id}`);
      console.log(`📊 Exam now has ${newExam.questions.length} questions`);
    }

    await mongoose.disconnect();
    console.log("\n✅ Test completed successfully");

  } catch (error) {
    console.error("❌ Test failed:", error);
    await mongoose.disconnect();
  }
}

testStandaloneApproval();
