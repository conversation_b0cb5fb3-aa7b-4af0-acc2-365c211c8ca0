const mongoose = require("mongoose");

const videoCommentSchema = new mongoose.Schema({
  videoId: {
    type: String,
    required: true,
    index: true
  },
  text: {
    type: String,
    required: true,
    trim: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "users",
    required: true
  },
  author: {
    type: String,
    required: true
  },
  avatar: {
    type: String,
    required: true
  },
  userLevel: {
    type: String,
    enum: ["primary", "secondary", "advance"],
    default: "primary"
  },
  userClass: {
    type: String
  },
  likes: {
    type: Number,
    default: 0
  },
  likedBy: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: "users"
  }],
  replies: [{
    text: {
      type: String,
      required: true,
      trim: true
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
      required: true
    },
    author: {
      type: String,
      required: true
    },
    avatar: {
      type: String,
      required: true
    },
    likes: {
      type: Number,
      default: 0
    },
    likedBy: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: "users"
    }],
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  isAI: {
    type: Boolean,
    default: false
  },
  aiAuthor: {
    type: String,
    default: null
  }
}, {
  timestamps: true
});

// Index for efficient queries
videoCommentSchema.index({ videoId: 1, createdAt: -1 });
videoCommentSchema.index({ user: 1 });

const VideoComment = mongoose.model("video-comments", videoCommentSchema);
module.exports = VideoComment;
