const mongoose = require("mongoose");

const xpTransactionSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
      required: true,
    },
    
    // Transaction Details
    xpAmount: {
      type: Number,
      required: true,
    },
    transactionType: {
      type: String,
      enum: ["quiz_completion", "achievement_unlock", "daily_bonus", "streak_bonus", "level_bonus", "admin_adjustment", "penalty"],
      required: true,
    },
    
    // Source Information
    sourceId: {
      type: mongoose.Schema.Types.ObjectId,
      refPath: 'sourceModel',
    },
    sourceModel: {
      type: String,
      enum: ['exams', 'achievements', 'users'],
    },
    
    // XP Breakdown
    breakdown: {
      baseXP: {
        type: Number,
        default: 0,
      },
      difficultyBonus: {
        type: Number,
        default: 0,
      },
      speedBonus: {
        type: Number,
        default: 0,
      },
      perfectScoreBonus: {
        type: Number,
        default: 0,
      },
      streakBonus: {
        type: Number,
        default: 0,
      },
      levelMultiplier: {
        type: Number,
        default: 1.0,
      },
      firstAttemptBonus: {
        type: Number,
        default: 0,
      },
    },
    
    // Context Information
    quizData: {
      examId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "exams",
      },
      subject: String,
      difficulty: String,
      questionsTotal: Number,
      questionsCorrect: Number,
      timeSpent: Number,
      score: Number,
      isFirstAttempt: Boolean,
    },
    
    // Achievement Data (if applicable)
    achievementData: {
      achievementId: String,
      achievementName: String,
      rarity: String,
      category: String,
    },
    
    // User State at Transaction
    userStateAtTransaction: {
      levelBefore: Number,
      levelAfter: Number,
      xpBefore: Number,
      xpAfter: Number,
      streakBefore: Number,
      streakAfter: Number,
    },
    
    // Season Information
    season: {
      type: String,
      default: "2024-S1",
    },
    
    // Additional Metadata
    metadata: {
      type: Object,
      default: {},
    },
    
    // Admin Notes (for manual adjustments)
    adminNotes: String,
    processedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "users",
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
xpTransactionSchema.index({ user: 1, createdAt: -1 });
xpTransactionSchema.index({ transactionType: 1, createdAt: -1 });
xpTransactionSchema.index({ season: 1, user: 1 });
xpTransactionSchema.index({ "quizData.subject": 1, user: 1 });

// Virtual for formatted XP amount
xpTransactionSchema.virtual('formattedXP').get(function() {
  return this.xpAmount >= 0 ? `+${this.xpAmount}` : `${this.xpAmount}`;
});

// Method to get transaction summary
xpTransactionSchema.methods.getSummary = function() {
  return {
    id: this._id,
    xp: this.xpAmount,
    type: this.transactionType,
    date: this.createdAt,
    source: this.sourceModel,
    breakdown: this.breakdown,
  };
};

// Static method to get user's XP history
xpTransactionSchema.statics.getUserXPHistory = function(userId, limit = 50) {
  return this.find({ user: userId })
    .sort({ createdAt: -1 })
    .limit(limit)
    .populate('sourceId')
    .lean();
};

// Static method to get XP leaderboard for a season
xpTransactionSchema.statics.getSeasonLeaderboard = function(season, limit = 100) {
  return this.aggregate([
    { $match: { season: season } },
    {
      $group: {
        _id: '$user',
        totalSeasonXP: { $sum: '$xpAmount' },
        transactionCount: { $sum: 1 },
        lastActivity: { $max: '$createdAt' },
      }
    },
    { $sort: { totalSeasonXP: -1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'userInfo'
      }
    },
    { $unwind: '$userInfo' },
    {
      $project: {
        userId: '$_id',
        userName: '$userInfo.name',
        userClass: '$userInfo.class',
        userLevel: '$userInfo.currentLevel',
        totalSeasonXP: 1,
        transactionCount: 1,
        lastActivity: 1,
      }
    }
  ]);
};

const XPTransaction = mongoose.model("xp_transactions", xpTransactionSchema);

module.exports = XPTransaction;
