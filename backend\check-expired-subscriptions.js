const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');

async function checkExpiredSubscriptions() {
  try {
    await mongoose.connect('mongodb://hvmgeeks:<EMAIL>:27017,ac-4ojvv6y-shard-00-01.cdg8fdn.mongodb.net:27017,ac-4ojvv6y-shard-00-02.cdg8fdn.mongodb.net:27017/stjoseph?ssl=true&replicaSet=atlas-fsgg6f-shard-0&authSource=admin&retryWrites=true&w=majority&appName=Cluster0');
    
    console.log('🔍 Checking for expired subscriptions...\n');
    
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];
    
    console.log(`📅 Today's Date: ${todayString}`);
    console.log(`📅 Current Time: ${today.toISOString()}\n`);
    
    // Find all users with active subscription status
    const activeUsers = await User.find({ subscriptionStatus: 'active' });
    
    console.log(`👥 Found ${activeUsers.length} users with active subscription status\n`);
    
    const expiredUsers = [];
    const validUsers = [];
    const noEndDateUsers = [];
    
    for (const user of activeUsers) {
      console.log(`\n👤 Checking ${user.firstName} ${user.lastName} (@${user.username})`);
      
      // Get user's subscription details
      const subscriptions = await Subscription.find({ user: user._id })
        .populate('activePlan', 'title duration discountedPrice')
        .sort({ createdAt: -1 });
      
      if (subscriptions.length === 0) {
        console.log(`   ⚠️ No subscriptions found - should be free`);
        expiredUsers.push({
          user,
          reason: 'NO_SUBSCRIPTION',
          endDate: null,
          daysExpired: null
        });
        continue;
      }
      
      // Find the most recent active subscription
      const activeSubscription = subscriptions.find(sub => sub.status === 'active') || subscriptions[0];
      
      console.log(`   📋 Plan: ${activeSubscription.activePlan?.title || 'Unknown'}`);
      console.log(`   📅 Subscription End Date: ${activeSubscription.endDate || 'Not Set'}`);
      console.log(`   📅 User End Date: ${user.subscriptionEndDate ? user.subscriptionEndDate.toISOString().split('T')[0] : 'Not Set'}`);
      
      // Use subscription end date or user end date
      let endDate = null;
      if (activeSubscription.endDate) {
        endDate = new Date(activeSubscription.endDate);
      } else if (user.subscriptionEndDate) {
        endDate = new Date(user.subscriptionEndDate);
      }
      
      if (!endDate) {
        console.log(`   ⚠️ No end date found - needs manual review`);
        noEndDateUsers.push({
          user,
          subscription: activeSubscription,
          reason: 'NO_END_DATE'
        });
        continue;
      }
      
      // Check if subscription has expired
      const timeDiff = today.getTime() - endDate.getTime();
      const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      
      if (daysDiff > 0) {
        console.log(`   ❌ EXPIRED ${daysDiff} days ago (${endDate.toISOString().split('T')[0]})`);
        expiredUsers.push({
          user,
          subscription: activeSubscription,
          endDate: endDate,
          daysExpired: daysDiff,
          reason: 'EXPIRED'
        });
      } else {
        const daysRemaining = Math.abs(daysDiff);
        console.log(`   ✅ Valid - expires in ${daysRemaining} days (${endDate.toISOString().split('T')[0]})`);
        validUsers.push({
          user,
          subscription: activeSubscription,
          endDate: endDate,
          daysRemaining: daysRemaining
        });
      }
    }
    
    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 SUBSCRIPTION EXPIRY ANALYSIS');
    console.log('='.repeat(60));
    
    console.log(`\n✅ Valid Subscriptions: ${validUsers.length}`);
    console.log(`❌ Expired Subscriptions: ${expiredUsers.length}`);
    console.log(`⚠️ No End Date: ${noEndDateUsers.length}`);
    
    if (expiredUsers.length > 0) {
      console.log(`\n❌ EXPIRED SUBSCRIPTIONS (${expiredUsers.length} users):`);
      expiredUsers.forEach((userData, index) => {
        const { user, endDate, daysExpired, reason } = userData;
        if (reason === 'EXPIRED') {
          console.log(`   ${index + 1}. ${user.firstName} ${user.lastName} (@${user.username}) - Expired ${daysExpired} days ago (${endDate.toISOString().split('T')[0]})`);
        } else {
          console.log(`   ${index + 1}. ${user.firstName} ${user.lastName} (@${user.username}) - ${reason}`);
        }
      });
    }
    
    if (noEndDateUsers.length > 0) {
      console.log(`\n⚠️ NO END DATE (${noEndDateUsers.length} users):`);
      noEndDateUsers.forEach((userData, index) => {
        const { user, subscription } = userData;
        console.log(`   ${index + 1}. ${user.firstName} ${user.lastName} (@${user.username}) - ${subscription.activePlan?.title || 'Unknown Plan'}`);
      });
    }
    
    if (validUsers.length > 0) {
      console.log(`\n✅ VALID SUBSCRIPTIONS (${validUsers.length} users):`);
      // Show only first 10 to avoid clutter
      const displayUsers = validUsers.slice(0, 10);
      displayUsers.forEach((userData, index) => {
        const { user, endDate, daysRemaining } = userData;
        console.log(`   ${index + 1}. ${user.firstName} ${user.lastName} (@${user.username}) - ${daysRemaining} days remaining (expires ${endDate.toISOString().split('T')[0]})`);
      });
      if (validUsers.length > 10) {
        console.log(`   ... and ${validUsers.length - 10} more valid subscriptions`);
      }
    }
    
    // Ask if user wants to update expired subscriptions
    console.log('\n🔧 NEXT STEPS:');
    if (expiredUsers.length > 0) {
      console.log(`❌ ${expiredUsers.length} users need their status updated to 'expired' or 'free'`);
      console.log('💡 Run the update script to fix these expired subscriptions');
    }
    
    if (noEndDateUsers.length > 0) {
      console.log(`⚠️ ${noEndDateUsers.length} users need end dates calculated based on their plan duration`);
      console.log('💡 These users may need manual review or automatic end date calculation');
    }
    
    if (expiredUsers.length === 0 && noEndDateUsers.length === 0) {
      console.log('🎉 All active users have valid, non-expired subscriptions!');
    }
    
    return {
      expired: expiredUsers,
      valid: validUsers,
      noEndDate: noEndDateUsers
    };
    
  } catch (error) {
    console.error('❌ Error checking expired subscriptions:', error.message);
    return null;
  } finally {
    mongoose.disconnect();
    console.log('\n🔌 Disconnected from MongoDB');
  }
}

checkExpiredSubscriptions();
