const mongoose = require('mongoose');
require('dotenv').config();
const xpRankingService = require('./services/xpRankingService');
const User = require('./models/userModel');

async function testRankingWithUser() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log('Connected to MongoDB');
    
    // Find our test user
    const user = await User.findById('6793d2ac3213409e03cd5a83');
    if (user) {
      console.log('Test user found:', user.name, 'XP:', user.totalXP);
      
      // Get user ranking position
      const ranking = await xpRankingService.getUserRankingPosition(user._id, 5);
      if (ranking.success) {
        console.log('User rank:', ranking.userRank, 'out of', ranking.totalUsers);
        console.log('Nearby users:');
        ranking.nearbyUsers.forEach((u, i) => {
          const rank = ranking.context.showingFrom + i;
          const marker = u._id.toString() === user._id.toString() ? ' <-- YOU' : '';
          console.log(rank + '. ' + u.name + ': ' + u.totalXP + ' XP' + marker);
        });
      } else {
        console.log('Error getting user ranking:', ranking.message);
      }
    } else {
      console.log('Test user not found');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

testRankingWithUser();
