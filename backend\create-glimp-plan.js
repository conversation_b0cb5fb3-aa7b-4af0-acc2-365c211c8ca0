const mongoose = require('mongoose');

// Simple plan schema
const planSchema = new mongoose.Schema({
  title: String,
  features: [String],
  actualPrice: Number,
  discountedPrice: Number,
  discountPercentage: Number,
  duration: Number,
  status: Boolean,
}, { timestamps: true });

const Plan = mongoose.model('Plan', planSchema);

async function createGlimpPlan() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb+srv://henrymushi:<EMAIL>/brainwave');
    console.log('✅ Connected to MongoDB');
    
    // Check if Glimp plan exists
    const existingPlan = await Plan.findOne({ title: 'Glimp Plan' });
    if (existingPlan) {
      console.log('⚠️ Glimp Plan already exists');
      process.exit(0);
    }
    
    // Create Glimp plan
    const glimpPlan = new Plan({
      title: 'Glimp Plan',
      features: [
        '1-month full access',
        'Unlimited quizzes',
        'Personalized profile',
        'AI chat for instant help',
        'Forum for student discussions',
        'Study notes',
        'Past papers',
        'Books',
        'Learning videos',
        'Track progress with rankings'
      ],
      actualPrice: 15000,
      discountedPrice: 13000,
      discountPercentage: 13,
      duration: 1,
      status: true,
    });
    
    await glimpPlan.save();
    console.log('✅ Glimp Plan created successfully!');
    
    // List all plans
    const allPlans = await Plan.find({});
    console.log('\n📋 All plans:');
    allPlans.forEach(plan => {
      console.log(`- ${plan.title}: ${plan.duration} month(s), ${plan.discountedPrice} TZS`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

createGlimpPlan();
