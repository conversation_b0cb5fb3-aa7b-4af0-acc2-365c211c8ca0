const axios = require('axios');

const testAPIConnection = async () => {
  console.log('🔍 Testing API Connection...');
  
  try {
    // Test health endpoint
    console.log('⏳ Testing health endpoint...');
    const healthResponse = await axios.get('http://localhost:5000/api/health');
    console.log('✅ Health endpoint response:', healthResponse.data);
    
    // Test database endpoint
    console.log('⏳ Testing database endpoint...');
    const dbResponse = await axios.get('http://localhost:5000/api/test/db');
    console.log('✅ Database endpoint response:', dbResponse.data);
    
    // Test a user endpoint (if available)
    console.log('⏳ Testing user endpoint...');
    try {
      const userResponse = await axios.post('http://localhost:5000/api/users/get-all-users');
      console.log('✅ Users endpoint working, user count:', userResponse.data?.data?.length || 0);
    } catch (userError) {
      console.log('⚠️ Users endpoint requires authentication (expected)');
    }
    
    console.log('✅ All API tests completed successfully!');
    
  } catch (error) {
    console.error('❌ API connection failed:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('💡 Server is not running on port 5000');
    }
  }
};

testAPIConnection();
