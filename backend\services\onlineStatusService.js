const User = require("../models/userModel");

class OnlineStatusService {
  
  // Set user as online
  static async setUserOnline(userId) {
    try {
      await User.findByIdAndUpdate(userId, {
        isOnline: true,
        lastActivity: new Date(),
        lastSeen: new Date()
      });
      
      console.log(`✅ User ${userId} is now online`);
      
    } catch (error) {
      console.error('Error setting user online:', error);
    }
  }
  
  // Set user as offline
  static async setUserOffline(userId) {
    try {
      await User.findByIdAndUpdate(userId, {
        isOnline: false,
        lastSeen: new Date()
      });
      
      console.log(`✅ User ${userId} is now offline`);
      
    } catch (error) {
      console.error('Error setting user offline:', error);
    }
  }
  
  // Update user activity (heartbeat)
  static async updateUserActivity(userId) {
    try {
      await User.findByIdAndUpdate(userId, {
        lastActivity: new Date(),
        isOnline: true
      });
      
    } catch (error) {
      console.error('Error updating user activity:', error);
    }
  }
  
  // Get online users
  static async getOnlineUsers(options = {}) {
    try {
      const {
        limit = 50,
        excludeUserId = null
      } = options;
      
      const query = {
        isOnline: true
      };
      
      if (excludeUserId) {
        query._id = { $ne: excludeUserId };
      }
      
      const users = await User.find(query)
        .select('name profilePicture level class lastActivity')
        .sort({ lastActivity: -1 })
        .limit(limit);
      
      return users;
      
    } catch (error) {
      console.error('Error getting online users:', error);
      throw error;
    }
  }
  
  // Check if user is online
  static async isUserOnline(userId) {
    try {
      const user = await User.findById(userId).select('isOnline lastActivity');
      
      if (!user) {
        return false;
      }
      
      // Consider user offline if no activity for more than 5 minutes
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      
      if (user.lastActivity < fiveMinutesAgo) {
        // Auto-set as offline
        await this.setUserOffline(userId);
        return false;
      }
      
      return user.isOnline;
      
    } catch (error) {
      console.error('Error checking user online status:', error);
      return false;
    }
  }
  
  // Cleanup offline users (run periodically)
  static async cleanupOfflineUsers() {
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      
      const result = await User.updateMany(
        {
          isOnline: true,
          lastActivity: { $lt: fiveMinutesAgo }
        },
        {
          isOnline: false,
          lastSeen: new Date()
        }
      );
      
      if (result.modifiedCount > 0) {
        console.log(`✅ Set ${result.modifiedCount} inactive users as offline`);
      }
      
      return result;
      
    } catch (error) {
      console.error('Error cleaning up offline users:', error);
    }
  }
  
  // Get user's online status with last seen
  static async getUserStatus(userId) {
    try {
      const user = await User.findById(userId).select('isOnline lastSeen lastActivity name');
      
      if (!user) {
        return null;
      }
      
      // Check if really online
      const isReallyOnline = await this.isUserOnline(userId);
      
      return {
        userId: user._id,
        name: user.name,
        isOnline: isReallyOnline,
        lastSeen: user.lastSeen,
        lastActivity: user.lastActivity
      };
      
    } catch (error) {
      console.error('Error getting user status:', error);
      return null;
    }
  }
  
  // Get online count
  static async getOnlineCount() {
    try {
      // First cleanup inactive users
      await this.cleanupOfflineUsers();
      
      const count = await User.countDocuments({ isOnline: true });
      return count;
      
    } catch (error) {
      console.error('Error getting online count:', error);
      return 0;
    }
  }
}

module.exports = OnlineStatusService;
