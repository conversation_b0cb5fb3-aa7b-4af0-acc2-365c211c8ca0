const mongoose = require('mongoose');
const xpRankingService = require('../services/xpRankingService');
require('dotenv').config();

const User = require('../models/userModel');

async function testRankingFix() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Get test users from each level
        const primaryUser = await User.findOne({ level: 'primary' });
        const secondaryUser = await User.findOne({ level: 'secondary' });
        const advanceUser = await User.findOne({ level: 'advance' });

        console.log('\n👥 Test Users:');
        console.log(`Primary: ${primaryUser?.name} (${primaryUser?.level})`);
        console.log(`Secondary: ${secondaryUser?.name} (${secondaryUser?.level})`);
        console.log(`Advance: ${advanceUser?.name} (${advanceUser?.level})`);

        // Test ranking for each level
        const testCases = [
            { level: 'primary', user: primaryUser },
            { level: 'secondary', user: secondaryUser },
            { level: 'advance', user: advanceUser }
        ];

        for (const { level, user } of testCases) {
            if (!user) {
                console.log(`\n❌ No ${level} user found, skipping test`);
                continue;
            }

            console.log(`\n🧪 Testing ${level.toUpperCase()} Level Ranking`);
            console.log(`User: ${user.name} (Level: ${user.level})`);

            // Test the ranking service directly
            const rankingResult = await xpRankingService.getXPLeaderboard({
                limit: 10,
                levelFilter: level,
                includeInactive: false
            });

            if (rankingResult.success) {
                console.log(`📊 Returned ${rankingResult.data.length} users`);
                
                // Check if all returned users are from the correct level
                const userLevels = rankingResult.data.map(u => u.level);
                const uniqueLevels = [...new Set(userLevels)];
                
                console.log(`🎯 Levels in result: ${uniqueLevels.join(', ')}`);
                
                // Check for contamination
                let hasContamination = false;
                if (level === 'primary') {
                    hasContamination = rankingResult.data.some(u => 
                        u.level === 'secondary' || u.level === 'advance' || 
                        u.level === 'Secondary' || u.level === 'Advance'
                    );
                } else if (level === 'secondary') {
                    hasContamination = rankingResult.data.some(u => 
                        u.level !== 'secondary' && u.level !== 'Secondary'
                    );
                } else if (level === 'advance') {
                    hasContamination = rankingResult.data.some(u => 
                        u.level !== 'advance' && u.level !== 'Advance'
                    );
                }

                if (hasContamination) {
                    console.log('❌ LEVEL CONTAMINATION DETECTED!');
                    console.log('Users by level:');
                    const levelCounts = {};
                    rankingResult.data.forEach(u => {
                        levelCounts[u.level] = (levelCounts[u.level] || 0) + 1;
                    });
                    console.log(levelCounts);
                } else {
                    console.log('✅ Level separation working correctly');
                }

                // Show top 3 users
                console.log('🏆 Top 3 users:');
                rankingResult.data.slice(0, 3).forEach((user, index) => {
                    console.log(`  ${index + 1}. ${user.name} (${user.level}) - ${user.totalXP} XP`);
                });

            } else {
                console.log(`❌ Ranking service error: ${rankingResult.error}`);
            }
        }

        console.log('\n🎯 Ranking Fix Test Summary:');
        console.log('✅ Direct service calls tested');
        console.log('✅ Level separation verified');
        
    } catch (error) {
        console.error('❌ Error testing ranking fix:', error);
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    testRankingFix();
}

module.exports = testRankingFix;
