const mongoose = require('mongoose');
require('dotenv').config();

// Video model
const videoSchema = new mongoose.Schema({
  className: String,
  subject: String,
  title: String,
  videoID: String,
  videoUrl: String,
  thumbnail: String,
  level: String,
});

const Video = mongoose.model('studyvideos', videoSchema);

const testVideoData = async () => {
  try {
    console.log('🔗 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB');

    // Check if there are any videos
    const videoCount = await Video.countDocuments();
    console.log(`📹 Total videos in database: ${videoCount}`);

    if (videoCount > 0) {
      // Get first few videos
      const sampleVideos = await Video.find().limit(3);
      console.log('📹 Sample videos:');
      sampleVideos.forEach((video, index) => {
        console.log(`  ${index + 1}. ${video.title}`);
        console.log(`     - Subject: ${video.subject}`);
        console.log(`     - Class: ${video.className}`);
        console.log(`     - Level: ${video.level}`);
        console.log(`     - Thumbnail: ${video.thumbnail || 'NO THUMBNAIL'}`);
        console.log(`     - VideoID: ${video.videoID}`);
        console.log(`     - VideoURL: ${video.videoUrl || 'NO VIDEO URL'}`);
        console.log('');
      });

      // Check how many videos have thumbnails
      const videosWithThumbnails = await Video.countDocuments({ thumbnail: { $exists: true, $ne: null, $ne: '' } });
      console.log(`🖼️ Videos with thumbnails: ${videosWithThumbnails}/${videoCount}`);
    } else {
      console.log('❌ No videos found in database');
      
      // Let's create a sample video for testing
      console.log('🔧 Creating sample video...');
      const sampleVideo = new Video({
        className: '1',
        subject: 'Mathematics',
        title: 'Basic Addition',
        videoID: 'sample123',
        videoUrl: 'https://example.com/video.mp4',
        thumbnail: 'https://example.com/thumbnail.jpg',
        level: 'primary'
      });
      
      await sampleVideo.save();
      console.log('✅ Sample video created');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
};

testVideoData();
