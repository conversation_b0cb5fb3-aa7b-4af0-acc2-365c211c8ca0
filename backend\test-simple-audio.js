const ffmpeg = require("fluent-ffmpeg");
const ffmpegStatic = require("ffmpeg-static");
const fs = require("fs");
const path = require("path");
const os = require("os");
const { v4: uuidv4 } = require("uuid");

// Configure FFmpeg
ffmpeg.setFfmpegPath(ffmpegStatic);

async function testSimpleAudioExtraction() {
  console.log('🧪 Testing simple audio extraction...');
  
  try {
    // Create a simple test video file (just copy an existing one from our S3)
    const tempDir = os.tmpdir();
    const testVideoPath = path.join(tempDir, `test_video_${uuidv4()}.mp4`);
    const audioPathWav = path.join(tempDir, `test_audio_${uuidv4()}.wav`);
    const audioPathMp3 = path.join(tempDir, `test_audio_${uuidv4()}.mp3`);
    
    // Create a minimal MP4 file for testing (just headers - won't work for real extraction but tests the command)
    console.log('1️⃣ Creating test video file...');
    const mp4Header = Buffer.from([
      0x00, 0x00, 0x00, 0x20, 0x66, 0x74, 0x79, 0x70, // ftyp box
      0x69, 0x73, 0x6F, 0x6D, 0x00, 0x00, 0x02, 0x00,
      0x69, 0x73, 0x6F, 0x6D, 0x69, 0x73, 0x6F, 0x32,
      0x61, 0x76, 0x63, 0x31, 0x6D, 0x70, 0x34, 0x31
    ]);
    fs.writeFileSync(testVideoPath, mp4Header);
    console.log('✅ Test video file created');
    
    // Test 1: Try WAV extraction with PCM codec (our current approach)
    console.log('2️⃣ Testing WAV extraction with PCM codec...');
    try {
      await new Promise((resolve, reject) => {
        const command = ffmpeg(testVideoPath)
          .audioCodec('pcm_s16le')
          .audioFrequency(16000)
          .audioChannels(1)
          .format('wav')
          .output(audioPathWav)
          .on('start', (commandLine) => {
            console.log('   Command:', commandLine);
          })
          .on('end', () => {
            console.log('✅ WAV extraction completed');
            resolve();
          })
          .on('error', (error) => {
            console.error('❌ WAV extraction failed:', error.message);
            reject(error);
          });
        
        command.run();
      });
    } catch (wavError) {
      console.error('❌ WAV extraction error details:', wavError.message);
    }
    
    // Test 2: Try MP3 extraction (original approach)
    console.log('3️⃣ Testing MP3 extraction...');
    try {
      await new Promise((resolve, reject) => {
        const command = ffmpeg(testVideoPath)
          .audioCodec('mp3')
          .audioFrequency(16000)
          .audioChannels(1)
          .audioBitrate('64k')
          .format('mp3')
          .output(audioPathMp3)
          .on('start', (commandLine) => {
            console.log('   Command:', commandLine);
          })
          .on('end', () => {
            console.log('✅ MP3 extraction completed');
            resolve();
          })
          .on('error', (error) => {
            console.error('❌ MP3 extraction failed:', error.message);
            reject(error);
          });
        
        command.run();
      });
    } catch (mp3Error) {
      console.error('❌ MP3 extraction error details:', mp3Error.message);
    }
    
    // Test 3: Try with libmp3lame encoder explicitly
    console.log('4️⃣ Testing MP3 extraction with libmp3lame...');
    try {
      const audioPathMp3Lame = path.join(tempDir, `test_audio_lame_${uuidv4()}.mp3`);
      await new Promise((resolve, reject) => {
        const command = ffmpeg(testVideoPath)
          .audioCodec('libmp3lame')
          .audioFrequency(16000)
          .audioChannels(1)
          .audioBitrate('64k')
          .format('mp3')
          .output(audioPathMp3Lame)
          .on('start', (commandLine) => {
            console.log('   Command:', commandLine);
          })
          .on('end', () => {
            console.log('✅ MP3 (libmp3lame) extraction completed');
            resolve();
          })
          .on('error', (error) => {
            console.error('❌ MP3 (libmp3lame) extraction failed:', error.message);
            reject(error);
          });
        
        command.run();
      });
    } catch (lameError) {
      console.error('❌ MP3 (libmp3lame) extraction error details:', lameError.message);
    }
    
    // Cleanup
    console.log('🧹 Cleaning up...');
    try {
      if (fs.existsSync(testVideoPath)) fs.unlinkSync(testVideoPath);
      if (fs.existsSync(audioPathWav)) fs.unlinkSync(audioPathWav);
      if (fs.existsSync(audioPathMp3)) fs.unlinkSync(audioPathMp3);
    } catch (cleanupError) {
      console.warn('⚠️ Cleanup warning:', cleanupError.message);
    }
    
    console.log('✅ Simple audio extraction test completed');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSimpleAudioExtraction();
