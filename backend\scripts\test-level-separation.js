const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('../models/userModel');
const Exam = require('../models/examModel');
const ForumQuestion = require('../models/forumQuestionModel');

async function testLevelSeparation() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Test 1: Check if users exist for each level
        console.log('\n📊 Test 1: User distribution by level');
        const usersByLevel = await User.aggregate([
            { $group: { _id: "$level", count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
        ]);
        console.log('Users by level:', usersByLevel);

        // Test 2: Test forum question filtering for each level
        console.log('\n📊 Test 2: Forum question filtering');
        
        // Get sample users from each level
        const primaryUser = await User.findOne({ level: "primary" });
        const secondaryUser = await User.findOne({ level: "secondary" });
        const advanceUser = await User.findOne({ level: "advance" });

        if (primaryUser) {
            const primaryQuestions = await ForumQuestion.find({
                level: { $nin: ["secondary", "advance", "Secondary", "Advance"] }
            });
            console.log(`Primary user sees ${primaryQuestions.length} questions`);
        }

        if (secondaryUser) {
            const secondaryQuestions = await ForumQuestion.find({
                $or: [{ level: "secondary" }, { level: "Secondary" }]
            });
            console.log(`Secondary user sees ${secondaryQuestions.length} questions`);
        }

        if (advanceUser) {
            const advanceQuestions = await ForumQuestion.find({
                $or: [{ level: "advance" }, { level: "Advance" }]
            });
            console.log(`Advance user sees ${advanceQuestions.length} questions`);
        }

        // Test 3: Test exam filtering for each level
        console.log('\n📊 Test 3: Exam filtering');
        
        if (primaryUser) {
            const primaryExams = await Exam.find({
                level: { $nin: ["secondary", "advance", "Secondary", "Advance"] }
            });
            console.log(`Primary user sees ${primaryExams.length} exams`);
        }

        if (secondaryUser) {
            const secondaryExams = await Exam.find({
                $or: [{ level: "secondary" }, { level: "Secondary" }]
            });
            console.log(`Secondary user sees ${secondaryExams.length} exams`);
        }

        if (advanceUser) {
            const advanceExams = await Exam.find({
                $or: [{ level: "advance" }, { level: "Advance" }]
            });
            console.log(`Advance user sees ${advanceExams.length} exams`);
        }

        // Test 4: Check for any mixed level content
        console.log('\n📊 Test 4: Cross-level contamination check');
        
        const allLevels = await ForumQuestion.distinct("level");
        console.log('All forum question levels in database:', allLevels);
        
        const examLevels = await Exam.distinct("level");
        console.log('All exam levels in database:', examLevels);

        // Test 5: Simulate ranking separation
        console.log('\n📊 Test 5: Ranking separation simulation');
        
        if (primaryUser) {
            const primaryRanking = await User.find({
                level: { $nin: ["secondary", "advance", "Secondary", "Advance"] }
            }).select('name level totalXP').sort({ totalXP: -1 }).limit(5);
            console.log('Top 5 primary users:', primaryRanking.map(u => ({ name: u.name, level: u.level, xp: u.totalXP })));
        }

        if (secondaryUser) {
            const secondaryRanking = await User.find({
                $or: [{ level: "secondary" }, { level: "Secondary" }]
            }).select('name level totalXP').sort({ totalXP: -1 }).limit(5);
            console.log('Top 5 secondary users:', secondaryRanking.map(u => ({ name: u.name, level: u.level, xp: u.totalXP })));
        }

        if (advanceUser) {
            const advanceRanking = await User.find({
                $or: [{ level: "advance" }, { level: "Advance" }]
            }).select('name level totalXP').sort({ totalXP: -1 }).limit(5);
            console.log('Top 5 advance users:', advanceRanking.map(u => ({ name: u.name, level: u.level, xp: u.totalXP })));
        }

        console.log('\n✅ Level separation test completed!');
        
    } catch (error) {
        console.error('❌ Error testing level separation:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    testLevelSeparation();
}

module.exports = testLevelSeparation;
