const { 
  primarySyllabus, 
  secondarySyllabus, 
  advanceSyllabus, 
  questionGenerationGuidelines 
} = require("../data/tanzaniaSyllabus");

class QuestionValidationService {
  constructor() {
    this.validationRules = {
      minQuestionLength: 10,
      maxQuestionLength: 500,
      minOptionLength: 1,
      maxOptionLength: 200,
      requiredOptions: ["A", "B", "C", "D"],
      maxSyllabusTopics: 5,
    };
  }

  // Main validation method
  validateQuestion(question, level, className, subject) {
    const validationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      score: 100,
    };

    // Basic structure validation
    this.validateBasicStructure(question, validationResult);
    
    // Content validation
    this.validateContent(question, validationResult);
    
    // Answer type specific validation
    this.validateAnswerType(question, validationResult);
    
    // Tanzania syllabus compliance
    this.validateSyllabusCompliance(question, level, className, subject, validationResult);
    
    // Educational quality validation
    this.validateEducationalQuality(question, level, validationResult);
    
    // Calculate final score
    this.calculateValidationScore(validationResult);

    return validationResult;
  }

  // Validate basic question structure
  validateBasicStructure(question, result) {
    // Check if question text exists
    if (!question.name || typeof question.name !== "string") {
      result.errors.push("Question text is required and must be a string");
      result.isValid = false;
    } else {
      // Check question length
      if (question.name.length < this.validationRules.minQuestionLength) {
        result.warnings.push(`Question text is too short (minimum ${this.validationRules.minQuestionLength} characters)`);
      }
      if (question.name.length > this.validationRules.maxQuestionLength) {
        result.errors.push(`Question text is too long (maximum ${this.validationRules.maxQuestionLength} characters)`);
        result.isValid = false;
      }
    }

    // Check answer type
    if (!question.answerType) {
      result.errors.push("Answer type is required");
      result.isValid = false;
    }

    // Check if question type is specified for AI-generated questions
    if (question.isAIGenerated && !question.questionType) {
      result.warnings.push("Question type should be specified for AI-generated questions");
    }
  }

  // Validate question content
  validateContent(question, result) {
    const questionText = question.name.toLowerCase();

    // Check for inappropriate content
    const inappropriateWords = ["violence", "weapon", "drug", "alcohol", "gambling"];
    inappropriateWords.forEach(word => {
      if (questionText.includes(word)) {
        result.errors.push(`Question contains inappropriate content: ${word}`);
        result.isValid = false;
      }
    });

    // Check for educational language
    const educationalIndicators = ["what", "how", "why", "which", "calculate", "identify", "explain", "describe"];
    const hasEducationalLanguage = educationalIndicators.some(indicator => 
      questionText.includes(indicator)
    );
    
    if (!hasEducationalLanguage) {
      result.warnings.push("Question may benefit from more educational language patterns");
    }

    // Check for clear question structure
    if (!questionText.includes("?") && !questionText.includes("____")) {
      result.warnings.push("Question should end with a question mark or contain fill-in-the-blank indicators");
    }
  }

  // Validate answer type specific requirements
  validateAnswerType(question, result) {
    switch (question.answerType) {
      case "Options":
        this.validateMultipleChoice(question, result);
        break;
      case "Free Text":
        this.validateFreeText(question, result);
        break;
      case "Fill in the Blank":
        this.validateFillInTheBlank(question, result);
        break;
      default:
        result.errors.push(`Unknown answer type: ${question.answerType}`);
        result.isValid = false;
    }
  }

  // Validate multiple choice questions
  validateMultipleChoice(question, result) {
    // Check if options exist
    if (!question.options || typeof question.options !== "object") {
      result.errors.push("Multiple choice questions must have options");
      result.isValid = false;
      return;
    }

    // Check if all required options are present
    const missingOptions = this.validationRules.requiredOptions.filter(
      option => !question.options[option]
    );
    if (missingOptions.length > 0) {
      result.errors.push(`Missing options: ${missingOptions.join(", ")}`);
      result.isValid = false;
    }

    // Validate option lengths
    Object.entries(question.options).forEach(([key, value]) => {
      if (typeof value !== "string") {
        result.errors.push(`Option ${key} must be a string`);
        result.isValid = false;
      } else {
        if (value.length < this.validationRules.minOptionLength) {
          result.warnings.push(`Option ${key} is too short`);
        }
        if (value.length > this.validationRules.maxOptionLength) {
          result.errors.push(`Option ${key} is too long`);
          result.isValid = false;
        }
      }
    });

    // Check correct option
    if (!question.correctOption) {
      result.errors.push("Correct option is required for multiple choice questions");
      result.isValid = false;
    } else if (!question.options[question.correctOption]) {
      result.errors.push("Correct option must match one of the available options");
      result.isValid = false;
    }

    // Check for duplicate options
    const optionValues = Object.values(question.options);
    const uniqueOptions = [...new Set(optionValues)];
    if (optionValues.length !== uniqueOptions.length) {
      result.warnings.push("Some options appear to be duplicates");
    }

    // Check option quality
    this.validateOptionQuality(question.options, question.correctOption, result);
  }

  // Validate free text questions
  validateFreeText(question, result) {
    if (!question.correctAnswer || typeof question.correctAnswer !== "string") {
      result.errors.push("Free text questions must have a correct answer");
      result.isValid = false;
    } else {
      if (question.correctAnswer.length < 1) {
        result.errors.push("Correct answer cannot be empty");
        result.isValid = false;
      }
      if (question.correctAnswer.length > 500) {
        result.warnings.push("Correct answer is very long, consider breaking into smaller parts");
      }
    }
  }

  // Validate fill in the blank questions
  validateFillInTheBlank(question, result) {
    // Check for blank indicators
    if (!question.name.includes("____") && !question.name.includes("_____")) {
      result.errors.push("Fill in the blank questions must contain blank indicators (______)");
      result.isValid = false;
    }

    // Check correct answer
    if (!question.correctAnswer || typeof question.correctAnswer !== "string") {
      result.errors.push("Fill in the blank questions must have a correct answer");
      result.isValid = false;
    } else {
      // Check if answer is reasonable length
      if (question.correctAnswer.length > 100) {
        result.warnings.push("Fill in the blank answers should typically be short words or phrases");
      }
    }
  }

  // Validate option quality for multiple choice
  validateOptionQuality(options, correctOption, result) {
    const optionTexts = Object.values(options);
    const correctText = options[correctOption];

    // Check if options are too similar
    optionTexts.forEach((option, index) => {
      optionTexts.slice(index + 1).forEach(otherOption => {
        const similarity = this.calculateTextSimilarity(option, otherOption);
        if (similarity > 0.8) {
          result.warnings.push("Some options are very similar, which may confuse students");
        }
      });
    });

    // Check if distractors are plausible
    const correctLength = correctText.length;
    const avgDistractorLength = optionTexts
      .filter(opt => opt !== correctText)
      .reduce((sum, opt) => sum + opt.length, 0) / 3;

    if (Math.abs(correctLength - avgDistractorLength) > correctLength * 0.5) {
      result.warnings.push("Correct answer length differs significantly from distractors");
    }
  }

  // Validate Tanzania syllabus compliance
  validateSyllabusCompliance(question, level, className, subject, result) {
    // Get syllabus data
    let syllabusData;
    switch (level) {
      case "primary":
        syllabusData = primarySyllabus;
        break;
      case "secondary":
        syllabusData = secondarySyllabus;
        break;
      case "advance":
        syllabusData = advanceSyllabus;
        break;
      default:
        result.warnings.push("Unknown education level for syllabus validation");
        return;
    }

    // Check if subject exists in syllabus
    if (!syllabusData[subject]) {
      result.warnings.push(`Subject ${subject} not found in ${level} syllabus`);
      return;
    }

    // Validate syllabus topics if provided
    if (question.syllabusTopics && question.syllabusTopics.length > 0) {
      if (question.syllabusTopics.length > this.validationRules.maxSyllabusTopics) {
        result.warnings.push(`Too many syllabus topics (maximum ${this.validationRules.maxSyllabusTopics})`);
      }

      // Check if topics are relevant to the subject
      const subjectData = syllabusData[subject][className];
      if (subjectData && subjectData.topics) {
        const validTopics = subjectData.topics.map(topic => topic.topicName.toLowerCase());
        const invalidTopics = question.syllabusTopics.filter(topic => 
          !validTopics.some(validTopic => validTopic.includes(topic.toLowerCase()))
        );
        
        if (invalidTopics.length > 0) {
          result.warnings.push(`Some topics may not align with syllabus: ${invalidTopics.join(", ")}`);
        }
      }
    }
  }

  // Validate educational quality
  validateEducationalQuality(question, level, result) {
    const guidelines = questionGenerationGuidelines[level];
    if (!guidelines) return;

    const questionText = question.name.toLowerCase();

    // Check language complexity
    const complexWords = questionText.split(" ").filter(word => word.length > 12);
    if (level === "primary" && complexWords.length > 2) {
      result.warnings.push("Question may contain words too complex for primary level");
    }

    // Check for Tanzanian context (basic check)
    const tanzanianContextWords = ["tanzania", "swahili", "dar es salaam", "dodoma", "kilimanjaro"];
    const hasLocalContext = tanzanianContextWords.some(word => questionText.includes(word));
    
    if (question.isAIGenerated && !hasLocalContext) {
      result.warnings.push("Consider adding Tanzanian context to make question more relevant");
    }

    // Check difficulty alignment
    if (question.difficultyLevel) {
      const wordCount = questionText.split(" ").length;
      const expectedWordCount = {
        easy: { min: 5, max: 20 },
        medium: { min: 10, max: 30 },
        hard: { min: 15, max: 50 },
      };

      const expected = expectedWordCount[question.difficultyLevel];
      if (expected && (wordCount < expected.min || wordCount > expected.max)) {
        result.warnings.push(`Question length may not match ${question.difficultyLevel} difficulty level`);
      }
    }
  }

  // Calculate validation score
  calculateValidationScore(result) {
    let score = 100;
    
    // Deduct points for errors
    score -= result.errors.length * 20;
    
    // Deduct points for warnings
    score -= result.warnings.length * 5;
    
    // Ensure score doesn't go below 0
    result.score = Math.max(0, score);
    
    // Set quality level
    if (result.score >= 90) {
      result.qualityLevel = "Excellent";
    } else if (result.score >= 75) {
      result.qualityLevel = "Good";
    } else if (result.score >= 60) {
      result.qualityLevel = "Fair";
    } else {
      result.qualityLevel = "Poor";
    }
  }

  // Calculate text similarity (simple implementation)
  calculateTextSimilarity(text1, text2) {
    const words1 = text1.toLowerCase().split(" ");
    const words2 = text2.toLowerCase().split(" ");
    
    const commonWords = words1.filter(word => words2.includes(word));
    const totalWords = new Set([...words1, ...words2]).size;
    
    return commonWords.length / totalWords;
  }

  // Batch validate multiple questions
  validateQuestions(questions, level, className, subject) {
    return questions.map(question => 
      this.validateQuestion(question, level, className, subject)
    );
  }
}

module.exports = QuestionValidationService;
