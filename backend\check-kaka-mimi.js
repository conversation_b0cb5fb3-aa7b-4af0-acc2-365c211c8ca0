const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const Plan = require('./models/planModel');

async function checkKakaMimi() {
  try {
    await mongoose.connect('mongodb://hvmgeeks:<EMAIL>:27017,ac-4ojvv6y-shard-00-01.cdg8fdn.mongodb.net:27017,ac-4ojvv6y-shard-00-02.cdg8fdn.mongodb.net:27017/stjoseph?ssl=true&replicaSet=atlas-fsgg6f-shard-0&authSource=admin&retryWrites=true&w=majority&appName=Cluster0');
    
    console.log('🔍 Searching for user kaka.mimi...');
    
    // Find user by username
    const user = await User.findOne({ username: 'kaka.mimi' });
    
    if (!user) {
      console.log('❌ User kaka.mimi not found');
      return;
    }
    
    console.log('👤 User found:');
    console.log('   Name:', user.firstName, user.lastName);
    console.log('   Email:', user.email);
    console.log('   Username:', user.username);
    console.log('   Phone:', user.phoneNumber);
    console.log('   Subscription Status:', user.subscriptionStatus);
    console.log('   Payment Required:', user.paymentRequired);
    console.log('   Subscription End Date:', user.subscriptionEndDate);
    
    // Find all subscriptions for this user
    const subscriptions = await Subscription.find({ user: user._id })
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });
    
    console.log('\n💳 Subscriptions found:', subscriptions.length);
    
    subscriptions.forEach((sub, index) => {
      console.log(`\n📋 Subscription ${index + 1}:`);
      console.log('   ID:', sub._id);
      console.log('   Plan:', sub.activePlan?.title || 'Unknown');
      console.log('   Payment Status:', sub.paymentStatus);
      console.log('   Status:', sub.status);
      console.log('   Start Date:', sub.startDate);
      console.log('   End Date:', sub.endDate);
      console.log('   Created:', sub.createdAt);
      console.log('   Payment History:', sub.paymentHistory.length, 'entries');
      
      if (sub.paymentHistory.length > 0) {
        const latestPayment = sub.paymentHistory[sub.paymentHistory.length - 1];
        console.log('   Latest Payment:');
        console.log('     Order ID:', latestPayment.orderId);
        console.log('     Amount:', latestPayment.amount);
        console.log('     Status:', latestPayment.paymentStatus);
        console.log('     Date:', latestPayment.paymentDate);
        console.log('     Reference:', latestPayment.referenceId);
      }
    });
    
    // Check if there are any paid subscriptions that should be active
    const paidSubscriptions = subscriptions.filter(sub => sub.paymentStatus === 'paid');
    const activeSubscriptions = subscriptions.filter(sub => sub.status === 'active');
    
    console.log('\n📊 Summary:');
    console.log('   Total subscriptions:', subscriptions.length);
    console.log('   Paid subscriptions:', paidSubscriptions.length);
    console.log('   Active subscriptions:', activeSubscriptions.length);
    
    if (paidSubscriptions.length > 0 && user.subscriptionStatus === 'free') {
      console.log('\n⚠️  ISSUE DETECTED: User has paid subscriptions but status is FREE!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    mongoose.disconnect();
  }
}

checkKakaMimi();
