const mongoose = require('mongoose');
require('dotenv').config();

const Exam = require('../models/examModel');

async function fixBiologyExamTopic() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Find the BS1-Q1 exam
        console.log('\n🔍 Looking for BS1-Q1 exam...');
        
        const biologyExam = await Exam.findOne({ name: 'BS1-Q1' });

        if (!biologyExam) {
            console.log('❌ BS1-Q1 exam not found');
            return;
        }

        console.log('📝 Found BS1-Q1 exam:');
        console.log(`  Name: ${biologyExam.name}`);
        console.log(`  Subject: ${biologyExam.subject}`);
        console.log(`  Category: ${biologyExam.category}`);
        console.log(`  Topic: ${biologyExam.topic}`);
        console.log(`  Level: ${biologyExam.level}`);
        console.log(`  Class: ${biologyExam.class}`);

        // Update the exam to add a proper topic
        console.log('\n🔧 Updating exam topic...');
        
        const updateResult = await Exam.findByIdAndUpdate(
            biologyExam._id,
            { 
                topic: 'Cell Biology',  // Set a proper biology topic
                subject: 'Biology'      // Also fix the subject from 'General' to 'Biology'
            },
            { new: true }
        );

        if (updateResult) {
            console.log('✅ Successfully updated BS1-Q1 exam:');
            console.log(`  Name: ${updateResult.name}`);
            console.log(`  Subject: ${updateResult.subject}`);
            console.log(`  Category: ${updateResult.category}`);
            console.log(`  Topic: ${updateResult.topic}`);
            console.log(`  Level: ${updateResult.level}`);
            console.log(`  Class: ${updateResult.class}`);
        } else {
            console.log('❌ Failed to update exam');
        }

        console.log('\n✅ Fix completed!');
        
    } catch (error) {
        console.error('❌ Error fixing biology exam:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    fixBiologyExamTopic();
}

module.exports = fixBiologyExamTopic;
