// Security Middleware for Production
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const compression = require('compression');

// Rate limiting configuration
const createRateLimit = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      error: message,
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      res.status(429).json({
        success: false,
        message: message,
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
  });
};

// General rate limiting
const generalLimiter = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  process.env.NODE_ENV === 'production' ? 100 : 1000, // 100 requests per 15 minutes in production
  'Too many requests from this IP, please try again later.'
);

// Strict rate limiting for authentication endpoints
const authLimiter = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  5, // 5 attempts per 15 minutes
  'Too many authentication attempts, please try again later.'
);

// API rate limiting
const apiLimiter = createRateLimit(
  1 * 60 * 1000, // 1 minute
  process.env.NODE_ENV === 'production' ? 60 : 200, // 60 requests per minute in production
  'API rate limit exceeded, please slow down.'
);

// Payment rate limiting
const paymentLimiter = createRateLimit(
  60 * 60 * 1000, // 1 hour
  10, // 10 payment attempts per hour
  'Too many payment attempts, please try again later.'
);

// Helmet configuration for security headers
const helmetConfig = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: [
        "'self'", 
        "'unsafe-inline'", 
        "https://fonts.googleapis.com",
        "https://cdn.jsdelivr.net"
      ],
      scriptSrc: [
        "'self'", 
        "'unsafe-inline'", 
        "'unsafe-eval'",
        "https://cdn.jsdelivr.net",
        "https://unpkg.com"
      ],
      fontSrc: [
        "'self'", 
        "https://fonts.gstatic.com",
        "https://cdn.jsdelivr.net"
      ],
      imgSrc: [
        "'self'", 
        "data:", 
        "https:",
        "https://brainwavebucket.s3.amazonaws.com",
        "https://brainwavebucket.s3.us-east-1.amazonaws.com"
      ],
      connectSrc: [
        "'self'",
        "https://api.openai.com",
        "https://api.zenoapi.com",
        "https://server-fmff.onrender.com",
        "wss://server-fmff.onrender.com"
      ],
      mediaSrc: [
        "'self'",
        "https://brainwavebucket.s3.amazonaws.com",
        "https://brainwavebucket.s3.us-east-1.amazonaws.com"
      ],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: process.env.NODE_ENV === 'production' ? [] : null
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for video streaming
  crossOriginResourcePolicy: { policy: "cross-origin" }
};

// Compression middleware
const compressionConfig = {
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  },
  level: 6,
  threshold: 1024
};

// Security headers middleware
const securityHeaders = (req, res, next) => {
  // Remove server information
  res.removeHeader('X-Powered-By');
  
  // Add custom security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Add HSTS header for HTTPS
  if (process.env.NODE_ENV === 'production') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  }
  
  next();
};

// Request logging for security monitoring
const securityLogger = (req, res, next) => {
  const timestamp = new Date().toISOString();
  const ip = req.ip || req.connection.remoteAddress;
  const userAgent = req.get('User-Agent') || 'Unknown';
  
  // Log suspicious requests
  if (req.path.includes('..') || 
      req.path.includes('<script>') || 
      req.path.includes('SELECT') ||
      req.path.includes('DROP')) {
    console.warn(`🚨 Suspicious request detected: ${timestamp} - IP: ${ip} - Path: ${req.path} - UA: ${userAgent}`);
  }
  
  next();
};

module.exports = {
  generalLimiter,
  authLimiter,
  apiLimiter,
  paymentLimiter,
  helmet: helmet(helmetConfig),
  compression: compression(compressionConfig),
  securityHeaders,
  securityLogger
};
