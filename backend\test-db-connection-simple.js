const mongoose = require('mongoose');
require('dotenv').config();

console.log('🔍 Testing MongoDB Connection...');
console.log('MongoDB URL:', process.env.MONGO_URL ? 'Configured' : 'NOT CONFIGURED');

const testConnection = async () => {
  try {
    console.log('⏳ Attempting to connect to MongoDB...');
    
    await mongoose.connect(process.env.MONGO_URL, {
      serverSelectionTimeoutMS: 10000,
      socketTimeoutMS: 10000,
      connectTimeoutMS: 10000,
    });
    
    console.log('✅ MongoDB Connection Successful!');
    
    // Test a simple operation
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('📊 Available collections:', collections.map(c => c.name));
    
    // Test user collection specifically
    const User = mongoose.connection.db.collection('users');
    const userCount = await User.countDocuments();
    console.log('👥 Total users in database:', userCount);
    
    await mongoose.disconnect();
    console.log('✅ Database test completed successfully!');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
};

testConnection();
