const http = require('http');

console.log('🧪 Testing server health...');

const options = {
  hostname: 'localhost',
  port: 5000,
  path: '/',
  method: 'GET'
};

const req = http.request(options, (res) => {
  console.log(`✅ Server responded with status: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('✅ Server is running and responding');
    console.log('Response:', data.substring(0, 100) + '...');
  });
});

req.on('error', (e) => {
  console.error(`❌ Server test failed: ${e.message}`);
});

req.setTimeout(5000, () => {
  console.error('❌ Request timeout');
  req.destroy();
});

req.end();
