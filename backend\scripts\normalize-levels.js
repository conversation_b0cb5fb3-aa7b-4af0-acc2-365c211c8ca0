const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('../models/userModel');
const Exam = require('../models/examModel');
const ForumQuestion = require('../models/forumQuestionModel');
const Question = require('../models/questionModel');

async function normalizeLevels() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Normalize User levels - handle all variations including null
        console.log('📊 Normalizing User levels...');

        // First, handle null levels
        const nullUserUpdates = await User.updateMany(
            { $or: [{ level: null }, { level: { $exists: false } }] },
            { $set: { level: "primary" } }
        );
        console.log(`✅ Updated ${nullUserUpdates.modifiedCount} users with null levels to primary`);

        // Then handle capitalized levels
        const userUpdates = await User.updateMany(
            { level: { $in: ["Primary", "Secondary", "Advance"] } },
            [
                {
                    $set: {
                        level: {
                            $switch: {
                                branches: [
                                    { case: { $eq: ["$level", "Primary"] }, then: "primary" },
                                    { case: { $eq: ["$level", "Secondary"] }, then: "secondary" },
                                    { case: { $eq: ["$level", "Advance"] }, then: "advance" }
                                ],
                                default: "$level"
                            }
                        }
                    }
                }
            ]
        );
        console.log(`✅ Updated ${userUpdates.modifiedCount} user records with capitalized levels`);

        // Normalize Exam levels
        console.log('📊 Normalizing Exam levels...');
        const examUpdates = await Exam.updateMany(
            { level: { $in: ["Primary", "Secondary", "Advance"] } },
            [
                {
                    $set: {
                        level: {
                            $switch: {
                                branches: [
                                    { case: { $eq: ["$level", "Primary"] }, then: "primary" },
                                    { case: { $eq: ["$level", "Secondary"] }, then: "secondary" },
                                    { case: { $eq: ["$level", "Advance"] }, then: "advance" }
                                ],
                                default: "$level"
                            }
                        }
                    }
                }
            ]
        );
        console.log(`✅ Updated ${examUpdates.modifiedCount} exam records`);

        // Normalize Forum Question levels
        console.log('📊 Normalizing Forum Question levels...');

        // First, handle null levels
        const nullForumUpdates = await ForumQuestion.updateMany(
            { $or: [{ level: null }, { level: { $exists: false } }] },
            { $set: { level: "primary" } }
        );
        console.log(`✅ Updated ${nullForumUpdates.modifiedCount} forum questions with null levels to primary`);

        // Then handle capitalized levels
        const forumUpdates = await ForumQuestion.updateMany(
            { level: { $in: ["Primary", "Secondary", "Advance"] } },
            [
                {
                    $set: {
                        level: {
                            $switch: {
                                branches: [
                                    { case: { $eq: ["$level", "Primary"] }, then: "primary" },
                                    { case: { $eq: ["$level", "Secondary"] }, then: "secondary" },
                                    { case: { $eq: ["$level", "Advance"] }, then: "advance" }
                                ],
                                default: "$level"
                            }
                        }
                    }
                }
            ]
        );
        console.log(`✅ Updated ${forumUpdates.modifiedCount} forum question records with capitalized levels`);

        // Normalize Question levels
        console.log('📊 Normalizing Question levels...');
        const questionUpdates = await Question.updateMany(
            { level: { $in: ["Primary", "Secondary", "Advance"] } },
            [
                {
                    $set: {
                        level: {
                            $switch: {
                                branches: [
                                    { case: { $eq: ["$level", "Primary"] }, then: "primary" },
                                    { case: { $eq: ["$level", "Secondary"] }, then: "secondary" },
                                    { case: { $eq: ["$level", "Advance"] }, then: "advance" }
                                ],
                                default: "$level"
                            }
                        }
                    }
                }
            ]
        );
        console.log(`✅ Updated ${questionUpdates.modifiedCount} question records`);

        // Verify the changes
        console.log('\n📊 Verification - Current level distribution:');
        
        const userLevels = await User.aggregate([
            { $group: { _id: "$level", count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
        ]);
        console.log('Users by level:', userLevels);

        const examLevels = await Exam.aggregate([
            { $group: { _id: "$level", count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
        ]);
        console.log('Exams by level:', examLevels);

        const forumLevels = await ForumQuestion.aggregate([
            { $group: { _id: "$level", count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
        ]);
        console.log('Forum questions by level:', forumLevels);

        console.log('\n✅ Level normalization completed successfully!');
        
    } catch (error) {
        console.error('❌ Error normalizing levels:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    normalizeLevels();
}

module.exports = normalizeLevels;
