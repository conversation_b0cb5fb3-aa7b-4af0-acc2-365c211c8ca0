const express = require('express');
const router = express.Router();

/**
 * Simple health check for QA service
 * GET /api/qa/health
 */
router.get('/health', (req, res) => {
  console.log('🔍 QA Health check requested');
  res.json({
    success: true,
    service: 'QA Service',
    status: 'operational',
    timestamp: new Date().toISOString(),
    message: 'Simple QA service is working'
  });
});

/**
 * Simple question answering endpoint
 * POST /api/qa/question/:number
 */
router.post('/question/:number', async (req, res) => {
  try {
    const { number } = req.params;
    const { pdfContent, language = 'english' } = req.body;

    console.log(`🔍 Processing question ${number} request`);

    // Simple question extraction (basic pattern matching)
    const questionPattern = new RegExp(`Question\\s*${number}[:\\.]([\\s\\S]*?)(?=Question\\s*\\d+[:\\.]|$)`, 'i');
    const match = pdfContent.match(questionPattern);

    if (!match) {
      return res.status(404).json({
        success: false,
        error: `Question ${number} not found in the document`,
        questionNumber: number
      });
    }

    const questionText = match[1].trim();

    // Simple response without AI for now
    const answer = language === 'kiswahili' 
      ? `Hii ni jibu la swali namba ${number}: ${questionText}`
      : `This is the answer to question ${number}: ${questionText}`;

    return res.json({
      success: true,
      questionNumber: number,
      question: questionText,
      answer: answer,
      extractedAt: new Date().toISOString(),
      language: language
    });

  } catch (error) {
    console.error('❌ QA Route Error:', error);
    return res.status(500).json({
      success: false,
      error: 'Server error while processing question',
      details: error.message
    });
  }
});

module.exports = router;
