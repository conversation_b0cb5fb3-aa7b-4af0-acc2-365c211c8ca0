# Environment Configuration Guide

## Overview

This guide explains how to set up and configure the environment variables for the St. Joseph Kibada Quiz App. The application uses environment variables to manage configuration across different deployment environments (development, staging, production).

## Quick Start

1. **Copy the example file:**
   ```bash
   cp .env.example .env
   ```

2. **Edit the `.env` file** with your actual credentials and configuration values.

3. **Never commit the `.env` file** to version control.

## Configuration Sections

### 🚀 Application Configuration

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Environment mode | `development` | ✅ |
| `APP_NAME` | Application name | `"St. Joseph Ki<PERSON>a Quiz App"` | ✅ |
| `PORT` | Server port | `5000` | ✅ |
| `CLIENT_URL` | Frontend URL | `http://localhost:3000` | ✅ |

### 🗄️ Database Configuration

| Variable | Description | Required |
|----------|-------------|----------|
| `MONGO_URL` | Primary MongoDB connection string | ✅ |
| `MONGO_URL_FALLBACK` | Fallback MongoDB connection | ✅ |
| `DB_CONNECTION_TIMEOUT` | Connection timeout (ms) | ❌ |
| `DB_MAX_POOL_SIZE` | Maximum connection pool size | ❌ |

**MongoDB URL Format:**
```
********************************:port/database?options
```

### 🔐 Authentication & Security

| Variable | Description | Required | Security Level |
|----------|-------------|----------|----------------|
| `JWT_SECRET` | JWT signing secret | ✅ | 🔴 Critical |
| `JWT_EXPIRES_IN` | JWT expiration time | ❌ | 🟡 Medium |
| `SESSION_SECRET` | Session signing secret | ✅ | 🔴 Critical |
| `BCRYPT_SALT_ROUNDS` | Password hashing rounds | ❌ | 🟡 Medium |

**Security Best Practices:**
- Use secrets with minimum 32 characters
- Include uppercase, lowercase, numbers, and symbols
- Rotate secrets regularly
- Never share secrets in plain text

### 📧 Email Configuration

| Variable | Description | Required |
|----------|-------------|----------|
| `SENDER_EMAIL` | Email address for sending | ✅ |
| `SENDER_EMAIL_PASSWORD` | Email app password | ✅ |
| `OWNER_EMAIL` | Admin email address | ✅ |

**Gmail Setup:**
1. Enable 2-factor authentication
2. Generate app-specific password
3. Use app password in `SENDER_EMAIL_PASSWORD`

### ☁️ AWS Configuration

| Variable | Description | Required |
|----------|-------------|----------|
| `AWS_ACCESS_KEY_ID` | AWS access key | ✅ |
| `AWS_SECRET_ACCESS_KEY` | AWS secret key | ✅ |
| `AWS_S3_BUCKET_NAME` | S3 bucket name | ✅ |
| `AWS_REGION` | AWS region | ✅ |

**AWS Setup:**
1. Create IAM user with S3 permissions
2. Generate access keys
3. Create S3 bucket with appropriate permissions

### 🤖 AI/ML Services

| Variable | Description | Required |
|----------|-------------|----------|
| `OPENAI_API_KEY` | OpenAI API key | ✅ |
| `OPENAI_MODEL` | GPT model to use | ❌ |
| `AI_QUESTION_GENERATION` | Enable AI question generation | ❌ |

### 💳 Payment Gateway

| Variable | Description | Required |
|----------|-------------|----------|
| `ZENOPAY_ACCOUNT_ID` | ZenoPay account ID | ✅ |
| `ZENOPAY_SECRET_KEY` | ZenoPay secret key | ✅ |
| `ZENOPAY_API_KEY` | ZenoPay API key | ✅ |
| `ZENOPAY_WEBHOOK_URL` | Webhook endpoint URL | ✅ |

### 📊 Logging & Monitoring

| Variable | Description | Default |
|----------|-------------|---------|
| `LOG_LEVEL` | Logging level | `info` |
| `LOG_FILE_PATH` | Log file location | `./logs/app.log` |
| `SENTRY_DSN` | Sentry error tracking | - |

### 🚀 Feature Flags

Enable or disable features using boolean values:

| Feature | Variable | Description |
|---------|----------|-------------|
| User Registration | `FEATURE_USER_REGISTRATION` | Allow new user signups |
| Email Verification | `FEATURE_EMAIL_VERIFICATION` | Require email verification |
| Payment Gateway | `FEATURE_PAYMENT_GATEWAY` | Enable payment processing |
| AI Assistance | `FEATURE_AI_ASSISTANCE` | Enable AI-powered features |
| Video Streaming | `FEATURE_VIDEO_STREAMING` | Enable video content |
| Discussion Forum | `FEATURE_DISCUSSION_FORUM` | Enable forum features |

## Environment-Specific Configuration

### Development Environment
```bash
NODE_ENV=development
DEBUG=true
VERBOSE_LOGGING=true
MOCK_EXTERNAL_APIS=false
```

### Production Environment
```bash
NODE_ENV=production
DEBUG=false
VERBOSE_LOGGING=false
HELMET_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=50
```

### Testing Environment
```bash
NODE_ENV=test
TEST_DATABASE_URL=mongodb://localhost:27017/stjoseph_test
TEST_EMAIL_PROVIDER=ethereal
MOCK_EXTERNAL_APIS=true
```

## Security Checklist

- [ ] Strong JWT secret (32+ characters)
- [ ] Secure session secret
- [ ] App-specific email passwords
- [ ] AWS IAM user with minimal permissions
- [ ] Webhook URLs use HTTPS
- [ ] Rate limiting enabled
- [ ] CORS properly configured
- [ ] Security headers enabled
- [ ] Secrets not in version control

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check MongoDB URL format
   - Verify credentials
   - Ensure IP whitelist includes your IP

2. **Email Not Sending**
   - Verify email credentials
   - Check app-specific password
   - Ensure 2FA is enabled

3. **File Upload Errors**
   - Check AWS credentials
   - Verify S3 bucket permissions
   - Confirm bucket region

4. **Payment Webhook Issues**
   - Ensure webhook URL is accessible
   - Check HTTPS configuration
   - Verify webhook secret

### Environment Validation

The application validates environment variables on startup. Check the console for any missing or invalid configurations.

## Support

For additional help with environment configuration:
- Check the application logs
- Review the `.env.example` file
- Contact the development team
- Refer to service provider documentation

## Security Notice

⚠️ **Important Security Reminders:**
- Never commit `.env` files to version control
- Use different credentials for each environment
- Rotate secrets regularly
- Monitor for credential leaks
- Use environment-specific access controls
