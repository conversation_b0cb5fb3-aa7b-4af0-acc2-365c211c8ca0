const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
const Plan = require('./models/planModel');

async function checkAllPaymentIssues() {
  try {
    await mongoose.connect('mongodb://hvmgeeks:<EMAIL>:27017,ac-4ojvv6y-shard-00-01.cdg8fdn.mongodb.net:27017,ac-4ojvv6y-shard-00-02.cdg8fdn.mongodb.net:27017/stjoseph?ssl=true&replicaSet=atlas-fsgg6f-shard-0&authSource=admin&retryWrites=true&w=majority&appName=Cluster0');
    
    console.log('🔍 Checking ALL users with payment issues...\n');
    
    // Check specific users mentioned
    const usersToCheck = ['didi.didi', 'lolo.lolo', 'kaka.mimi'];
    
    for (const username of usersToCheck) {
      console.log(`\n👤 Checking ${username}:`);
      
      const user = await User.findOne({ username });
      if (!user) {
        console.log(`   ❌ User ${username} not found`);
        continue;
      }
      
      const subscriptions = await Subscription.find({ user: user._id })
        .populate('activePlan', 'title duration discountedPrice')
        .sort({ createdAt: -1 });
      
      console.log(`   Status: ${user.subscriptionStatus}`);
      console.log(`   Payment Required: ${user.paymentRequired}`);
      console.log(`   Subscriptions: ${subscriptions.length}`);
      
      if (subscriptions.length > 0) {
        const latest = subscriptions[0];
        console.log(`   Latest: ${latest.activePlan?.title} - ${latest.paymentStatus}/${latest.status}`);
        
        if (latest.paymentHistory.length > 0) {
          const payment = latest.paymentHistory[latest.paymentHistory.length - 1];
          console.log(`   Order ID: ${payment.orderId}`);
          console.log(`   Amount: ${payment.amount}`);
        }
      }
    }
    
    // Find ALL users with subscription issues
    console.log('\n🔍 Finding ALL users with subscription issues...');
    
    const allUsers = await User.find({});
    const usersWithIssues = [];
    
    for (const user of allUsers) {
      const subscriptions = await Subscription.find({ user: user._id })
        .populate('activePlan', 'title duration discountedPrice');
      
      if (subscriptions.length > 0) {
        const paidSubs = subscriptions.filter(sub => sub.paymentStatus === 'paid');
        const activeSubs = subscriptions.filter(sub => sub.status === 'active');
        const pendingSubs = subscriptions.filter(sub => sub.paymentStatus === 'pending');
        
        // Check for issues
        const issues = [];
        
        if (paidSubs.length > 0 && user.subscriptionStatus === 'free') {
          issues.push('PAID_BUT_FREE_STATUS');
        }
        
        if (activeSubs.length > 0 && user.subscriptionStatus === 'free') {
          issues.push('ACTIVE_BUT_FREE_STATUS');
        }
        
        if (pendingSubs.length > 0 && user.subscriptionStatus === 'free') {
          issues.push('PENDING_PAYMENTS');
        }
        
        if (issues.length > 0) {
          usersWithIssues.push({
            user,
            subscriptions,
            issues,
            paidCount: paidSubs.length,
            activeCount: activeSubs.length,
            pendingCount: pendingSubs.length
          });
        }
      }
    }
    
    console.log(`\n🚨 Found ${usersWithIssues.length} users with payment issues:`);
    
    usersWithIssues.forEach((userData, index) => {
      const user = userData.user;
      console.log(`\n${index + 1}. 👤 ${user.firstName} ${user.lastName} (@${user.username})`);
      console.log(`   Issues: ${userData.issues.join(', ')}`);
      console.log(`   Subscriptions: ${userData.subscriptions.length} total (${userData.paidCount} paid, ${userData.activeCount} active, ${userData.pendingCount} pending)`);
      
      if (userData.subscriptions.length > 0) {
        const latest = userData.subscriptions[0];
        const payment = latest.paymentHistory[latest.paymentHistory.length - 1];
        console.log(`   Latest Order: ${payment?.orderId} - ${latest.activePlan?.title} (${payment?.amount} TZS)`);
        console.log(`   Created: ${latest.createdAt.toISOString().split('T')[0]}`);
      }
    });
    
    // Check webhook configuration
    console.log('\n🔧 Current Webhook Configuration:');
    console.log(`   Webhook URL: ${process.env.ZENOPAY_WEBHOOK_URL}`);
    console.log(`   Account ID: ${process.env.ZENOPAY_ACCOUNT_ID}`);
    console.log(`   Environment: ${process.env.ZENOPAY_ENVIRONMENT}`);
    console.log(`   Demo Mode: ${process.env.PAYMENT_DEMO_MODE}`);
    
    // Check recent subscriptions
    console.log('\n📊 Recent Subscription Activity (Last 24 hours):');
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentSubs = await Subscription.find({
      createdAt: { $gte: yesterday }
    }).populate('user', 'firstName lastName username')
      .populate('activePlan', 'title discountedPrice')
      .sort({ createdAt: -1 });
    
    console.log(`   ${recentSubs.length} subscriptions created in last 24 hours`);
    
    recentSubs.forEach((sub, index) => {
      const payment = sub.paymentHistory[sub.paymentHistory.length - 1];
      console.log(`   ${index + 1}. ${sub.user?.firstName} ${sub.user?.lastName} - ${sub.activePlan?.title} - ${sub.paymentStatus}/${sub.status}`);
      console.log(`      Order: ${payment?.orderId} - ${payment?.amount} TZS`);
    });
    
    return usersWithIssues;
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    return [];
  } finally {
    mongoose.disconnect();
  }
}

checkAllPaymentIssues();
