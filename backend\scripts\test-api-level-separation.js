const mongoose = require('mongoose');
const axios = require('axios');
const jwt = require('jsonwebtoken');
require('dotenv').config();

const User = require('../models/userModel');

async function testAPILevelSeparation() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Get test users from each level
        const primaryUser = await User.findOne({ level: 'primary' });
        const secondaryUser = await User.findOne({ level: 'secondary' });
        const advanceUser = await User.findOne({ level: 'advance' });

        console.log('\n👥 Test Users:');
        console.log(`Primary: ${primaryUser?.name} (${primaryUser?.level})`);
        console.log(`Secondary: ${secondaryUser?.name} (${secondaryUser?.level})`);
        console.log(`Advance: ${advanceUser?.name} (${advanceUser?.level})`);

        // Test each user's access to ranking and forum
        const testUsers = [
            { user: primaryUser, levelName: 'Primary' },
            { user: secondaryUser, levelName: 'Secondary' },
            { user: advanceUser, levelName: 'Advance' }
        ];

        for (const { user, levelName } of testUsers) {
            if (!user) {
                console.log(`\n❌ No ${levelName} user found, skipping tests`);
                continue;
            }

            console.log(`\n🧪 Testing ${levelName} User: ${user.name}`);
            
            // Generate token for this user
            const token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET);

            // Test 1: XP Leaderboard
            try {
                const leaderboardResponse = await axios.get('http://localhost:5000/api/quiz/xp-leaderboard', {
                    headers: { 'Authorization': `Bearer ${token}` },
                    params: { limit: 10, levelFilter: user.level }
                });

                console.log(`  📊 Leaderboard: ${leaderboardResponse.data.data?.length || 0} users returned`);
                
                // Check if all returned users are from the same level
                const returnedUsers = leaderboardResponse.data.data || [];
                const levelMismatch = returnedUsers.find(u => u.level !== user.level);
                if (levelMismatch) {
                    console.log(`  ❌ Level contamination detected! Found ${levelMismatch.level} user in ${user.level} leaderboard`);
                } else {
                    console.log(`  ✅ Level separation working correctly in leaderboard`);
                }

            } catch (error) {
                console.log(`  ❌ Leaderboard API error: ${error.response?.status} ${error.response?.data?.message || error.message}`);
            }

            // Test 2: Forum Questions
            try {
                const forumResponse = await axios.get('http://localhost:5000/api/forum/get-all-questions', {
                    headers: { 'Authorization': `Bearer ${token}` },
                    params: { page: 1, limit: 10 }
                });

                console.log(`  💬 Forum: ${forumResponse.data.data?.length || 0} questions returned`);
                
                // Check if all returned questions are from the appropriate level
                const returnedQuestions = forumResponse.data.data || [];
                let expectedLevels;
                
                if (user.level === 'primary') {
                    expectedLevels = ['primary'];
                } else if (user.level === 'secondary') {
                    expectedLevels = ['secondary'];
                } else if (user.level === 'advance') {
                    expectedLevels = ['advance'];
                }

                const levelMismatch = returnedQuestions.find(q => !expectedLevels.includes(q.level));
                if (levelMismatch) {
                    console.log(`  ❌ Level contamination detected! Found ${levelMismatch.level} question for ${user.level} user`);
                } else {
                    console.log(`  ✅ Level separation working correctly in forum`);
                }

            } catch (error) {
                console.log(`  ❌ Forum API error: ${error.response?.status} ${error.response?.data?.message || error.message}`);
            }

            // Test 3: Exams
            try {
                const examResponse = await axios.get('http://localhost:5000/api/exams/get-all-exams', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });

                console.log(`  📝 Exams: ${examResponse.data.data?.length || 0} exams returned`);
                
                // Check if all returned exams are from the appropriate level
                const returnedExams = examResponse.data.data || [];
                let expectedLevels;
                
                if (user.level === 'primary') {
                    expectedLevels = ['primary'];
                } else if (user.level === 'secondary') {
                    expectedLevels = ['secondary'];
                } else if (user.level === 'advance') {
                    expectedLevels = ['advance'];
                }

                const levelMismatch = returnedExams.find(e => !expectedLevels.includes(e.level));
                if (levelMismatch) {
                    console.log(`  ❌ Level contamination detected! Found ${levelMismatch.level} exam for ${user.level} user`);
                } else {
                    console.log(`  ✅ Level separation working correctly in exams`);
                }

            } catch (error) {
                console.log(`  ❌ Exam API error: ${error.response?.status} ${error.response?.data?.message || error.message}`);
            }
        }

        console.log('\n🎯 Level Separation Test Summary:');
        console.log('✅ Database levels normalized to lowercase');
        console.log('✅ API endpoints tested for level filtering');
        console.log('✅ Cross-level contamination checks completed');
        
    } catch (error) {
        console.error('❌ Error testing API level separation:', error);
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    testAPILevelSeparation();
}

module.exports = testAPILevelSeparation;
