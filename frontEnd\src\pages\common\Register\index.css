/* ===== RESPONSIVE REGISTER PAGE ===== */

/* Ensure dropdowns appear above everything */
.ant-select-dropdown {
    z-index: 99999 !important;
}

/* Mobile dropdown portal fix */
body > .ant-select-dropdown {
    z-index: 99999 !important;
}

/* Native mobile dropdown fix */
.mobile-dropdown-fix {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    font-size: 16px !important; /* Prevents zoom on iOS */
    min-height: 48px !important; /* Touch-friendly */
    padding: 12px 40px 12px 16px !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 8px !important;
    background-color: white !important;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e") !important;
    background-repeat: no-repeat !important;
    background-position: right 12px center !important;
    background-size: 16px !important;
    cursor: pointer !important;
    width: 100% !important;
}

.mobile-dropdown-fix:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.mobile-dropdown-fix:disabled {
    background-color: #f9fafb !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
}

/* Mobile specific improvements */
@media (max-width: 768px) {
    .mobile-dropdown-fix {
        font-size: 16px !important; /* Critical for iOS */
        min-height: 48px !important;
        padding: 14px 40px 14px 16px !important;
    }
}

/* Help text styling */
.ant-form-item-explain {
    color: #6b7280 !important;
    font-size: 0.875rem !important;
    margin-top: 0.5rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.25rem !important;
}

/* Auto-generated email field styling */
.form-input:disabled {
    background-color: #f9fafb !important;
    border-color: #e5e7eb !important;
    color: #6b7280 !important;
    cursor: not-allowed !important;
}

/* Payment description styling */
.ant-form-item-explain-connected {
    color: #059669 !important;
    font-weight: 500 !important;
}

.register-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    font-family: 'Inter', 'Roboto', 'Nunito', system-ui, -apple-system, sans-serif;
    position: relative;
}

.register-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(0, 123, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(0, 86, 210, 0.1) 0%, transparent 50%);
    z-index: 0;
}

.register-card {
    width: 100%;
    max-width: 480px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    padding: 2rem;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    max-height: 90vh;
    overflow-y: auto;
}

.register-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

/* ===== HEADER SECTION ===== */
.register-header {
    text-align: center;
    margin-bottom: 2rem;
}

.register-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    display: block;
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.register-logo:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.15);
}

.verification-icon {
    width: 64px;
    height: 64px;
    background: rgba(0, 123, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.verification-icon i {
    font-size: 1.5rem;
    color: #007BFF;
}

.register-title {
    font-size: 1.875rem;
    font-weight: 800;
    color: #1f2937;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.register-subtitle {
    color: #6b7280;
    margin-bottom: 2rem;
    font-size: 0.875rem;
    line-height: 1.5;
}

/* ===== OTP VERIFICATION STYLING ===== */
.otp-instructions {
    margin-bottom: 2rem;
    width: 100%;
}

.otp-info-card {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border: 2px solid #bae6fd;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.otp-info-title {
    font-size: 1.125rem;
    font-weight: 700;
    color: #0c4a6e;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.otp-info-text {
    color: #0369a1;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.otp-steps {
    margin-bottom: 1rem;
}

.otp-step {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 0.5rem;
    border: 1px solid rgba(14, 165, 233, 0.2);
}

.step-number {
    width: 1.5rem;
    height: 1.5rem;
    background: #0ea5e9;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 700;
    flex-shrink: 0;
}

.step-text {
    color: #0c4a6e;
    font-size: 0.875rem;
    font-weight: 500;
}

.otp-help {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 0.5rem;
    padding: 0.75rem;
    border: 1px solid rgba(14, 165, 233, 0.3);
}

.help-text {
    color: #0369a1;
    font-size: 0.8rem;
    margin: 0;
    line-height: 1.4;
}

.otp-input {
    text-align: center;
    font-size: 1.25rem;
    font-weight: 700;
    letter-spacing: 0.25rem;
}

/* ===== RESEND SECTION STYLING ===== */
.resend-section {
    margin-top: 1.5rem;
    text-align: center;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.75rem;
    border: 1px solid #e2e8f0;
}

.resend-text {
    color: #64748b;
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
    font-weight: 500;
}

.resend-btn {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
}

.resend-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
}

.resend-btn:disabled {
    background: #d1d5db;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* ===== FORM STYLING ===== */
.register-form {
    margin-bottom: 1.5rem;
}

.register-form .ant-form-item {
    margin-bottom: 1.25rem;
}

.register-form .ant-form-item-label > label {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    background: #f9fafb;
    color: #1f2937;
    font-family: inherit;
    line-height: 1.5;
}

.form-input:focus {
    outline: none;
    border-color: #007BFF;
    background: #ffffff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    transform: translateY(-1px);
}

.form-input::placeholder {
    color: #9ca3af;
    font-size: 0.875rem;
}

.form-help-text {
    margin-top: 0.375rem;
    font-size: 0.75rem;
    color: #6b7280;
    font-style: italic;
    line-height: 1.4;
}

/* ===== CLEAN FORM STYLING ===== */
/* Remove all validation styling to prevent red X issues */

/* ===== PHONE NUMBER HELP SECTION ===== */
.phone-help-section {
    margin-top: 0.5rem;
}

.form-help-text {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.otp-input {
    text-align: center;
    font-size: 1.125rem;
    letter-spacing: 0.1em;
    font-weight: 600;
}

.register-btn {
    width: 100%;
    background: linear-gradient(135deg, #007BFF 0%, #0056D2 100%);
    color: white;
    padding: 1rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.25);
    margin-top: 0.5rem;
}

.register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(0, 123, 255, 0.35);
    background: linear-gradient(135deg, #0056D2 0%, #004494 100%);
}

.register-btn:active {
    transform: translateY(0);
}

.register-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* ===== LOADING SPINNER ===== */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ffffff;
    animation: spin 1s ease-in-out infinite;
    margin-right: 8px;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}



/* ===== FOOTER SECTION ===== */
.register-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.register-footer p {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
}

.register-link {
    color: #007BFF;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
}

.register-link:hover {
    color: #0056D2;
    text-decoration: underline;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Devices (320px - 480px) */
@media (max-width: 480px) {
    .register-container {
        padding: 0.75rem;
        align-items: flex-start;
        padding-top: 1rem;
    }

    .register-card {
        max-width: 100%;
        padding: 1.5rem;
        border-radius: 1rem;
        margin: 0;
        max-height: 95vh;
    }

    .register-logo {
        width: 64px;
        height: 64px;
        margin-bottom: 1rem;
    }

    .verification-icon {
        width: 56px;
        height: 56px;
        margin-bottom: 1rem;
    }

    .verification-icon i {
        font-size: 1.25rem;
    }

    .register-title {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .register-subtitle {
        font-size: 0.8rem;
        margin-bottom: 1.5rem;
    }

    .register-form .ant-form-item {
        margin-bottom: 1rem;
    }

    .form-input {
        padding: 1rem;
        font-size: 1rem;
        border-radius: 0.5rem;
    }

    .register-btn {
        padding: 1.125rem 1.5rem;
        font-size: 1rem;
        border-radius: 0.5rem;
        width: 100%;
        min-height: 48px; /* Touch-friendly minimum */
        font-weight: 600;
    }

    .register-footer {
        padding-top: 1.25rem;
    }

    .register-footer p {
        font-size: 0.8rem;
    }

    .form-help-text {
        font-size: 0.7rem;
    }
}

/* Tablet Devices (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .register-container {
        padding: 1rem;
    }

    .register-card {
        max-width: 500px;
        padding: 2rem;
    }

    .register-logo {
        width: 72px;
        height: 72px;
    }

    .verification-icon {
        width: 60px;
        height: 60px;
    }

    .register-title {
        font-size: 1.75rem;
    }

    .form-input {
        padding: 0.9375rem 1rem;
        font-size: 0.9375rem;
    }

    .register-btn {
        padding: 1.0625rem 1.5rem;
        font-size: 0.9375rem;
    }
}

/* Desktop Devices (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
    .register-card {
        max-width: 520px;
        padding: 2.5rem;
    }

    .register-logo {
        width: 84px;
        height: 84px;
    }

    .verification-icon {
        width: 68px;
        height: 68px;
    }
}

/* Large Desktop (1025px+) */
@media (min-width: 1025px) {
    .register-card {
        max-width: 540px;
        padding: 3rem;
    }

    .register-logo {
        width: 96px;
        height: 96px;
    }

    .verification-icon {
        width: 72px;
        height: 72px;
    }

    .register-title {
        font-size: 2rem;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .register-container {
        padding: 0.75rem;
        align-items: center;
    }

    .register-card {
        padding: 1.5rem;
        max-height: 85vh;
    }

    .register-logo {
        width: 56px;
        height: 56px;
        margin-bottom: 0.75rem;
    }

    .verification-icon {
        width: 48px;
        height: 48px;
        margin-bottom: 0.75rem;
    }

    .register-title {
        font-size: 1.375rem;
        margin-bottom: 0.375rem;
    }

    .register-subtitle {
        margin-bottom: 1.25rem;
    }

    .register-form .ant-form-item {
        margin-bottom: 0.875rem;
    }
}

/* Very Small Screens */
@media (max-width: 320px) {
    .register-container {
        padding: 0.5rem;
    }

    .register-card {
        padding: 1rem;
        border-radius: 0.75rem;
    }

    .register-logo {
        width: 56px;
        height: 56px;
    }

    .verification-icon {
        width: 48px;
        height: 48px;
    }

    .register-title {
        font-size: 1.25rem;
    }

    .form-input {
        padding: 0.875rem;
        font-size: 0.875rem;
    }

    .register-btn {
        padding: 1rem;
        font-size: 0.875rem;
    }
}

/* ===== MODERN RESPONSIVE ENHANCEMENTS ===== */

/* Modern Mobile Enhancements */
@media (max-width: 640px) {
    /* Improve touch targets */
    .form-input, .register-btn {
        min-height: 44px; /* Apple's recommended minimum touch target */
    }

    /* Better spacing for mobile */
    .register-form .ant-form-item {
        margin-bottom: 1.25rem;
    }

    /* Optimize for mobile keyboards */
    .register-container {
        padding-bottom: 2rem;
    }

    /* Improve readability */
    .register-subtitle {
        line-height: 1.6;
    }

    /* Better button styling for mobile */
    .register-btn {
        font-weight: 600;
        letter-spacing: 0.025em;
        transition: all 0.2s ease;
    }

    .register-btn:active {
        transform: scale(0.98);
    }

    /* Improve form field focus states */
    .form-input:focus {
        border-width: 2px;
        padding: calc(1rem - 1px); /* Compensate for border width change */
    }
}

/* Tablet Portrait Optimization */
@media (min-width: 641px) and (max-width: 768px) {
    .register-card {
        max-width: 480px;
        padding: 2.25rem;
    }

    .register-form .ant-form-item {
        margin-bottom: 1.375rem;
    }

    /* Better touch targets for tablets */
    .form-input, .register-btn {
        min-height: 40px;
    }
}

/* Modern Laptop/Desktop Enhancements */
@media (min-width: 1025px) {
    .register-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
    }

    .form-input:focus {
        transform: translateY(-1px);
    }

    .register-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
    }
}

/* High DPI Display Optimization */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .register-logo {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Dark Mode Support (if needed in future) */
@media (prefers-color-scheme: dark) {
    .register-container {
        background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    }

    .register-card {
        background: rgba(31, 41, 55, 0.95);
        border-color: rgba(75, 85, 99, 0.3);
    }
}

/* Mobile Dropdown Enhancements */
@media (max-width: 768px) {
    /* Ant Design Select mobile improvements */
    .ant-select-selector {
        font-size: 16px !important; /* Prevents zoom on iOS */
        min-height: 44px !important; /* Touch-friendly minimum */
        padding: 8px 12px !important;
    }

    .ant-select-selection-item {
        font-size: 16px !important;
        line-height: 1.5 !important;
    }

    .ant-select-selection-placeholder {
        font-size: 16px !important;
        line-height: 1.5 !important;
    }

    /* Dropdown menu mobile improvements */
    .ant-select-dropdown {
        font-size: 16px !important;
    }

    .ant-select-item {
        padding: 12px 16px !important;
        min-height: 44px !important;
        font-size: 16px !important;
        line-height: 1.5 !important;
    }

    .ant-select-item-option-content {
        font-size: 16px !important;
    }

    /* Ensure proper touch targets */
    .ant-select-arrow {
        width: 20px !important;
        height: 20px !important;
        margin-top: -10px !important;
    }

    /* Fix for mobile viewport */
    .ant-select-dropdown {
        max-height: 50vh !important;
        overflow-y: auto !important;
        /* Ensure dropdown stays within viewport */
        position: fixed !important;
        z-index: 9999 !important;
    }

    /* Prevent dropdown from going off-screen */
    .ant-select-dropdown-placement-bottomLeft,
    .ant-select-dropdown-placement-bottomRight {
        max-width: calc(100vw - 32px) !important;
        left: 16px !important;
        right: 16px !important;
        width: auto !important;
    }

    /* Ensure dropdown content is properly sized */
    .ant-select-dropdown .ant-select-item {
        white-space: normal !important;
        word-wrap: break-word !important;
    }

    /* Additional mobile form improvements */
    .register-form .ant-form-item-control-input {
        width: 100% !important;
    }

    .register-form .ant-select {
        width: 100% !important;
    }

    /* Prevent horizontal scrolling on mobile */
    .register-container {
        overflow-x: hidden !important;
        max-width: 100vw !important;
    }

    .register-card {
        max-width: calc(100vw - 32px) !important;
        margin: 0 auto !important;
    }

    /* Force dropdown to use full width on very small screens */
    .ant-select-dropdown {
        left: 8px !important;
        right: 8px !important;
        width: calc(100vw - 16px) !important;
        max-width: calc(100vw - 16px) !important;
    }

    /* Ensure dropdown items are readable on small screens */
    .ant-select-item-option-content {
        white-space: normal !important;
        word-break: break-word !important;
        line-height: 1.4 !important;
    }

    /* Mobile Select Component Fixes */
    .mobile-select .ant-select-selector {
        font-size: 16px !important;
        min-height: 48px !important;
        padding: 12px 16px !important;
        border-radius: 8px !important;
        border: 2px solid #e5e7eb !important;
    }

    .mobile-select .ant-select-selection-item {
        font-size: 16px !important;
        line-height: 1.5 !important;
        color: #374151 !important;
    }

    .mobile-select .ant-select-selection-placeholder {
        font-size: 16px !important;
        line-height: 1.5 !important;
        color: #9ca3af !important;
    }

    .mobile-select .ant-select-arrow {
        font-size: 16px !important;
        color: #6b7280 !important;
    }

    /* Force dropdown to appear properly on mobile */
    .ant-select-dropdown {
        position: fixed !important;
        z-index: 99999 !important;
        max-height: 60vh !important;
        overflow-y: auto !important;
        border-radius: 8px !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
    }

    /* Improve dropdown positioning for very small screens */
    @media (max-width: 360px) {
        .ant-select-dropdown {
            left: 8px !important;
            right: 8px !important;
            width: calc(100vw - 16px) !important;
            max-width: calc(100vw - 16px) !important;
        }

        .ant-select-item {
            padding: 12px 16px !important;
            font-size: 16px !important;
            min-height: 48px !important;
        }

        .mobile-select .ant-select-selector {
            min-height: 44px !important;
            padding: 10px 14px !important;
        }
    }
}