const mongoose = require("mongoose");

const literatureSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: false,
    },
    subject: {
      type: String,
      required: true,
    },
    className: {
      type: String,
      required: true,
    },
    additionalClasses: [{
      type: String,
      required: false,
    }],
    level: {
      type: String,
      enum: ["secondary"], // Only for secondary level
      required: true,
      default: "secondary",
    },
    documentUrl: {
      type: String,
      required: true,
    },
    thumbnail: {
      type: String,
      required: false,
    },
    year: {
      type: String,
      required: false,
    },
    author: {
      type: String,
      required: false,
    },
    genre: {
      type: String,
      enum: ["play", "novel", "poetry", "short-story", "drama"],
      required: true,
    },
    language: {
      type: String,
      enum: ["english", "kiswahili"],
      required: true,
      default: "english",
    },
    tags: [{
      type: String,
    }],
    isActive: {
      type: Boolean,
      default: true,
    },
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    downloadCount: {
      type: Number,
      default: 0,
    },
    viewCount: {
      type: Number,
      default: 0,
    },
    // Additional metadata for literature
    metadata: {
      pageCount: Number,
      fileSize: String,
      format: String,
      isbn: String,
      publisher: String,
      publicationYear: String,
      difficulty: {
        type: String,
        enum: ["beginner", "intermediate", "advanced"],
        default: "intermediate",
      },
    },
  },
  {
    timestamps: true,
  }
);

// Index for better query performance
literatureSchema.index({ level: 1, className: 1, subject: 1 });
literatureSchema.index({ genre: 1, language: 1 });
literatureSchema.index({ title: "text", description: "text", author: "text" });

// Virtual for full document info
literatureSchema.virtual('fullInfo').get(function() {
  return {
    id: this._id,
    title: this.title,
    author: this.author,
    genre: this.genre,
    language: this.language,
    className: this.className,
    subject: this.subject,
  };
});

// Method to increment view count
literatureSchema.methods.incrementViewCount = function() {
  this.viewCount += 1;
  return this.save();
};

// Static method to find by genre
literatureSchema.statics.findByGenre = function(genre, level = 'secondary') {
  return this.find({ 
    genre: genre, 
    level: level, 
    isActive: true 
  }).sort({ createdAt: -1 });
};

// Static method to find by class and subject
literatureSchema.statics.findByClassAndSubject = function(className, subject, level = 'secondary') {
  const filter = { 
    level: level, 
    isActive: true 
  };
  
  if (className && className !== 'all') {
    filter.className = className;
  }
  
  if (subject && subject !== 'all') {
    filter.subject = subject;
  }
  
  return this.find(filter).sort({ createdAt: -1 });
};

module.exports = mongoose.model("Literature", literatureSchema);
