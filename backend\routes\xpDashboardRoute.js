const router = require("express").Router();
const authMiddleware = require("../middlewares/authMiddleware");
const User = require("../models/userModel");
const XPTransaction = require("../models/xpTransactionModel");
const Report = require("../models/reportModel");

// Get comprehensive XP dashboard data
router.get("/dashboard", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    
    // Get user with activity tracking
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    // Get recent XP transactions (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentTransactions = await XPTransaction.find({
      userId: userId,
      createdAt: { $gte: thirtyDaysAgo }
    }).sort({ createdAt: -1 }).limit(50);

    // Calculate daily XP for the last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const dailyXP = await XPTransaction.aggregate([
      {
        $match: {
          userId: user._id,
          createdAt: { $gte: sevenDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: "%Y-%m-%d", date: "$createdAt" }
          },
          totalXP: { $sum: "$amount" },
          transactionCount: { $sum: 1 }
        }
      },
      { $sort: { "_id": 1 } }
    ]);

    // Calculate subject-wise XP
    const subjectXP = await XPTransaction.aggregate([
      {
        $match: {
          userId: user._id,
          "quizData.subject": { $exists: true }
        }
      },
      {
        $group: {
          _id: "$quizData.subject",
          totalXP: { $sum: "$amount" },
          quizCount: { $sum: 1 },
          averageXP: { $avg: "$amount" }
        }
      },
      { $sort: { totalXP: -1 } }
    ]);

    // Get level progress information
    const currentLevel = user.currentLevel || 1;
    const currentXP = user.totalXP || 0;
    const xpToNextLevel = user.xpToNextLevel || 100;
    const xpForCurrentLevel = currentXP - xpToNextLevel;
    const totalXPForNextLevel = getXPRequiredForLevel(currentLevel + 1);
    const progressPercentage = Math.round((xpForCurrentLevel / totalXPForNextLevel) * 100);

    // Calculate streak information
    const activityTracking = user.activityTracking || {};
    const streakInfo = {
      loginStreak: {
        current: activityTracking.dailyLoginStreak || 0,
        best: activityTracking.bestLoginStreak || 0,
      },
      quizStreak: {
        current: user.currentStreak || 0,
        best: user.bestStreak || 0,
      },
      perfectScoreStreak: {
        current: activityTracking.perfectScoreStreak || 0,
        best: activityTracking.bestPerfectStreak || 0,
      }
    };

    // Calculate performance metrics
    const performanceMetrics = {
      totalQuizzes: user.totalQuizzesTaken || 0,
      quizzesPassed: activityTracking.quizzesPassed || 0,
      quizzesFailed: activityTracking.quizzesFailed || 0,
      averageScore: user.averageScore || 0,
      totalCorrectAnswers: activityTracking.totalCorrectAnswers || 0,
      totalQuestionsAnswered: activityTracking.totalQuestionsAnswered || 0,
      accuracyRate: activityTracking.totalQuestionsAnswered > 0 ? 
        Math.round((activityTracking.totalCorrectAnswers / activityTracking.totalQuestionsAnswered) * 100) : 0
    };

    // Get recent achievements (last 10)
    const recentAchievements = user.achievements ? 
      user.achievements.slice(-10).reverse() : [];

    // Calculate weekly and monthly stats
    const weeklyStats = activityTracking.weeklyStats || {};
    const monthlyStats = activityTracking.monthlyStats || {};

    // XP breakdown by source
    const xpBreakdown = {
      fromQuizzes: user.xpStats?.xpFromQuizzes || 0,
      fromStreaks: user.xpStats?.xpFromStreaks || 0,
      fromAchievements: user.xpStats?.xpFromAchievements || 0,
      fromConsistency: user.xpStats?.xpFromConsistency || 0,
    };

    // Subject performance details
    const subjectPerformance = activityTracking.subjectPerformance || [];

    const dashboardData = {
      // User basic info
      user: {
        name: user.name,
        level: user.level,
        class: user.class,
        profileImage: user.profileImage,
        subscriptionStatus: user.subscriptionStatus
      },

      // XP Information
      xp: {
        total: currentXP,
        lifetime: user.lifetimeXP || 0,
        seasonal: user.seasonXP || 0,
        toNextLevel: xpToNextLevel,
        currentLevel: currentLevel,
        progressPercentage: progressPercentage,
        breakdown: xpBreakdown
      },

      // Streak Information
      streaks: streakInfo,

      // Performance Metrics
      performance: performanceMetrics,

      // Activity Data
      activity: {
        daily: dailyXP,
        weekly: weeklyStats,
        monthly: monthlyStats,
        recentTransactions: recentTransactions.slice(0, 10)
      },

      // Subject Data
      subjects: {
        performance: subjectPerformance,
        xpDistribution: subjectXP
      },

      // Achievements
      achievements: recentAchievements,

      // Statistics
      stats: {
        totalLoginDays: activityTracking.totalLoginDays || 0,
        averageXPPerQuiz: user.xpStats?.averageXPPerQuiz || 0,
        bestXPGain: user.xpStats?.bestXPGain || 0,
        lastXPGain: user.xpStats?.lastXPGain || null
      }
    };

    res.send({
      message: "XP Dashboard data fetched successfully",
      data: dashboardData,
      success: true,
    });

  } catch (error) {
    console.error("XP Dashboard error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Get XP leaderboard for user's class
router.get("/class-leaderboard", authMiddleware, async (req, res) => {
  try {
    const userId = req.body.userId;
    const user = await User.findById(userId);
    
    if (!user) {
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    // Get top 20 users from same class
    const classLeaderboard = await User.find({
      class: user.class,
      level: user.level,
      isAdmin: { $ne: true },
      isBlocked: { $ne: true }
    })
    .select('name totalXP currentLevel averageScore currentStreak profileImage subscriptionStatus')
    .sort({ totalXP: -1 })
    .limit(20);

    // Add rank to each user
    const rankedLeaderboard = classLeaderboard.map((classUser, index) => ({
      ...classUser.toObject(),
      rank: index + 1,
      isCurrentUser: classUser._id.toString() === userId.toString()
    }));

    res.send({
      message: "Class leaderboard fetched successfully",
      data: rankedLeaderboard,
      success: true,
    });

  } catch (error) {
    console.error("Class leaderboard error:", error);
    res.status(500).send({
      message: error.message,
      success: false,
    });
  }
});

// Helper function to calculate XP required for level
function getXPRequiredForLevel(level) {
  if (level <= 1) return 0;
  return Math.round(100 * level * (level - 1) / 2 + 50 * (level - 1));
}

module.exports = router;
