const mongoose = require('mongoose');
require('dotenv').config();

const User = require('../models/userModel');
const ForumQuestion = require('../models/forumQuestionModel');

async function testForumLevelSeparation() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Get test users from each level
        const primaryUser = await User.findOne({ level: 'primary' });
        const secondaryUser = await User.findOne({ level: 'secondary' });
        const advanceUser = await User.findOne({ level: 'advance' });

        console.log('\n👥 Test Users:');
        console.log(`Primary: ${primaryUser?.name} (${primaryUser?.level})`);
        console.log(`Secondary: ${secondaryUser?.name} (${secondaryUser?.level})`);
        console.log(`Advance: ${advanceUser?.name} (${advanceUser?.level})`);

        // Check all forum questions and their levels
        console.log('\n📊 All Forum Questions by Level:');
        const allQuestions = await ForumQuestion.find({}).populate('user');
        
        const questionsByLevel = {};
        allQuestions.forEach(q => {
            const level = q.level || 'undefined';
            if (!questionsByLevel[level]) {
                questionsByLevel[level] = [];
            }
            questionsByLevel[level].push(q);
        });

        Object.keys(questionsByLevel).forEach(level => {
            console.log(`  ${level}: ${questionsByLevel[level].length} questions`);
            questionsByLevel[level].slice(0, 3).forEach(q => {
                console.log(`    - "${q.title}" by ${q.user?.name || 'Unknown'} (User Level: ${q.user?.level || 'Unknown'})`);
            });
        });

        // Test filtering for each user level
        const testUsers = [
            { user: primaryUser, levelName: 'Primary' },
            { user: secondaryUser, levelName: 'Secondary' },
            { user: advanceUser, levelName: 'Advance' }
        ];

        for (const { user, levelName } of testUsers) {
            if (!user) {
                console.log(`\n❌ No ${levelName} user found, skipping tests`);
                continue;
            }

            console.log(`\n🧪 Testing ${levelName} User: ${user.name}`);
            
            const userLevel = user.level ? user.level.toLowerCase() : 'primary';
            let filter = {};

            // Apply the same filtering logic as the backend
            if (userLevel === "secondary") {
                filter = {
                    $or: [
                        { level: "secondary" },
                        { level: "Secondary" }
                    ]
                };
            } else if (userLevel === "advance") {
                filter = {
                    $or: [
                        { level: "advance" },
                        { level: "Advance" }
                    ]
                };
            } else {
                // Primary level - exclude secondary and advance
                filter = {
                    level: {
                        $nin: ["secondary", "advance", "Secondary", "Advance"]
                    }
                };
            }

            console.log(`  🔍 Filter applied:`, JSON.stringify(filter, null, 2));

            const userQuestions = await ForumQuestion.find(filter).populate('user');
            console.log(`  📊 Questions returned: ${userQuestions.length}`);

            // Check if all returned questions are appropriate for this level
            const inappropriateQuestions = userQuestions.filter(q => {
                const qLevel = (q.level || 'primary').toLowerCase();
                if (userLevel === 'primary') {
                    return qLevel === 'secondary' || qLevel === 'advance';
                } else if (userLevel === 'secondary') {
                    return qLevel !== 'secondary';
                } else if (userLevel === 'advance') {
                    return qLevel !== 'advance';
                }
                return false;
            });

            if (inappropriateQuestions.length > 0) {
                console.log(`  ❌ LEVEL CONTAMINATION DETECTED! ${inappropriateQuestions.length} inappropriate questions:`);
                inappropriateQuestions.forEach(q => {
                    console.log(`    - "${q.title}" (Level: ${q.level}) - Should not be visible to ${userLevel} user`);
                });
            } else {
                console.log(`  ✅ Level separation working correctly`);
            }

            // Show sample questions
            if (userQuestions.length > 0) {
                console.log(`  📝 Sample questions (showing first 3):`);
                userQuestions.slice(0, 3).forEach(q => {
                    console.log(`    - "${q.title}" (Level: ${q.level}) by ${q.user?.name || 'Unknown'}`);
                });
            } else {
                console.log(`  📝 No questions available for ${userLevel} level`);
            }
        }

        console.log('\n🎯 Forum Level Separation Test Summary:');
        console.log('✅ Backend filtering logic tested');
        console.log('✅ Cross-level contamination checks completed');
        
    } catch (error) {
        console.error('❌ Error testing forum level separation:', error);
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    testForumLevelSeparation();
}

module.exports = testForumLevelSeparation;
