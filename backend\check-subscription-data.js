const mongoose = require('mongoose');
const User = require('./models/userModel');
const Subscription = require('./models/subscriptionModel');
require('dotenv').config();

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URL || 'mongodb://localhost:27017/brainwave', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const checkSubscriptionData = async () => {
  try {
    await connectDB();

    console.log('🔍 CHECKING ALL USER SUBSCRIPTIONS AND ACTIVE STATUS\n');
    console.log('=' .repeat(60));

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayString = today.toISOString().split('T')[0];

    console.log(`📅 Today's Date: ${todayString}`);
    console.log('=' .repeat(60));

    // Get all subscriptions with user and plan data
    const allSubscriptions = await Subscription.find({})
      .populate('user', 'firstName lastName username email phoneNumber subscriptionStatus subscriptionEndDate paymentRequired')
      .populate('activePlan', 'title duration discountedPrice')
      .sort({ createdAt: -1 });

    console.log(`📋 Total Subscription Records Found: ${allSubscriptions.length}\n`);

    // Analyze subscription data
    let activeSubscriptions = [];
    let expiredSubscriptions = [];
    let pendingSubscriptions = [];

    allSubscriptions.forEach((sub) => {
      if (!sub.user) {
        console.log('⚠️  Found subscription without user data');
        return;
      }

      const endDate = sub.endDate ? new Date(sub.endDate) : null;
      const isDateValid = endDate && endDate >= today;
      const isStatusActive = sub.status === 'active';
      const isPaymentPaid = sub.paymentStatus === 'paid';

      const subscriptionInfo = {
        user: sub.user,
        subscription: sub,
        endDate: endDate,
        endDateString: sub.endDate,
        isDateValid: isDateValid,
        isStatusActive: isStatusActive,
        isPaymentPaid: isPaymentPaid,
        daysRemaining: endDate ? Math.ceil((endDate - today) / (1000 * 60 * 60 * 24)) : null
      };

      if (isStatusActive && isPaymentPaid && isDateValid) {
        activeSubscriptions.push(subscriptionInfo);
      } else if (sub.status === 'expired' || (endDate && endDate < today)) {
        expiredSubscriptions.push(subscriptionInfo);
      } else {
        pendingSubscriptions.push(subscriptionInfo);
      }
    });

    // Display active subscriptions
    console.log(`✅ ACTIVE SUBSCRIPTIONS: ${activeSubscriptions.length}`);
    console.log('-' .repeat(50));

    if (activeSubscriptions.length === 0) {
      console.log('❌ NO ACTIVE SUBSCRIPTIONS FOUND\n');
    } else {
      activeSubscriptions.forEach((info, index) => {
        const user = info.user;
        const sub = info.subscription;
        console.log(`${index + 1}. ${user.firstName || ''} ${user.lastName || ''} (@${user.username})`);
        console.log(`   📧 Email: ${user.email || 'Not provided'}`);
        console.log(`   📱 Phone: ${user.phoneNumber || 'Not provided'}`);
        console.log(`   📋 Plan: ${sub.activePlan?.title || 'Unknown'}`);
        console.log(`   📅 End Date: ${info.endDateString}`);
        console.log(`   ⏰ Days Remaining: ${info.daysRemaining} days`);
        console.log(`   💳 Payment Status: ${sub.paymentStatus}`);
        console.log(`   📊 Status: ${sub.status}`);
        console.log(`   🆔 User ID: ${user._id}`);
        console.log('');
      });
    }

    // Display expired subscriptions (first 10)
    console.log(`❌ EXPIRED SUBSCRIPTIONS: ${expiredSubscriptions.length}`);
    console.log('-' .repeat(50));

    if (expiredSubscriptions.length === 0) {
      console.log('✅ No expired subscriptions found\n');
    } else {
      expiredSubscriptions.slice(0, 10).forEach((info, index) => {
        const user = info.user;
        const sub = info.subscription;
        console.log(`${index + 1}. ${user.firstName || ''} ${user.lastName || ''} (@${user.username})`);
        console.log(`   📅 End Date: ${info.endDateString}`);
        console.log(`   ⏰ Days Expired: ${Math.abs(info.daysRemaining || 0)} days ago`);
        console.log(`   📋 Plan: ${sub.activePlan?.title || 'Unknown'}`);
        console.log(`   📊 Status: ${sub.status}`);
        console.log('');
      });

      if (expiredSubscriptions.length > 10) {
        console.log(`   ... and ${expiredSubscriptions.length - 10} more expired subscriptions\n`);
      }
    }

    // Display pending subscriptions (first 5)
    console.log(`⏳ PENDING/OTHER SUBSCRIPTIONS: ${pendingSubscriptions.length}`);
    console.log('-' .repeat(50));

    if (pendingSubscriptions.length === 0) {
      console.log('✅ No pending subscriptions found\n');
    } else {
      pendingSubscriptions.slice(0, 5).forEach((info, index) => {
        const user = info.user;
        const sub = info.subscription;
        console.log(`${index + 1}. ${user.firstName || ''} ${user.lastName || ''} (@${user.username})`);
        console.log(`   📊 Status: ${sub.status}`);
        console.log(`   💳 Payment: ${sub.paymentStatus}`);
        console.log(`   📅 End Date: ${info.endDateString || 'Not set'}`);
        console.log('');
      });

      if (pendingSubscriptions.length > 5) {
        console.log(`   ... and ${pendingSubscriptions.length - 5} more pending subscriptions\n`);
      }
    }

    // Summary
    const totalSubscriptions = activeSubscriptions.length + expiredSubscriptions.length + pendingSubscriptions.length;
    const activePercentage = totalSubscriptions > 0 ? ((activeSubscriptions.length / totalSubscriptions) * 100).toFixed(1) : 0;

    console.log('📊 SUBSCRIPTION SUMMARY');
    console.log('=' .repeat(60));
    console.log(`Total Subscription Records: ${totalSubscriptions}`);
    console.log(`Active Subscriptions: ${activeSubscriptions.length}`);
    console.log(`Expired Subscriptions: ${expiredSubscriptions.length}`);
    console.log(`Pending Subscriptions: ${pendingSubscriptions.length}`);
    console.log(`Active Subscription Rate: ${activePercentage}%`);

    // Quick active users list
    if (activeSubscriptions.length > 0) {
      console.log('\n📋 QUICK ACTIVE USERS LIST:');
      console.log('-' .repeat(40));
      activeSubscriptions.forEach((info, index) => {
        const user = info.user;
        console.log(`${index + 1}. ${user.firstName || ''} ${user.lastName || ''} - Expires: ${info.endDateString} (${info.daysRemaining} days)`);
      });
    }

    // Get total users for context
    const totalUsers = await User.countDocuments({ isAdmin: { $ne: true } });
    const usersWithSubscriptions = new Set(allSubscriptions.map(sub => sub.user?._id?.toString())).size;

    console.log('\n👥 USER CONTEXT:');
    console.log('-' .repeat(40));
    console.log(`Total Users (non-admin): ${totalUsers}`);
    console.log(`Users with Subscription Records: ${usersWithSubscriptions}`);
    console.log(`Users without Subscriptions: ${totalUsers - usersWithSubscriptions}`);
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Check failed:', error);
    process.exit(1);
  }
};

checkSubscriptionData();
