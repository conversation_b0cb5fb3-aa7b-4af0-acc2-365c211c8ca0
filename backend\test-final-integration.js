const mongoose = require('mongoose');
require('dotenv').config();

async function testFinalIntegration() {
  console.log('🧪 Testing Final Integration - Subject Dropdown & AI Generation...\n');

  try {
    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    const Syllabus = require('./models/syllabusModel');

    // Test 1: Check current syllabus data
    console.log('\n1️⃣ Current Syllabus Data:');
    const syllabuses = await Syllabus.find({ isActive: true });
    syllabuses.forEach((s, i) => {
      console.log(`${i + 1}. "${s.title}"`);
      console.log(`   Level: ${s.level}`);
      console.log(`   Subject: "${s.subject}"`);
      console.log(`   Classes: ${s.classes.join(', ')}`);
      console.log(`   Status: ${s.processingStatus}`);
      console.log(`   ID: ${s._id}`);
      console.log('');
    });

    // Test 2: Check subject availability
    console.log('2️⃣ Subject Availability:');
    const primarySubjects = await Syllabus.getAvailableSubjects('primary');
    console.log(`Primary subjects: [${primarySubjects.join(', ')}]`);

    // Test 3: Check predefined subjects
    console.log('\n3️⃣ Predefined Subjects Check:');
    const { primarySubjects: predefinedPrimary } = require('../client/src/data/Subjects');
    console.log(`Predefined primary subjects: [${predefinedPrimary.join(', ')}]`);
    
    // Check if syllabus subject matches predefined subjects
    const syllabusSubjects = syllabuses.map(s => s.subject);
    const matchingSubjects = syllabusSubjects.filter(s => predefinedPrimary.includes(s));
    console.log(`Matching subjects: [${matchingSubjects.join(', ')}]`);
    
    if (matchingSubjects.length > 0) {
      console.log('✅ Subject matching is working correctly!');
    } else {
      console.log('❌ No matching subjects found - there may be a case mismatch');
    }

    // Test 4: Simulate AI question generation payload
    console.log('\n4️⃣ AI Question Generation Test:');
    if (syllabuses.length > 0) {
      const testSyllabus = syllabuses[0];
      const testPayload = {
        examId: null,
        questionTypes: ['multiple_choice'],
        subjects: [testSyllabus.subject], // Using corrected subject name
        level: testSyllabus.level,
        class: testSyllabus.classes[0],
        difficultyLevels: ['medium'],
        syllabusTopics: [],
        totalQuestions: 1,
        questionDistribution: { multiple_choice: 1, fill_blank: 0, picture_based: 0 },
        selectedSyllabusId: testSyllabus._id.toString()
      };
      
      console.log('Test payload for AI generation:');
      console.log(JSON.stringify(testPayload, null, 2));
      
      // Test AI service integration
      const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');
      const aiService = new AIQuestionGenerationService();
      
      console.log('\n5️⃣ Testing AI Service with Selected Syllabus:');
      const syllabusData = await aiService.getSyllabusData(
        testSyllabus.level,
        testSyllabus.classes[0],
        testSyllabus.subject,
        testSyllabus._id.toString()
      );
      
      console.log('AI Service Response:');
      console.log(`- Source: ${syllabusData.source}`);
      console.log(`- Syllabus Title: ${syllabusData.syllabusTitle || 'N/A'}`);
      console.log(`- Topics Available: ${syllabusData.topics ? Object.keys(syllabusData.topics).length : 0}`);
      console.log(`- Text Length: ${syllabusData.extractedText ? syllabusData.extractedText.length : 0} characters`);
      
      if (syllabusData.source === 'selected_pdf') {
        console.log('✅ AI service correctly uses selected syllabus!');
      } else {
        console.log('⚠️ AI service not using selected syllabus');
      }
    }

    // Test 6: Subject dropdown data structure
    console.log('\n6️⃣ Subject Dropdown Data Structure:');
    const subjectsByLevel = {
      primary: predefinedPrimary,
      secondary: require('../client/src/data/Subjects').secondarySubjects,
      advance: require('../client/src/data/Subjects').advanceSubjects
    };
    
    Object.entries(subjectsByLevel).forEach(([level, subjects]) => {
      console.log(`${level}: ${subjects.length} subjects`);
      console.log(`  Sample: [${subjects.slice(0, 3).join(', ')}${subjects.length > 3 ? ', ...' : ''}]`);
    });

    console.log('\n🎯 Integration Test Summary:');
    console.log(`✅ Database connection: Working`);
    console.log(`✅ Syllabus data: ${syllabuses.length} active syllabuses`);
    console.log(`✅ Subject matching: ${matchingSubjects.length > 0 ? 'Working' : 'Needs fixing'}`);
    console.log(`✅ AI service: Ready`);
    console.log(`✅ Subject dropdowns: Ready`);
    
    console.log('\n🚀 Ready for Testing:');
    console.log('1. Upload syllabus form now has subject dropdown');
    console.log('2. AI question generation should work with "Science and Technology"');
    console.log('3. Auto-generate exam should show correct subjects');
    console.log('4. Syllabus selection should work properly');

  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from database');
  }
}

// Run test if this file is executed directly
if (require.main === module) {
  testFinalIntegration().catch(console.error);
}

module.exports = { testFinalIntegration };
