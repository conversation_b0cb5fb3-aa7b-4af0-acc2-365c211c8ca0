const mongoose = require('mongoose');
require('dotenv').config();
const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');

async function testAIFix() {
  try {
    console.log('🧪 Testing AI PDF content fix...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    const aiService = new AIQuestionGenerationService();
    
    // Test with a known syllabus ID
    const testSyllabusId = '6865aa99b4448a485a03b86f';
    
    console.log('📝 Testing prompt building with PDF content...');
    const prompt = await aiService.buildMultipleChoicePrompt(
      'primary',
      '3',
      'Science and Technology',
      'medium',
      [],
      testSyllabusId
    );
    
    console.log('Generated prompt preview (first 800 characters):');
    console.log(prompt.substring(0, 800) + '...');
    console.log('');
    
    // Check if PDF content is included in prompt
    if (prompt.includes('SYLLABUS CONTENT FROM PDF:')) {
      console.log('✅ PDF content is properly included in the prompt');
      console.log('🎉 Fix successful!');
    } else {
      console.log('⚠️ PDF content is still not included in the prompt');
      console.log('❌ Fix needs more work');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
}

testAIFix();
