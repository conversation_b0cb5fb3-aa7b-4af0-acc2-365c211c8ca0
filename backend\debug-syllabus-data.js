const mongoose = require('mongoose');
require('dotenv').config();

async function debugSyllabusData() {
  console.log('🔍 Debugging Syllabus Data...\n');

  try {
    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to database');

    const Syllabus = require('./models/syllabusModel');

    // Check if there are any syllabuses in the database
    console.log('\n1️⃣ Checking all syllabuses in database...');
    const allSyllabuses = await Syllabus.find({});
    console.log(`Found ${allSyllabuses.length} syllabuses in database`);

    if (allSyllabuses.length > 0) {
      console.log('\n📚 Syllabus details:');
      allSyllabuses.forEach((syllabus, index) => {
        console.log(`${index + 1}. ${syllabus.title}`);
        console.log(`   Level: ${syllabus.level}`);
        console.log(`   Classes: ${Array.isArray(syllabus.classes) ? syllabus.classes.join(', ') : syllabus.classes}`);
        console.log(`   Subject: ${syllabus.subject}`);
        console.log(`   Status: ${syllabus.processingStatus}`);
        console.log(`   Active: ${syllabus.isActive}`);
        console.log(`   Extracted Text Length: ${syllabus.extractedText ? syllabus.extractedText.length : 0}`);
        console.log(`   Created: ${syllabus.createdAt}`);
        console.log('');
      });
    }

    // Test getAvailableSubjects method
    console.log('\n2️⃣ Testing getAvailableSubjects method...');
    const levels = ['primary', 'secondary', 'advance'];
    
    for (const level of levels) {
      try {
        const subjects = await Syllabus.getAvailableSubjects(level);
        console.log(`${level}: ${subjects.length} subjects - [${subjects.join(', ')}]`);
      } catch (error) {
        console.log(`${level}: Error - ${error.message}`);
      }
    }

    // Test findForAIGeneration method
    console.log('\n3️⃣ Testing findForAIGeneration method...');
    const testCases = [
      { level: 'primary', class: '5', subject: 'Mathematics' },
      { level: 'primary', class: '6', subject: 'Mathematics' },
      { level: 'secondary', class: 'I', subject: 'Physics' },
    ];

    for (const testCase of testCases) {
      try {
        const result = await Syllabus.findForAIGeneration(testCase.level, testCase.class, testCase.subject);
        if (result) {
          console.log(`✅ Found syllabus for ${testCase.level} Class ${testCase.class} ${testCase.subject}`);
          console.log(`   Title: ${result.title}`);
          console.log(`   Covers classes: ${result.classes.join(', ')}`);
        } else {
          console.log(`❌ No syllabus found for ${testCase.level} Class ${testCase.class} ${testCase.subject}`);
        }
      } catch (error) {
        console.log(`❌ Error testing ${testCase.level} Class ${testCase.class} ${testCase.subject}: ${error.message}`);
      }
    }

    // Check processing status distribution
    console.log('\n4️⃣ Processing status distribution...');
    const statusCounts = await Syllabus.aggregate([
      {
        $group: {
          _id: '$processingStatus',
          count: { $sum: 1 }
        }
      }
    ]);
    
    statusCounts.forEach(status => {
      console.log(`${status._id}: ${status.count}`);
    });

    // Check active vs inactive
    console.log('\n5️⃣ Active vs Inactive syllabuses...');
    const activeCount = await Syllabus.countDocuments({ isActive: true });
    const inactiveCount = await Syllabus.countDocuments({ isActive: false });
    console.log(`Active: ${activeCount}`);
    console.log(`Inactive: ${inactiveCount}`);

  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error(error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from database');
  }
}

// Test API endpoints
async function testAPIEndpoints() {
  console.log('\n🌐 Testing API Endpoints...\n');
  
  const axios = require('axios');
  const baseURL = 'http://localhost:5000';

  try {
    // Test syllabus subjects endpoint (should work with auth)
    console.log('1️⃣ Testing syllabus subjects endpoint...');
    try {
      const response = await axios.get(`${baseURL}/api/syllabus/subjects/primary`);
      console.log('❌ Should require authentication');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Syllabus subjects endpoint requires authentication (correct)');
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.message);
      }
    }

    // Test AI questions subjects endpoint (should work with auth)
    console.log('\n2️⃣ Testing AI questions subjects endpoint...');
    try {
      const response = await axios.get(`${baseURL}/api/ai-questions/subjects/primary`);
      console.log('❌ Should require authentication');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ AI questions subjects endpoint requires authentication (correct)');
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.message);
      }
    }

    console.log('\n📋 API Endpoints Summary:');
    console.log('✅ Both endpoints properly require authentication');
    console.log('🔧 Need to test with valid authentication token');

  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
}

// Run all debug functions
async function runAllDebug() {
  console.log('🚀 Starting Syllabus Debug Session\n');
  console.log('=' .repeat(60));
  
  await debugSyllabusData();
  await testAPIEndpoints();
  
  console.log('\n' + '=' .repeat(60));
  console.log('🏁 Debug session completed!');
}

// Run debug if this file is executed directly
if (require.main === module) {
  runAllDebug().catch(console.error);
}

module.exports = {
  debugSyllabusData,
  testAPIEndpoints,
  runAllDebug
};
