const User = require('../models/userModel');
const enhancedXPService = require('./enhancedXPService');

class InactivityPenaltyService {
  constructor() {
    this.penaltyAmount = 2; // 2 XP per day of inactivity
    this.maxConsecutiveDays = 7; // Maximum days to apply penalty
  }

  /**
   * Check and apply inactivity penalties for all users
   */
  async checkInactivityPenalties() {
    try {
      console.log('🔍 Checking for user inactivity penalties...');
      
      const users = await User.find({
        isBlocked: false,
        totalXP: { $gt: 0 } // Only users with XP to deduct
      });

      let penaltiesApplied = 0;
      const today = new Date();
      
      for (const user of users) {
        const penalty = await this.calculateInactivityPenalty(user, today);
        
        if (penalty.shouldApplyPenalty) {
          await this.applyInactivityPenalty(user, penalty, today);
          penaltiesApplied++;
        }
      }

      console.log(`✅ Applied inactivity penalties to ${penaltiesApplied} users`);
      return { penaltiesApplied };

    } catch (error) {
      console.error('❌ Error checking inactivity penalties:', error);
      throw error;
    }
  }

  /**
   * Calculate inactivity penalty for a user
   */
  async calculateInactivityPenalty(user, currentDate) {
    try {
      // Get user's last activity date
      const lastActivityDate = this.getLastActivityDate(user);
      
      if (!lastActivityDate) {
        return { shouldApplyPenalty: false, reason: 'No activity tracking data' };
      }

      // Calculate days since last activity
      const daysSinceActivity = this.calculateDaysDifference(lastActivityDate, currentDate);
      
      // Apply penalty if inactive for more than 1 day
      if (daysSinceActivity > 1) {
        const consecutiveDays = Math.min(daysSinceActivity - 1, this.maxConsecutiveDays);
        const totalPenalty = consecutiveDays * this.penaltyAmount;
        
        return {
          shouldApplyPenalty: true,
          daysSinceActivity,
          consecutiveDays,
          penaltyPerDay: this.penaltyAmount,
          totalPenalty,
          lastActivityDate
        };
      }

      return { shouldApplyPenalty: false, reason: 'User is active' };

    } catch (error) {
      console.error('Error calculating inactivity penalty:', error);
      return { shouldApplyPenalty: false, reason: 'Calculation error' };
    }
  }

  /**
   * Apply inactivity penalty to user
   */
  async applyInactivityPenalty(user, penalty, currentDate) {
    try {
      const result = await enhancedXPService.deductXP({
        userId: user._id,
        xpAmount: penalty.totalPenalty,
        reason: 'inactivity_penalty',
        metadata: {
          daysSinceActivity: penalty.daysSinceActivity,
          consecutiveDays: penalty.consecutiveDays,
          penaltyPerDay: penalty.penaltyPerDay,
          lastActivityDate: penalty.lastActivityDate,
          penaltyDate: currentDate
        }
      });

      // Update user's last penalty date to avoid duplicate penalties
      if (!user.activityTracking) {
        user.activityTracking = {};
      }
      
      user.activityTracking.lastInactivityPenalty = currentDate;
      await user.save();

      console.log(`⚠️ Applied ${penalty.totalPenalty} XP inactivity penalty to user ${user.name} (${penalty.daysSinceActivity} days inactive)`);

      return result;

    } catch (error) {
      console.error('Error applying inactivity penalty:', error);
      throw error;
    }
  }

  /**
   * Get user's last activity date
   */
  getLastActivityDate(user) {
    const dates = [];
    
    // Check various activity dates
    if (user.activityTracking?.lastLoginDate) {
      dates.push(new Date(user.activityTracking.lastLoginDate));
    }
    
    if (user.xpStats?.lastXPGain) {
      dates.push(new Date(user.xpStats.lastXPGain));
    }
    
    if (user.lastSeen) {
      dates.push(new Date(user.lastSeen));
    }
    
    if (user.updatedAt) {
      dates.push(new Date(user.updatedAt));
    }

    // Return the most recent date
    return dates.length > 0 ? new Date(Math.max(...dates)) : null;
  }

  /**
   * Calculate difference in days between two dates
   */
  calculateDaysDifference(date1, date2) {
    const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
    const diffDays = Math.round(Math.abs((date2 - date1) / oneDay));
    return diffDays;
  }

  /**
   * Check if penalty was already applied today
   */
  async wasPenaltyAppliedToday(user, currentDate) {
    if (!user.activityTracking?.lastInactivityPenalty) {
      return false;
    }

    const lastPenaltyDate = new Date(user.activityTracking.lastInactivityPenalty);
    const today = new Date(currentDate);
    
    return lastPenaltyDate.toDateString() === today.toDateString();
  }

  /**
   * Get inactivity statistics
   */
  async getInactivityStats() {
    try {
      const users = await User.find({ isBlocked: false });
      const today = new Date();
      
      let activeUsers = 0;
      let inactiveUsers = 0;
      let eligibleForPenalty = 0;

      for (const user of users) {
        const lastActivity = this.getLastActivityDate(user);
        
        if (lastActivity) {
          const daysSinceActivity = this.calculateDaysDifference(lastActivity, today);
          
          if (daysSinceActivity <= 1) {
            activeUsers++;
          } else {
            inactiveUsers++;
            if (user.totalXP > 0) {
              eligibleForPenalty++;
            }
          }
        }
      }

      return {
        totalUsers: users.length,
        activeUsers,
        inactiveUsers,
        eligibleForPenalty
      };

    } catch (error) {
      console.error('Error getting inactivity stats:', error);
      throw error;
    }
  }
}

module.exports = new InactivityPenaltyService();
