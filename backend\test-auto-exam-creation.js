const mongoose = require("mongoose");
require("dotenv").config();

async function testAutoExamCreation() {
  try {
    await mongoose.connect(process.env.MONGO_URL);
    console.log("✅ Connected to MongoDB");

    const User = require("./models/userModel");
    const Exam = require("./models/examModel");
    const { AIQuestionGeneration } = require("./models/aiQuestionGenerationModel");
    const aiService = require("./services/aiQuestionGenerationService");

    // Find an admin user
    const adminUser = await User.findOne({ isAdmin: true });
    if (!adminUser) {
      console.log("❌ No admin user found");
      return;
    }
    console.log(`✅ Found admin user: ${adminUser.name}`);

    // Count exams before
    const examsBefore = await Exam.countDocuments();
    console.log(`📊 Exams before: ${examsBefore}`);

    // Test parameters for standalone question generation (no examId)
    const generationParams = {
      questionTypes: ["multiple_choice"],
      subjects: ["Mathematics"],
      level: "primary",
      class: "3",
      difficultyLevels: ["medium"],
      syllabusTopics: ["Numbers"],
      totalQuestions: 2,
      questionDistribution: {
        multiple_choice: 2
      }
    };

    console.log("🚀 Testing AI question generation without examId...");
    
    const aiServiceInstance = new aiService();
    const result = await aiServiceInstance.generateQuestions(
      generationParams,
      adminUser._id,
      null // No examId - should trigger auto exam creation
    );

    console.log("📋 Generation result:", {
      success: result.success,
      examId: result.examId,
      questionsGenerated: result.questions?.length || 0,
      generationTime: result.generationTime
    });

    // Count exams after
    const examsAfter = await Exam.countDocuments();
    console.log(`📊 Exams after: ${examsAfter}`);

    if (examsAfter > examsBefore) {
      console.log("✅ Auto exam creation successful!");
      
      // Find the newly created exam
      if (result.examId) {
        const newExam = await Exam.findById(result.examId);
        if (newExam) {
          console.log(`📝 Created exam: ${newExam.name}`);
          console.log(`   - Level: ${newExam.level}`);
          console.log(`   - Class: ${newExam.class}`);
          console.log(`   - Subject: ${newExam.subject}`);
          console.log(`   - Questions: ${newExam.questions.length}`);
        }
      }
    } else {
      console.log("❌ No new exam was created");
    }

    // Check the generation record
    if (result.generationId) {
      const generation = await AIQuestionGeneration.findById(result.generationId);
      if (generation) {
        console.log(`📋 Generation record:`);
        console.log(`   - Status: ${generation.generationStatus}`);
        console.log(`   - ExamId: ${generation.examId}`);
        console.log(`   - Questions: ${generation.generatedQuestions.length}`);
      }
    }

    await mongoose.disconnect();
    console.log("✅ Test completed");

  } catch (error) {
    console.error("❌ Test failed:", error);
    await mongoose.disconnect();
  }
}

testAutoExamCreation();
