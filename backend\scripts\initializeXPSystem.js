const mongoose = require("mongoose");
const LevelDefinition = require("../models/levelDefinitionModel");
const AchievementDefinition = require("../models/achievementDefinitionModel");

// Level definitions with XP thresholds and benefits
const levelDefinitions = [
  {
    level: 1,
    xpRequired: 0,
    name: "<PERSON><PERSON><PERSON>",
    title: "New Student",
    description: "Welcome to your learning journey!",
    icon: "🌱",
    color: {
      primary: "#10B981",
      secondary: "#D1FAE5",
      gradient: ["#10B981", "#34D399"]
    },
    badge: { shape: "circle", animation: "none" },
    benefits: {
      xpMultiplier: 1.0,
      unlocks: ["Basic Features"],
      perks: [
        { name: "Learning Basics", description: "Access to all basic features", icon: "📚" }
      ]
    },
    rarity: "common",
    category: "beginner"
  },
  {
    level: 2,
    xpRequired: 100,
    name: "<PERSON><PERSON>",
    title: "<PERSON> Learner",
    description: "You're getting the hang of this!",
    icon: "📖",
    color: {
      primary: "#3B82F6",
      secondary: "#DBEAFE",
      gradient: ["#3B82F6", "#60A5FA"]
    },
    badge: { shape: "circle", animation: "pulse" },
    benefits: {
      xpMultiplier: 1.05,
      unlocks: ["Progress Tracking"],
      perks: [
        { name: "XP Boost", description: "5% bonus XP on all activities", icon: "⚡" }
      ]
    },
    rarity: "common",
    category: "beginner"
  },
  {
    level: 3,
    xpRequired: 250,
    name: "Student",
    title: "Dedicated Learner",
    description: "Your dedication is showing!",
    icon: "🎓",
    color: {
      primary: "#8B5CF6",
      secondary: "#EDE9FE",
      gradient: ["#8B5CF6", "#A78BFA"]
    },
    badge: { shape: "hexagon", animation: "pulse" },
    benefits: {
      xpMultiplier: 1.10,
      unlocks: ["Study Streaks", "Basic Achievements"],
      perks: [
        { name: "Streak Bonus", description: "Enhanced streak rewards", icon: "🔥" },
        { name: "XP Boost", description: "10% bonus XP on all activities", icon: "⚡" }
      ]
    },
    rarity: "uncommon",
    category: "intermediate"
  },
  {
    level: 4,
    xpRequired: 450,
    name: "Scholar",
    title: "Knowledge Seeker",
    description: "Your thirst for knowledge is admirable!",
    icon: "📜",
    color: {
      primary: "#F59E0B",
      secondary: "#FEF3C7",
      gradient: ["#F59E0B", "#FBBF24"]
    },
    badge: { shape: "star", animation: "glow" },
    benefits: {
      xpMultiplier: 1.15,
      unlocks: ["Advanced Analytics", "Subject Mastery Tracking"],
      perks: [
        { name: "Analytics Access", description: "Detailed performance insights", icon: "📊" },
        { name: "XP Boost", description: "15% bonus XP on all activities", icon: "⚡" }
      ]
    },
    rarity: "uncommon",
    category: "intermediate"
  },
  {
    level: 5,
    xpRequired: 700,
    name: "Expert",
    title: "Subject Master",
    description: "You've mastered the fundamentals!",
    icon: "🏆",
    color: {
      primary: "#EF4444",
      secondary: "#FEE2E2",
      gradient: ["#EF4444", "#F87171"]
    },
    badge: { shape: "crown", animation: "glow" },
    benefits: {
      xpMultiplier: 1.20,
      unlocks: ["Custom Themes", "Priority Support"],
      perks: [
        { name: "Theme Customization", description: "Unlock custom UI themes", icon: "🎨" },
        { name: "XP Boost", description: "20% bonus XP on all activities", icon: "⚡" }
      ]
    },
    rarity: "rare",
    category: "advanced"
  },
  {
    level: 6,
    xpRequired: 1000,
    name: "Master",
    title: "Learning Master",
    description: "Few reach this level of mastery!",
    icon: "👑",
    color: {
      primary: "#7C3AED",
      secondary: "#EDE9FE",
      gradient: ["#7C3AED", "#8B5CF6"]
    },
    badge: { shape: "crown", animation: "rotate" },
    benefits: {
      xpMultiplier: 1.25,
      unlocks: ["Mentor Features", "Advanced Customization"],
      perks: [
        { name: "Mentor Status", description: "Help guide other students", icon: "🤝" },
        { name: "XP Boost", description: "25% bonus XP on all activities", icon: "⚡" }
      ]
    },
    rarity: "epic",
    category: "expert"
  },
  {
    level: 7,
    xpRequired: 1350,
    name: "Grandmaster",
    title: "Academic Elite",
    description: "Your knowledge knows no bounds!",
    icon: "💎",
    color: {
      primary: "#059669",
      secondary: "#D1FAE5",
      gradient: ["#059669", "#10B981"]
    },
    badge: { shape: "diamond", animation: "bounce" },
    benefits: {
      xpMultiplier: 1.30,
      unlocks: ["Elite Features", "Special Recognition"],
      perks: [
        { name: "Elite Status", description: "Special recognition and privileges", icon: "⭐" },
        { name: "XP Boost", description: "30% bonus XP on all activities", icon: "⚡" }
      ]
    },
    rarity: "epic",
    category: "expert"
  },
  {
    level: 8,
    xpRequired: 1750,
    name: "Legend",
    title: "Learning Legend",
    description: "Legendary status achieved!",
    icon: "🌟",
    color: {
      primary: "#DC2626",
      secondary: "#FEE2E2",
      gradient: ["#DC2626", "#EF4444"]
    },
    badge: { shape: "star", animation: "rotate" },
    benefits: {
      xpMultiplier: 1.35,
      unlocks: ["Legendary Features", "Hall of Fame"],
      perks: [
        { name: "Hall of Fame", description: "Permanent recognition", icon: "🏛️" },
        { name: "XP Boost", description: "35% bonus XP on all activities", icon: "⚡" }
      ]
    },
    rarity: "legendary",
    category: "master"
  },
  {
    level: 9,
    xpRequired: 2200,
    name: "Champion",
    title: "Academic Champion",
    description: "Champion of learning!",
    icon: "🥇",
    color: {
      primary: "#F59E0B",
      secondary: "#FEF3C7",
      gradient: ["#F59E0B", "#FBBF24", "#F97316"]
    },
    badge: { shape: "crown", animation: "glow" },
    benefits: {
      xpMultiplier: 1.40,
      unlocks: ["Champion Features", "Exclusive Content"],
      perks: [
        { name: "Exclusive Access", description: "Access to champion-only content", icon: "🔐" },
        { name: "XP Boost", description: "40% bonus XP on all activities", icon: "⚡" }
      ]
    },
    rarity: "legendary",
    category: "master"
  },
  {
    level: 10,
    xpRequired: 2700,
    name: "Elite",
    title: "Academic Elite",
    description: "The pinnacle of academic achievement!",
    icon: "👑",
    color: {
      primary: "#7C2D12",
      secondary: "#FED7AA",
      gradient: ["#7C2D12", "#EA580C", "#F97316"]
    },
    badge: { shape: "diamond", animation: "rotate" },
    benefits: {
      xpMultiplier: 1.50,
      unlocks: ["All Features", "Ultimate Recognition"],
      perks: [
        { name: "Ultimate Status", description: "The highest achievement possible", icon: "👑" },
        { name: "XP Boost", description: "50% bonus XP on all activities", icon: "⚡" }
      ]
    },
    rarity: "mythic",
    category: "legend"
  }
];

// Achievement definitions
const achievementDefinitions = [
  // Learning Achievements
  {
    id: "first_steps",
    name: "First Steps",
    description: "Complete your first quiz",
    icon: "🎯",
    category: "learning",
    rarity: "common",
    xpReward: 50,
    color: { primary: "#10B981", gradient: ["#10B981", "#34D399"] },
    requirements: {
      quizzes: { total: 1 }
    }
  },
  {
    id: "quick_learner",
    name: "Quick Learner",
    description: "Complete 5 quizzes in one day",
    icon: "⚡",
    category: "learning",
    rarity: "uncommon",
    xpReward: 100,
    color: { primary: "#3B82F6", gradient: ["#3B82F6", "#60A5FA"] },
    requirements: {
      quizzes: { total: 5, timeframe: 1 }
    }
  },
  {
    id: "perfectionist",
    name: "Perfectionist",
    description: "Score 100% on 3 consecutive quizzes",
    icon: "💎",
    category: "learning",
    rarity: "rare",
    xpReward: 200,
    color: { primary: "#8B5CF6", gradient: ["#8B5CF6", "#A78BFA"] },
    requirements: {
      performance: { perfectScores: 3 },
      quizzes: { consecutive: true }
    }
  },
  
  // Streak Achievements
  {
    id: "on_fire",
    name: "On Fire",
    description: "Maintain a 7-day study streak",
    icon: "🔥",
    category: "streak",
    rarity: "uncommon",
    xpReward: 150,
    color: { primary: "#F59E0B", gradient: ["#F59E0B", "#FBBF24"] },
    requirements: {
      streak: { type: "daily", count: 7 }
    }
  },
  {
    id: "unstoppable",
    name: "Unstoppable",
    description: "Maintain a 30-day study streak",
    icon: "🚀",
    category: "streak",
    rarity: "epic",
    xpReward: 300,
    color: { primary: "#EF4444", gradient: ["#EF4444", "#F87171"] },
    requirements: {
      streak: { type: "daily", count: 30 }
    }
  },
  
  // Subject Mastery
  {
    id: "math_master",
    name: "Math Master",
    description: "Score 90%+ on 10 math quizzes",
    icon: "🧮",
    category: "subject",
    rarity: "rare",
    xpReward: 250,
    color: { primary: "#7C3AED", gradient: ["#7C3AED", "#8B5CF6"] },
    requirements: {
      quizzes: { total: 10, subject: "Mathematics", minScore: 90 }
    }
  },
  {
    id: "science_genius",
    name: "Science Genius",
    description: "Score 90%+ on 10 science quizzes",
    icon: "🔬",
    category: "subject",
    rarity: "rare",
    xpReward: 250,
    color: { primary: "#059669", gradient: ["#059669", "#10B981"] },
    requirements: {
      quizzes: { total: 10, subject: "Science", minScore: 90 }
    }
  },
  
  // Social Achievements
  {
    id: "top_performer",
    name: "Top Performer",
    description: "Reach top 10 in class ranking",
    icon: "👑",
    category: "social",
    rarity: "epic",
    xpReward: 500,
    color: { primary: "#DC2626", gradient: ["#DC2626", "#EF4444"] },
    requirements: {
      special: { topPercentile: 10 }
    }
  },
  {
    id: "helping_hand",
    name: "Helping Hand",
    description: "Help 5 classmates improve their scores",
    icon: "🤝",
    category: "social",
    rarity: "uncommon",
    xpReward: 200,
    color: { primary: "#8B5CF6", gradient: ["#8B5CF6", "#A78BFA"] },
    requirements: {
      social: { helpOthers: 5 }
    }
  }
];

async function initializeXPSystem() {
  try {
    console.log("🚀 Initializing XP System...");
    
    // Clear existing data
    await LevelDefinition.deleteMany({});
    await AchievementDefinition.deleteMany({});
    
    console.log("📊 Creating level definitions...");
    await LevelDefinition.insertMany(levelDefinitions);
    
    console.log("🏆 Creating achievement definitions...");
    await AchievementDefinition.insertMany(achievementDefinitions);
    
    console.log("✅ XP System initialized successfully!");
    console.log(`Created ${levelDefinitions.length} levels and ${achievementDefinitions.length} achievements`);
    
  } catch (error) {
    console.error("❌ Error initializing XP system:", error);
    throw error;
  }
}

module.exports = { initializeXPSystem, levelDefinitions, achievementDefinitions };
