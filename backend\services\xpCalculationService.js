const User = require("../models/userModel");
const XPTransaction = require("../models/xpTransactionModel");
const LevelDefinition = require("../models/levelDefinitionModel");
const AchievementDefinition = require("../models/achievementDefinitionModel");

class XPCalculationService {
  constructor() {
    // Base XP configuration
    this.baseXPPerCorrectAnswer = 15;
    
    // Difficulty multipliers
    this.difficultyMultipliers = {
      'easy': 1.0,
      'medium': 1.3,
      'hard': 1.6,
      'expert': 2.0,
      'beginner': 0.8,
      'intermediate': 1.2,
      'advanced': 1.5,
    };
    
    // Question type multipliers
    this.questionTypeMultipliers = {
      'multiple-choice': 1.0,
      'fill-in-blank': 1.2,
      'true-false': 0.8,
      'image-based': 1.3,
      'essay': 1.5,
    };
    
    // Bonus configurations
    this.bonusConfig = {
      perfectScore: 0.5, // 50% bonus for perfect score
      speedBonus: {
        maxBonus: 0.3, // 30% max speed bonus
        timeThreshold: 0.5, // Must complete in less than 50% of allowed time
      },
      streakBonus: 5, // 5 XP per streak count
      firstAttemptBonus: 0.2, // 20% bonus for first attempt
      levelMultiplierBase: 0.1, // 10% per level above 1
    };
  }

  /**
   * Calculate XP for quiz completion
   */
  async calculateQuizXP({
    user,
    examData,
    questions,
    correctAnswers,
    wrongAnswers,
    timeSpent,
    totalTimeAllowed,
    isFirstAttempt = true,
    difficulty = 'medium'
  }) {
    try {
      const totalQuestions = questions.length;
      const correctCount = correctAnswers.length;
      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);
      
      // Base XP calculation
      const baseXP = correctCount * this.baseXPPerCorrectAnswer;
      
      // Difficulty bonus
      const difficultyMultiplier = this.difficultyMultipliers[difficulty] || 1.0;
      const difficultyBonus = baseXP * (difficultyMultiplier - 1);
      
      // Perfect score bonus
      const perfectScoreBonus = correctCount === totalQuestions ? 
        baseXP * this.bonusConfig.perfectScore : 0;
      
      // Speed bonus calculation
      const speedBonus = this.calculateSpeedBonus(
        baseXP, 
        timeSpent, 
        totalTimeAllowed
      );
      
      // Streak bonus
      const streakBonus = (user.currentStreak || 0) * this.bonusConfig.streakBonus;
      
      // First attempt bonus
      const firstAttemptBonus = isFirstAttempt ? 
        baseXP * this.bonusConfig.firstAttemptBonus : 0;
      
      // Level multiplier
      const levelMultiplier = 1 + ((user.currentLevel - 1) * this.bonusConfig.levelMultiplierBase);
      
      // Calculate total XP
      const totalBonuses = difficultyBonus + perfectScoreBonus + speedBonus + 
                          streakBonus + firstAttemptBonus;
      const finalXP = Math.round((baseXP + totalBonuses) * levelMultiplier);
      
      // Create breakdown for transparency
      const breakdown = {
        baseXP: Math.round(baseXP),
        difficultyBonus: Math.round(difficultyBonus),
        perfectScoreBonus: Math.round(perfectScoreBonus),
        speedBonus: Math.round(speedBonus),
        streakBonus: Math.round(streakBonus),
        firstAttemptBonus: Math.round(firstAttemptBonus),
        levelMultiplier: Math.round(levelMultiplier * 100) / 100,
        totalBonuses: Math.round(totalBonuses),
        finalXP: finalXP,
      };
      
      return {
        xpAwarded: finalXP,
        breakdown: breakdown,
        scorePercentage: scorePercentage,
        metadata: {
          difficulty: difficulty,
          questionsTotal: totalQuestions,
          questionsCorrect: correctCount,
          timeSpent: timeSpent,
          isFirstAttempt: isFirstAttempt,
        }
      };
      
    } catch (error) {
      console.error('Error calculating quiz XP:', error);
      throw new Error('Failed to calculate XP');
    }
  }

  /**
   * Calculate speed bonus based on time efficiency
   */
  calculateSpeedBonus(baseXP, timeSpent, totalTimeAllowed) {
    if (!timeSpent || !totalTimeAllowed || timeSpent >= totalTimeAllowed) {
      return 0;
    }
    
    const timeRatio = timeSpent / totalTimeAllowed;
    const speedThreshold = this.bonusConfig.speedBonus.timeThreshold;
    
    if (timeRatio <= speedThreshold) {
      // Linear bonus calculation based on speed
      const speedEfficiency = (speedThreshold - timeRatio) / speedThreshold;
      return baseXP * this.bonusConfig.speedBonus.maxBonus * speedEfficiency;
    }
    
    return 0;
  }

  /**
   * Award XP to user and create transaction record
   */
  async awardXP({
    userId,
    xpAmount,
    transactionType,
    sourceId = null,
    sourceModel = null,
    breakdown = {},
    quizData = {},
    achievementData = {},
    metadata = {},
    adminNotes = null
  }) {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Store user state before transaction
      const userStateBefore = {
        levelBefore: user.currentLevel,
        xpBefore: user.totalXP,
        streakBefore: user.currentStreak,
      };

      // Update user XP
      user.totalXP = (user.totalXP || 0) + xpAmount;
      user.lifetimeXP = (user.lifetimeXP || 0) + xpAmount;
      user.seasonXP = (user.seasonXP || 0) + xpAmount;
      
      // Update XP statistics
      user.xpStats.lastXPGain = new Date();
      user.xpStats.dailyXP = (user.xpStats.dailyXP || 0) + xpAmount;
      user.xpStats.weeklyXP = (user.xpStats.weeklyXP || 0) + xpAmount;
      user.xpStats.monthlyXP = (user.xpStats.monthlyXP || 0) + xpAmount;
      
      if (user.totalQuizzesTaken > 0) {
        user.xpStats.averageXPPerQuiz = Math.round(user.lifetimeXP / user.totalQuizzesTaken);
      }
      
      if (xpAmount > (user.xpStats.bestXPGain || 0)) {
        user.xpStats.bestXPGain = xpAmount;
      }

      // Check for level up
      const levelUpResult = await this.checkLevelUp(user);

      // Save user using findByIdAndUpdate to avoid validation issues
      await User.findByIdAndUpdate(userId, {
        totalXP: user.totalXP,
        lifetimeXP: user.lifetimeXP,
        seasonXP: user.seasonXP,
        currentLevel: user.currentLevel,
        xpToNextLevel: user.xpToNextLevel,
        xpStats: user.xpStats
      }, { runValidators: false });

      // Create XP transaction record
      const transaction = new XPTransaction({
        user: userId,
        xpAmount: xpAmount,
        transactionType: transactionType,
        sourceId: sourceId,
        sourceModel: sourceModel,
        breakdown: breakdown,
        quizData: quizData,
        achievementData: achievementData,
        userStateAtTransaction: {
          ...userStateBefore,
          levelAfter: user.currentLevel,
          xpAfter: user.totalXP,
          streakAfter: user.currentStreak,
        },
        season: user.currentSeason,
        metadata: metadata,
        adminNotes: adminNotes,
      });

      await transaction.save();

      return {
        success: true,
        xpAwarded: xpAmount,
        newTotalXP: user.totalXP,
        levelUp: levelUpResult.leveledUp,
        newLevel: user.currentLevel,
        xpToNextLevel: user.xpToNextLevel,
        transaction: transaction,
        breakdown: breakdown,
      };

    } catch (error) {
      console.error('Error awarding XP:', error);
      throw new Error('Failed to award XP');
    }
  }

  /**
   * Check if user should level up and handle level progression
   */
  async checkLevelUp(user) {
    try {
      const currentLevel = await LevelDefinition.findOne({ level: user.currentLevel });
      if (!currentLevel) {
        // Initialize user at level 1 if no current level found
        user.currentLevel = 1;
        user.xpToNextLevel = 100;
        return { leveledUp: false };
      }

      // Check if user has enough XP for next level
      const nextLevel = await LevelDefinition.findOne({ 
        level: user.currentLevel + 1,
        isActive: true 
      });

      if (!nextLevel) {
        // User is at max level
        user.xpToNextLevel = 0;
        return { leveledUp: false };
      }

      if (user.totalXP >= nextLevel.xpRequired) {
        // Level up!
        const oldLevel = user.currentLevel;
        user.currentLevel = nextLevel.level;
        
        // Calculate XP to next level
        const levelAfterNext = await LevelDefinition.findOne({ 
          level: nextLevel.level + 1,
          isActive: true 
        });
        
        if (levelAfterNext) {
          user.xpToNextLevel = levelAfterNext.xpRequired - user.totalXP;
        } else {
          user.xpToNextLevel = 0; // Max level reached
        }

        // Add to level history
        user.levelHistory.push({
          level: nextLevel.level,
          reachedAt: new Date(),
          xpAtLevel: user.totalXP,
        });

        // Check for level-based achievements
        await this.checkLevelAchievements(user, oldLevel, nextLevel.level);

        return { 
          leveledUp: true, 
          oldLevel: oldLevel, 
          newLevel: nextLevel.level,
          levelData: nextLevel 
        };
      } else {
        // Update XP to next level
        user.xpToNextLevel = nextLevel.xpRequired - user.totalXP;
        return { leveledUp: false };
      }

    } catch (error) {
      console.error('Error checking level up:', error);
      return { leveledUp: false };
    }
  }

  /**
   * Check for level-based achievements
   */
  async checkLevelAchievements(user, oldLevel, newLevel) {
    try {
      const levelAchievements = await AchievementDefinition.find({
        category: 'level',
        'requirements.level.minimum': { $lte: newLevel },
        'requirements.level.maximum': { $gte: newLevel },
        isActive: true,
      });

      for (const achievement of levelAchievements) {
        const alreadyEarned = user.achievements.some(a => a.id === achievement.id);
        if (!alreadyEarned) {
          await this.awardAchievement(user._id, achievement.id);
        }
      }
    } catch (error) {
      console.error('Error checking level achievements:', error);
    }
  }

  /**
   * Award achievement to user
   */
  async awardAchievement(userId, achievementId) {
    try {
      const achievement = await AchievementDefinition.findOne({ id: achievementId });
      if (!achievement) {
        throw new Error('Achievement not found');
      }

      const user = await User.findById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Check if already earned
      const alreadyEarned = user.achievements.some(a => a.id === achievementId);
      if (alreadyEarned && !achievement.tracking.isRepeatable) {
        return { success: false, reason: 'Already earned' };
      }

      // Add achievement to user
      user.achievements.push({
        id: achievement.id,
        name: achievement.name,
        description: achievement.description,
        icon: achievement.icon,
        xpReward: achievement.xpReward,
        rarity: achievement.rarity,
        category: achievement.category,
        earnedAt: new Date(),
      });

      // Save user using findByIdAndUpdate to avoid validation issues
      await User.findByIdAndUpdate(userId, {
        achievements: user.achievements
      }, { runValidators: false });

      // Award XP if applicable
      if (achievement.xpReward > 0) {
        await this.awardXP({
          userId: userId,
          xpAmount: achievement.xpReward,
          transactionType: 'achievement_unlock',
          achievementData: {
            achievementId: achievement.id,
            achievementName: achievement.name,
            rarity: achievement.rarity,
            category: achievement.category,
          },
        });
      }

      // Update achievement statistics
      achievement.stats.totalEarned = (achievement.stats.totalEarned || 0) + 1;
      achievement.stats.lastEarned = new Date();
      await achievement.save();

      return {
        success: true,
        achievement: achievement,
        xpAwarded: achievement.xpReward,
      };

    } catch (error) {
      console.error('Error awarding achievement:', error);
      throw new Error('Failed to award achievement');
    }
  }

  /**
   * Check and award quiz-related achievements
   */
  async checkQuizAchievements(userId, quizData) {
    try {
      const user = await User.findById(userId);
      if (!user) return;

      const achievements = await AchievementDefinition.find({
        category: { $in: ['learning', 'subject'] },
        isActive: true
      });

      for (const achievement of achievements) {
        const qualifies = await this.checkAchievementQualification(user, achievement, quizData);
        if (qualifies) {
          await this.awardAchievement(userId, achievement.id);
        }
      }
    } catch (error) {
      console.error('Error checking quiz achievements:', error);
    }
  }

  /**
   * Check if user qualifies for a specific achievement
   */
  async checkAchievementQualification(user, achievement, quizData = {}) {
    try {
      const req = achievement.requirements;

      // Check if already earned (and not repeatable)
      const alreadyEarned = user.achievements.some(a => a.id === achievement.id);
      if (alreadyEarned && !achievement.tracking?.isRepeatable) {
        return false;
      }

      // Check quiz-based requirements
      if (req.quizzes) {
        const Report = require("../models/reportModel");
        const reports = await Report.find({ user: user._id }).populate('exam');

        // Filter reports by subject if specified
        let relevantReports = reports;
        if (req.quizzes.subject) {
          relevantReports = reports.filter(r =>
            r.exam && r.exam.subject === req.quizzes.subject
          );
        }

        // Check total quiz requirement
        if (req.quizzes.total && relevantReports.length < req.quizzes.total) {
          return false;
        }

        // Check minimum score requirement
        if (req.quizzes.minScore) {
          const highScoreReports = relevantReports.filter(r =>
            r.result && r.result.score >= req.quizzes.minScore
          );
          if (req.quizzes.total && highScoreReports.length < req.quizzes.total) {
            return false;
          }
        }

        // Check timeframe requirement
        if (req.quizzes.timeframe) {
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - req.quizzes.timeframe);
          const recentReports = relevantReports.filter(r =>
            r.createdAt >= cutoffDate
          );
          if (req.quizzes.total && recentReports.length < req.quizzes.total) {
            return false;
          }
        }
      }

      // Check performance requirements
      if (req.performance) {
        if (req.performance.perfectScores) {
          const Report = require("../models/reportModel");
          const perfectReports = await Report.find({
            user: user._id,
            'result.score': 100
          });
          if (perfectReports.length < req.performance.perfectScores) {
            return false;
          }
        }

        if (req.performance.averageScore && user.averageScore < req.performance.averageScore) {
          return false;
        }
      }

      // Check streak requirements
      if (req.streak) {
        if (req.streak.type === 'daily' && user.currentStreak < req.streak.count) {
          return false;
        }
      }

      // Check level requirements
      if (req.level?.minimum && user.currentLevel < req.level.minimum) {
        return false;
      }

      return true;

    } catch (error) {
      console.error('Error checking achievement qualification:', error);
      return false;
    }
  }
}

module.exports = new XPCalculationService();
