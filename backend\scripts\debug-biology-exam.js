const mongoose = require('mongoose');
require('dotenv').config();

const Exam = require('../models/examModel');

async function debugBiologyExam() {
    try {
        console.log('🔄 Connecting to database...');
        await mongoose.connect(process.env.MONGO_URL);
        console.log('✅ Connected to database');

        // Find the biology exam in Form-1 secondary level
        console.log('\n🔍 Looking for biology exam in Form-1 secondary level...');
        
        const biologyExams = await Exam.find({
            $and: [
                { $or: [{ level: "secondary" }, { level: "Secondary" }] },
                { $or: [{ class: "Form-1" }, { class: "1" }] },
                { $or: [
                    { subject: { $regex: /biology/i } },
                    { name: { $regex: /biology/i } },
                    { category: { $regex: /biology/i } }
                ]}
            ]
        });

        console.log(`📊 Found ${biologyExams.length} biology exams in Form-1 secondary`);

        if (biologyExams.length === 0) {
            console.log('❌ No biology exams found. Let me check all secondary Form-1 exams...');
            
            const allForm1Exams = await Exam.find({
                $and: [
                    { $or: [{ level: "secondary" }, { level: "Secondary" }] },
                    { $or: [{ class: "Form-1" }, { class: "1" }] }
                ]
            });
            
            console.log(`📊 Found ${allForm1Exams.length} total exams in Form-1 secondary:`);
            allForm1Exams.forEach((exam, index) => {
                console.log(`  ${index + 1}. ${exam.name}`);
                console.log(`     Subject: ${exam.subject || 'N/A'}`);
                console.log(`     Category: ${exam.category || 'N/A'}`);
                console.log(`     Topic: ${exam.topic || 'N/A'}`);
                console.log(`     Level: ${exam.level}`);
                console.log(`     Class: ${exam.class}`);
                console.log('     ---');
            });
        } else {
            console.log('\n📝 Biology exam details:');
            biologyExams.forEach((exam, index) => {
                console.log(`\n🧬 Biology Exam ${index + 1}:`);
                console.log(`  Name: ${exam.name}`);
                console.log(`  Subject: ${exam.subject || 'N/A'}`);
                console.log(`  Category: ${exam.category || 'N/A'}`);
                console.log(`  Topic: ${exam.topic || 'N/A'}`);
                console.log(`  Level: ${exam.level}`);
                console.log(`  Class: ${exam.class}`);
                console.log(`  Duration: ${exam.duration} seconds`);
                console.log(`  Questions: ${exam.questions?.length || 0}`);
                console.log(`  Created: ${exam.createdAt}`);
                console.log(`  Updated: ${exam.updatedAt}`);
                
                // Check if topic field exists and what it contains
                console.log('\n🔍 Topic field analysis:');
                console.log(`  Topic exists: ${exam.topic !== undefined}`);
                console.log(`  Topic type: ${typeof exam.topic}`);
                console.log(`  Topic value: "${exam.topic}"`);
                console.log(`  Topic length: ${exam.topic?.length || 0}`);
                console.log(`  Topic is empty string: ${exam.topic === ''}`);
                console.log(`  Topic is null: ${exam.topic === null}`);
                console.log(`  Topic is undefined: ${exam.topic === undefined}`);
                console.log(`  Topic equals 'General': ${exam.topic === 'General'}`);
                
                // Show the raw document
                console.log('\n📄 Raw exam document:');
                console.log(JSON.stringify(exam.toObject(), null, 2));
            });
        }

        console.log('\n✅ Debug completed!');
        
    } catch (error) {
        console.error('❌ Error debugging biology exam:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 Disconnected from database');
    }
}

// Run the script
if (require.main === module) {
    debugBiologyExam();
}

module.exports = debugBiologyExam;
