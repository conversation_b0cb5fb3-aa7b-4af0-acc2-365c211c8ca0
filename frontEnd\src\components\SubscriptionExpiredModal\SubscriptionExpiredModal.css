/* Subscription Expired Modal Styles */
.subscription-expired-modal .ant-modal-content {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 25px 80px rgba(255, 77, 79, 0.3);
  border: none;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  padding: 0;
}

.subscription-expired-modal .ant-modal-body {
  padding: 0;
}

.expired-modal-content {
  background: white;
  border-radius: 20px;
  margin: 3px;
  overflow: hidden;
  min-height: 500px;
}

/* Expired Header */
.expired-header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  padding: 30px 30px 25px;
  text-align: center;
  color: white;
  position: relative;
}

.warning-icon {
  font-size: 56px;
  color: #fff3cd;
  margin-bottom: 15px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.modal-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.modal-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  font-weight: 400;
}

/* Expired Plan Card */
.expired-plan-card {
  margin: 25px;
  background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
  border-radius: 16px;
  padding: 25px;
  border: 2px solid #ffcccb;
  box-shadow: 0 8px 25px rgba(255, 107, 107, 0.1);
}

.plan-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.expired-icon {
  font-size: 24px;
  color: #ff4d4f;
  margin-right: 15px;
  background: rgba(255, 77, 79, 0.1);
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plan-info {
  flex: 1;
}

.plan-name {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 4px 0;
}

.expired-status {
  color: #ff4d4f;
  font-size: 14px;
  font-weight: 500;
  background: rgba(255, 77, 79, 0.1);
  padding: 4px 12px;
  border-radius: 20px;
}

/* Expiration Details */
.expiration-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 10px;
  border: 1px solid #ffcccb;
}

.detail-icon {
  font-size: 18px;
  color: #ff4d4f;
  margin-right: 12px;
}

.detail-content {
  display: flex;
  flex-direction: column;
}

.detail-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.detail-value {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
}

/* Progress Section */
.progress-section {
  margin-bottom: 0;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.progress-label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.expired-badge {
  font-size: 14px;
  font-weight: 600;
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
  padding: 4px 10px;
  border-radius: 12px;
}

/* Message Section */
.message-section {
  margin: 0 25px 25px;
}

.expired-message {
  background: linear-gradient(135deg, #fff2e6 0%, #ffe7d9 100%);
  border: 2px solid #ffb366;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  text-align: center;
}

.message-title {
  font-size: 18px;
  font-weight: 600;
  color: #d46b08;
  margin: 0 0 10px 0;
}

.message-text {
  font-size: 14px;
  color: #8c4a00;
  margin: 0;
  line-height: 1.6;
}

/* Restricted Access */
.restricted-access {
  background: #f9f9f9;
  border-radius: 12px;
  padding: 20px;
}

.restricted-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 15px 0;
}

.restricted-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.restricted-list li {
  font-size: 14px;
  color: #666;
  padding: 4px 0;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 12px;
  margin: 0 25px 25px;
}

.renew-button {
  flex: 2;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.renew-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.later-button {
  flex: 1;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  border: 2px solid #d9d9d9;
  transition: all 0.3s ease;
}

.later-button:hover {
  border-color: #ff6b6b;
  color: #ff6b6b;
  transform: translateY(-1px);
}

/* Footer Note */
.footer-note {
  background: #f0f8ff;
  padding: 15px 25px;
  border-top: 1px solid #e6f0ff;
  text-align: center;
}

.footer-note p {
  margin: 0;
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

/* Plan Selection Styles */
.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 30px 30px 25px;
  text-align: center;
  color: white;
  position: relative;
}

.crown-icon {
  font-size: 48px;
  color: #ffd700;
  margin-bottom: 15px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.plans-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 25px;
}

.plan-card {
  background: white;
  border: 2px solid #e8f2ff;
  border-radius: 16px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.plan-card:hover {
  border-color: #1890ff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.15);
}

.plan-card.selected {
  border-color: #1890ff;
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
  box-shadow: 0 8px 25px rgba(24, 144, 255, 0.2);
}

.plan-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 10px 0;
}

.plan-badge {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.plan-pricing {
  margin: 15px 0;
  text-align: center;
}

.price-main {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 5px;
}

.currency {
  font-size: 14px;
  color: #666;
  margin-right: 4px;
}

.amount {
  font-size: 24px;
  font-weight: 700;
  color: #1890ff;
}

.price-original {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 5px;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.discount {
  font-size: 12px;
  color: #52c41a;
  font-weight: 600;
}

.duration {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.plan-features {
  margin-top: 15px;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #333;
  margin-bottom: 8px;
}

.feature-icon {
  color: #1890ff;
  margin-right: 8px;
  font-size: 12px;
}

.selected-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #1890ff;
  color: white;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.continue-button {
  flex: 2;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.continue-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.continue-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.back-button {
  flex: 1;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  border: 2px solid #d9d9d9;
  transition: all 0.3s ease;
}

.back-button:hover {
  border-color: #1890ff;
  color: #1890ff;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .subscription-expired-modal .ant-modal-content {
    margin: 10px;
    border-radius: 16px;
  }
  
  .modal-header {
    padding: 25px 20px 20px;
  }
  
  .modal-title {
    font-size: 28px;
  }
  
  .expired-plan-card,
  .message-section,
  .plans-grid {
    margin: 20px;
  }
  
  .expiration-details,
  .restricted-list {
    grid-template-columns: 1fr;
  }
  
  .plans-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
    margin: 0 20px 20px;
  }
  
  .renew-button,
  .later-button,
  .continue-button,
  .back-button {
    flex: none;
  }
}
