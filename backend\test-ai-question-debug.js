const mongoose = require('mongoose');
const AIQuestionGenerationService = require('./services/aiQuestionGenerationService');
const { AIQuestionGeneration } = require('./models/aiQuestionGenerationModel');
const Question = require('./models/questionModel');
const Exam = require('./models/examModel');
require('dotenv').config();

async function debugAIQuestions() {
  try {
    console.log('🔍 Debugging AI Question Generation Issues...\n');
    
    // Connect to database
    await mongoose.connect(process.env.MONGO_URL);
    console.log('✅ Connected to MongoDB\n');
    
    // 1. Check existing AI-generated questions in database
    console.log('1️⃣ Checking existing AI-generated questions...');
    const aiQuestions = await Question.find({ isAIGenerated: true }).limit(5);
    console.log(`Found ${aiQuestions.length} AI-generated questions in database`);
    
    if (aiQuestions.length > 0) {
      console.log('\nSample AI-generated question structure:');
      const sample = aiQuestions[0];
      console.log({
        name: sample.name,
        answerType: sample.answerType,
        correctOption: sample.correctOption,
        correctAnswer: sample.correctAnswer,
        options: sample.options,
        questionType: sample.questionType,
        isAIGenerated: sample.isAIGenerated
      });
    }
    
    // 2. Check AI generation records
    console.log('\n2️⃣ Checking AI generation records...');
    const generations = await AIQuestionGeneration.find().sort({ createdAt: -1 }).limit(3);
    console.log(`Found ${generations.length} generation records`);
    
    if (generations.length > 0) {
      const latestGeneration = generations[0];
      console.log('\nLatest generation record:');
      console.log({
        status: latestGeneration.generationStatus,
        questionsGenerated: latestGeneration.generatedQuestions.length,
        approved: latestGeneration.generatedQuestions.filter(q => q.approved).length,
        examId: latestGeneration.examId
      });
      
      if (latestGeneration.generatedQuestions.length > 0) {
        const sampleGenerated = latestGeneration.generatedQuestions[0];
        console.log('\nSample generated question structure:');
        console.log({
          approved: sampleGenerated.approved,
          questionId: sampleGenerated.questionId,
          generatedContent: {
            name: sampleGenerated.generatedContent?.name,
            answerType: sampleGenerated.generatedContent?.answerType,
            correctOption: sampleGenerated.generatedContent?.correctOption,
            correctAnswer: sampleGenerated.generatedContent?.correctAnswer,
            options: sampleGenerated.generatedContent?.options,
            questionType: sampleGenerated.generatedContent?.questionType
          }
        });
      }
    }
    
    // 3. Test question generation process
    console.log('\n3️⃣ Testing question generation process...');
    const service = new AIQuestionGenerationService();
    
    const testParams = {
      questionTypes: ['multiple_choice'],
      subjects: ['Mathematics'],
      level: 'primary',
      class: '3',
      difficultyLevels: ['easy'],
      syllabusTopics: ['Numbers', 'Addition'],
      totalQuestions: 1,
      questionDistribution: {
        multiple_choice: 1,
        fill_blank: 0,
        picture_based: 0
      }
    };
    
    console.log('Generating test questions...');
    // Use a valid ObjectId for testing
    const testUserId = new mongoose.Types.ObjectId();
    const result = await service.generateQuestions(testParams, testUserId, null);
    
    if (result.success) {
      console.log('✅ Question generation successful');
      console.log('Generated questions:', result.questions.length);
      
      if (result.questions.length > 0) {
        const testQuestion = result.questions[0];
        console.log('\nGenerated question structure:');
        console.log({
          name: testQuestion.name,
          answerType: testQuestion.answerType,
          correctOption: testQuestion.correctOption,
          options: testQuestion.options,
          questionType: testQuestion.questionType,
          isAIGenerated: testQuestion.isAIGenerated
        });
        
        // 5. Test quiz marking logic simulation
        console.log('\n5️⃣ Testing quiz marking logic simulation...');
        
        // Simulate user selecting correct answer
        const correctAnswer = testQuestion.correctOption;
        const userSelectedOption = correctAnswer; // User selects correct answer
        
        console.log(`Question: ${testQuestion.name}`);
        console.log(`Correct Option: ${correctAnswer}`);
        console.log(`User Selected: ${userSelectedOption}`);
        console.log(`Options:`, testQuestion.options);
        
        // Simulate the marking logic from WriteExam component
        const isCorrect = correctAnswer === userSelectedOption;
        console.log(`Marking Result: ${isCorrect ? 'CORRECT' : 'WRONG'}`);
        
        // Test with wrong answer
        const wrongOptions = Object.keys(testQuestion.options).filter(key => key !== correctAnswer);
        if (wrongOptions.length > 0) {
          const wrongAnswer = wrongOptions[0];
          const isWrong = correctAnswer === wrongAnswer;
          console.log(`\nTesting wrong answer: ${wrongAnswer}`);
          console.log(`Marking Result: ${isWrong ? 'CORRECT' : 'WRONG'}`);
        }
      }
    } else {
      console.log('❌ Question generation failed:', result.error);
    }

    // 4. Test approval process if we have a generation record
    console.log('\n4️⃣ Testing approval process...');
    if (generations.length > 0) {
      const testGeneration = generations[0];
      console.log(`Testing approval for generation: ${testGeneration._id}`);

      if (testGeneration.generatedQuestions.length > 0) {
        // Create a test exam for approval
        const testExam = new Exam({
          name: 'Test Exam for AI Questions',
          duration: 60,
          category: 'Mathematics',
          level: 'primary',
          class: '3',
          totalMarks: 10,
          passingMarks: 5,
          questions: []
        });

        const savedExam = await testExam.save();
        console.log(`Created test exam: ${savedExam._id}`);

        // Update the generation record with exam ID
        testGeneration.examId = savedExam._id;
        await testGeneration.save();

        // Simulate approval process
        const questionToApprove = testGeneration.generatedQuestions[0];
        console.log('Approving question:', questionToApprove.generatedContent.name);

        // Create new question in database (simulating approval)
        const newQuestion = new Question({
          ...questionToApprove.generatedContent,
          exam: savedExam._id,
        });

        try {
          const savedQuestion = await newQuestion.save();
          console.log('✅ Question approved and saved:', savedQuestion._id);

          // Add to exam
          savedExam.questions.push(savedQuestion._id);
          await savedExam.save();
          console.log('✅ Question added to exam');

          // Update generation record
          questionToApprove.approved = true;
          questionToApprove.questionId = savedQuestion._id;
          await testGeneration.save();
          console.log('✅ Generation record updated');

          // Test the saved question structure
          console.log('\nSaved question structure:');
          console.log({
            name: savedQuestion.name,
            answerType: savedQuestion.answerType,
            correctOption: savedQuestion.correctOption,
            correctAnswer: savedQuestion.correctAnswer,
            options: savedQuestion.options,
            questionType: savedQuestion.questionType,
            isAIGenerated: savedQuestion.isAIGenerated
          });

        } catch (approvalError) {
          console.log('❌ Approval process failed:', approvalError.message);
        }
      }
    }
    
    // 6. Check for any exams with AI-generated questions
    console.log('\n6️⃣ Checking exams with AI-generated questions...');
    const examsWithAIQuestions = await Exam.aggregate([
      {
        $lookup: {
          from: 'questions',
          localField: 'questions',
          foreignField: '_id',
          as: 'questionDetails'
        }
      },
      {
        $match: {
          'questionDetails.isAIGenerated': true
        }
      },
      {
        $project: {
          name: 1,
          totalQuestions: { $size: '$questions' },
          aiQuestions: {
            $size: {
              $filter: {
                input: '$questionDetails',
                cond: { $eq: ['$$this.isAIGenerated', true] }
              }
            }
          }
        }
      }
    ]);
    
    console.log(`Found ${examsWithAIQuestions.length} exams with AI-generated questions`);
    examsWithAIQuestions.forEach(exam => {
      console.log(`- ${exam.name}: ${exam.aiQuestions}/${exam.totalQuestions} AI questions`);
    });
    
  } catch (error) {
    console.error('❌ Debug error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n✅ Disconnected from MongoDB');
  }
}

// Run the debug script
debugAIQuestions();
